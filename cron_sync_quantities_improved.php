<?php
/**
 * Improved AdvanceQB Quantity Sync Cron Job
 * Based on the working cron_sync_quantities12.php but with better logging and batch processing
 */

// Set the working directory to the OpenCart root
chdir(dirname(__FILE__));

// Set execution time limit and memory limit
set_time_limit(300); // 5 minutes
ini_set('memory_limit', '256M');

// Include OpenCart admin config
require_once('admin/config.php');

// Include OpenCart startup
require_once(DIR_SYSTEM . 'startup.php');

// Create registry manually
$registry = new Registry();

// Database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Config
$config = new Config();
$registry->set('config', $config);

// Load settings from database into config
$query = $db->query("SELECT * FROM " . DB_PREFIX . "setting WHERE store_id = '0'");
foreach ($query->rows as $result) {
    $config->set($result['key'], $result['value']);
}

// Log function for cron output
function cronLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
    flush();

    // Also log to file
    $logFile = DIR_LOGS . 'cron_quantity_sync.log';
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// Custom sync function that processes products in batches
function syncQuantitiesBatch($advqb, $db, $batchSize = 50) {
    $totalSynced = 0;
    $totalErrors = 0;
    
    try {
        // Get all linked products
        $linkedProducts = $db->query("SELECT * FROM " . DB_PREFIX . "advqb_product")->rows;
        $totalProducts = count($linkedProducts);
        
        cronLog("Found $totalProducts linked products to sync");
        
        if ($totalProducts == 0) {
            cronLog("No linked products found. Nothing to sync.");
            return 0;
        }
        
        // Process in batches
        $batches = array_chunk($linkedProducts, $batchSize);
        $batchNumber = 1;
        $totalBatches = count($batches);
        
        cronLog("Processing $totalProducts products in $totalBatches batches of $batchSize");
        
        foreach ($batches as $batch) {
            cronLog("Processing batch $batchNumber of $totalBatches...");
            
            foreach ($batch as $linkedProduct) {
                try {
                    $qbProductId = $linkedProduct['advqb_product_id'];
                    $ocProductId = $linkedProduct['oc_product_id'];
                    
                    // Get the current quantity from QuickBooks for this specific product
                    $qbProduct = $advqb->execute_curl("query", "GET", array(), "?query=" . urlencode("select * from Item WHERE Id = '" . $qbProductId . "'"));
                    
                    if (isset($qbProduct['QueryResponse']['Item'][0])) {
                        $oneProduct = $qbProduct['QueryResponse']['Item'][0];
                        
                        if (isset($oneProduct['Type']) && $oneProduct['Type'] == 'Inventory') {
                            // Update the quantity in OpenCart from QuickBooks
                            $qtyOnHand = isset($oneProduct['QtyOnHand']) ? $oneProduct['QtyOnHand'] : 0;
                            
                            $db->query("UPDATE " . DB_PREFIX . "product SET quantity = '" . (int)$qtyOnHand . "' WHERE product_id = '" . (int)$ocProductId . "'");
                            $totalSynced++;
                            
                            if ($totalSynced % 10 == 0) {
                                cronLog("Synced $totalSynced products so far...");
                            }
                        }
                    } else {
                        $totalErrors++;
                        if ($totalErrors <= 5) { // Only log first 5 errors to avoid spam
                            cronLog("Product ID $qbProductId not found in QuickBooks");
                        }
                    }
                    
                    // Small delay to avoid rate limiting
                    usleep(100000); // 0.1 second delay
                    
                } catch (Exception $e) {
                    $totalErrors++;
                    cronLog("Error syncing product $ocProductId: " . $e->getMessage());
                }
            }
            
            cronLog("Completed batch $batchNumber. Synced: $totalSynced, Errors: $totalErrors");
            
            // Longer delay between batches
            sleep(1);
            $batchNumber++;
        }
        
        cronLog("Batch processing completed. Total synced: $totalSynced, Total errors: $totalErrors");
        return $totalSynced;
        
    } catch (Exception $e) {
        cronLog("Error in batch sync: " . $e->getMessage());
        return $totalSynced;
    }
}

try {
    cronLog("Starting AdvanceQB quantity sync...");

    // Check if AdvanceQB module is enabled
    $moduleStatus = $db->query("SELECT value FROM " . DB_PREFIX . "setting WHERE `key` = 'module_opc_advqb_status'")->row;

    if (!$moduleStatus || !$moduleStatus['value']) {
        cronLog("ERROR: AdvanceQB module is not enabled. Exiting.");
        exit(1);
    }

    cronLog("AdvanceQB module is enabled. Proceeding with sync...");

    // Load the AdvanceQB library
    require_once(DIR_SYSTEM . 'library/advqb.php');

    // Initialize AdvanceQB
    $advqb = new Advqb($registry);
    cronLog("AdvanceQB library initialized");

    // Check QuickBooks connection first
    cronLog("Testing QuickBooks connection...");
    
    try {
        $testQuery = $advqb->execute_curl("query", "GET", array(), "?query=" . urlencode("select * from CompanyInfo"));
        if (isset($testQuery['QueryResponse'])) {
            cronLog("QuickBooks connection successful");
        } else {
            cronLog("WARNING: QuickBooks connection test returned unexpected response");
        }
    } catch (Exception $e) {
        cronLog("ERROR: QuickBooks connection failed: " . $e->getMessage());
        exit(1);
    }

    // Use our custom batch sync instead of the original method
    cronLog("Starting batch sync process...");
    $syncedCount = syncQuantitiesBatch($advqb, $db, 25); // Process 25 products at a time

    if ($syncedCount > 0) {
        cronLog("SUCCESS: Synced quantities for $syncedCount products.");
    } else {
        cronLog("INFO: No products were synced. This could mean no linked products or no quantity changes.");
    }

    cronLog("Quantity sync completed successfully.");

} catch (Exception $e) {
    cronLog("ERROR: " . $e->getMessage());
    cronLog("Stack trace: " . $e->getTraceAsString());
    exit(1);
}

cronLog("Cron job finished.");
exit(0);
?>
