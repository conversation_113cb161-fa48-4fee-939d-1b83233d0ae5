<?php
/**
 * Test to see what inventory items actually exist in QuickBooks
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
set_time_limit(120);

header('Content-Type: text/html; charset=utf-8');

echo "<h2>QuickBooks Inventory Test</h2>";
echo "<pre>";

function testLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
    flush();
}

try {
    testLog("=== Starting QuickBooks Inventory Test ===");
    
    // Define VERSION constant
    if (!defined('VERSION')) {
        define('VERSION', '*******');
    }
    
    // Get script directory
    $script_dir = dirname(__FILE__);
    
    // Include admin config
    require_once($script_dir . '/admin/config.php');
    require_once(DIR_SYSTEM . 'startup.php');
    
    // Initialize registry
    $registry = new Registry();
    
    // Config
    $config = new Config();
    $config->load('default');
    $config->load('admin');
    $registry->set('config', $config);
    
    // Log
    $log = new Log($config->get('error_filename') ?: 'error.log');
    $registry->set('log', $log);
    
    // Database
    $db = new DB(
        $config->get('db_engine') ?: DB_DRIVER,
        $config->get('db_hostname') ?: DB_HOSTNAME,
        $config->get('db_username') ?: DB_USERNAME,
        $config->get('db_password') ?: DB_PASSWORD,
        $config->get('db_database') ?: DB_DATABASE,
        $config->get('db_port') ?: DB_PORT
    );
    $registry->set('db', $db);
    
    // Other components
    $session = new Session($config->get('session_engine') ?: 'file', $registry);
    $registry->set('session', $session);
    
    $cache = new Cache($config->get('cache_engine') ?: 'file', $config->get('cache_expire') ?: 3600);
    $registry->set('cache', $cache);
    
    $event = new Event($registry);
    $registry->set('event', $event);
    
    $loader = new Loader($registry);
    $registry->set('load', $loader);
    
    testLog("Components initialized");
    
    // Initialize AdvQB library
    $registry->set('advqb', new Advqb($registry));
    $advqb = $registry->get('advqb');
    testLog("AdvQB library loaded");
    
    // Test 1: Get first 10 inventory items from QuickBooks
    testLog("Querying QuickBooks for inventory items...");
    
    $qb_inventory = $advqb->execute_curl("query", "GET", array(), "?query=" . urlencode("select * from Item WHERE Type = 'Inventory' maxResults 10"));
    
    if (isset($qb_inventory['QueryResponse']['Item'])) {
        $items = $qb_inventory['QueryResponse']['Item'];
        testLog("Found " . count($items) . " inventory items in QuickBooks:");
        
        foreach ($items as $index => $item) {
            $id = $item['Id'] ?? 'N/A';
            $name = $item['Name'] ?? 'N/A';
            $qty = $item['QtyOnHand'] ?? 'N/A';
            $sku = $item['Sku'] ?? 'N/A';
            
            testLog("  " . ($index + 1) . ". ID: $id, Name: $name, SKU: $sku, Qty: $qty");
            
            // Check if this QB product is linked in our database
            $linked_query = $db->query("SELECT oc_product_id FROM " . DB_PREFIX . "advqb_product WHERE advqb_product_id = '" . (int)$id . "'");
            if ($linked_query->num_rows > 0) {
                $oc_id = $linked_query->row['oc_product_id'];
                testLog("    ✓ Linked to OC Product ID: $oc_id");
            } else {
                testLog("    ✗ Not linked to any OC product");
            }
        }
    } else {
        testLog("No inventory items found in QuickBooks response");
        testLog("QB Response: " . print_r($qb_inventory, true));
    }
    
    // Test 2: Check if any of our linked products exist in QB
    testLog("\n=== Checking Linked Products ===");
    
    $linked_products_query = $db->query("SELECT * FROM " . DB_PREFIX . "advqb_product LIMIT 10");
    $found_count = 0;
    
    foreach ($linked_products_query->rows as $linked_product) {
        $qb_id = $linked_product['advqb_product_id'];
        $oc_id = $linked_product['oc_product_id'];
        
        testLog("Checking QB ID $qb_id (OC ID $oc_id)...");
        
        $qb_product = $advqb->execute_curl("query", "GET", array(), "?query=" . urlencode("select * from Item WHERE Id = '" . $qb_id . "'"));
        
        if (isset($qb_product['QueryResponse']['Item'][0])) {
            $item = $qb_product['QueryResponse']['Item'][0];
            $name = $item['Name'] ?? 'N/A';
            $qty = $item['QtyOnHand'] ?? 'N/A';
            testLog("  ✓ Found: $name (Qty: $qty)");
            $found_count++;
        } else {
            testLog("  ✗ Not found");
        }
        
        sleep(1); // Rate limiting
    }
    
    testLog("\n=== Results ===");
    testLog("Found $found_count out of 10 tested linked products in QuickBooks");
    
    if ($found_count > 0) {
        testLog("SUCCESS: Some linked products exist and can be synced!");
        testLog("The sync functionality is working correctly.");
        testLog("You may need to re-link products that are missing from QuickBooks.");
    } else {
        testLog("WARNING: None of the tested linked products were found in QuickBooks.");
        testLog("This could mean:");
        testLog("  - Products were deleted from QuickBooks");
        testLog("  - Product IDs changed in QuickBooks");
        testLog("  - The linking data is outdated");
        testLog("  - You may need to re-sync/re-link products");
    }
    
    // Test 3: Try the original sync method to see what it does
    testLog("\n=== Testing Original Sync Method ===");
    testLog("Running syncQuantityFromAdvQB() to see what happens...");
    
    $start_time = time();
    $count = $advqb->syncQuantityFromAdvQB();
    $end_time = time();
    $duration = $end_time - $start_time;
    
    testLog("Original sync completed in $duration seconds");
    testLog("Products updated: $count");
    
    if ($count > 0) {
        testLog("SUCCESS: The original sync method found and updated $count products!");
    } else {
        testLog("INFO: The original sync method didn't update any products.");
        testLog("This is expected if no linked products exist in QuickBooks.");
    }
    
    testLog("=== QuickBooks Inventory Test Completed ===");
    
} catch (Exception $e) {
    testLog("ERROR: " . $e->getMessage());
    testLog("File: " . $e->getFile() . " Line: " . $e->getLine());
}

echo "</pre>";
echo "<p><strong>Test completed.</strong></p>";
echo "<p>This test shows what inventory items actually exist in QuickBooks and whether your linked products are still valid.</p>";
?>
