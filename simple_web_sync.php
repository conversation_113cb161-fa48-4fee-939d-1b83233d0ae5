<?php
/**
 * Simple web version to test sync functionality
 * This mimics what the manual sync button does
 */

// Set up output buffering to prevent header issues
ob_start();

// Set error reporting but don't display errors immediately
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Define VERSION constant (required by OpenCart)
if (!defined('VERSION')) {
    define('VERSION', '3.0.3.8');
}

echo "<h2>Simple Sync Test</h2>";
echo "<pre>";

try {
    // Include the admin config
    require_once('admin/config.php');

    // Include startup
    require_once(DIR_SYSTEM . 'startup.php');

    // Start the admin application (similar to admin/index.php)
    start('admin');
    
    // At this point, we should have access to the registry and all components
    // Get the registry from the global scope (this is how OpenCart works)
    global $registry;
    
    if (!$registry) {
        throw new Exception("Registry not available");
    }
    
    echo "Registry available\n";
    
    // Set up the AdvQB library
    $registry->set('advqb', new Advqb($registry));
    $advqb = $registry->get('advqb');
    
    echo "AdvQB library loaded\n";
    
    // Check module status
    $config = $registry->get('config');
    $db = $registry->get('db');
    
    $module_status_query = $db->query("SELECT value FROM " . DB_PREFIX . "setting WHERE `key` = 'module_opc_advqb_status'");
    if ($module_status_query->num_rows > 0) {
        $module_status = (bool)$module_status_query->row['value'];
        echo "Module status: " . ($module_status ? "enabled" : "disabled") . "\n";
    }
    
    // Check linked products
    $linked_products_query = $db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "advqb_product");
    $linked_count = $linked_products_query->row['count'];
    echo "Linked products: $linked_count\n";
    
    if ($linked_count > 0) {
        echo "Starting sync...\n";
        
        // Call the sync function (same as the manual button)
        $count = $advqb->syncQuantityFromAdvQB();
        
        echo "Sync completed. Products updated: $count\n";
        echo "SUCCESS: Sync test completed successfully!\n";
    } else {
        echo "No linked products found. Cannot perform sync.\n";
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>";
echo "<p>Test completed. If you see 'SUCCESS' above, the sync is working!</p>";

// Flush the output buffer
ob_end_flush();
?>
