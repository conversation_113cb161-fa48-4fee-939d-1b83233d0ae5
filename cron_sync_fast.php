<?php
/**
 * Fast AdvanceQB Quantity Sync Cron Job
 * Uses the same method as the manual sync button for maximum speed
 */

// Set the working directory to the OpenCart root
chdir(dirname(__FILE__));

// Set execution time limit for CLI (no limit for cron jobs)
if (php_sapi_name() === 'cli') {
    set_time_limit(0); // No time limit for CLI
} else {
    set_time_limit(300); // 5 minutes for web
}

ini_set('memory_limit', '512M');

// Include OpenCart admin config
require_once('admin/config.php');

// Include OpenCart startup
require_once(DIR_SYSTEM . 'startup.php');

// Create registry manually (same as working version)
$registry = new Registry();

// Database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Config
$config = new Config();
$registry->set('config', $config);

// Load settings from database into config
$query = $db->query("SELECT * FROM " . DB_PREFIX . "setting WHERE store_id = '0'");
foreach ($query->rows as $result) {
    $config->set($result['key'], $result['value']);
}

// Log function for cron output
function cronLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
    flush();

    // Also log to file
    $logFile = DIR_LOGS . 'cron_quantity_sync.log';
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

try {
    cronLog("Starting AdvanceQB quantity sync (fast method)...");

    // Check if AdvanceQB module is enabled
    $moduleStatus = $db->query("SELECT value FROM " . DB_PREFIX . "setting WHERE `key` = 'module_opc_advqb_status'")->row;

    if (!$moduleStatus || !$moduleStatus['value']) {
        cronLog("ERROR: AdvanceQB module is not enabled. Exiting.");
        exit(1);
    }

    cronLog("AdvanceQB module is enabled. Proceeding with sync...");

    // Load the AdvanceQB library
    require_once(DIR_SYSTEM . 'library/advqb.php');

    // Initialize AdvanceQB (same as manual sync)
    $advqb = new Advqb($registry);
    cronLog("AdvanceQB library initialized");

    // Check linked products count
    $linkedCount = $db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "advqb_product")->row['count'];
    cronLog("Found $linkedCount linked products");

    if ($linkedCount == 0) {
        cronLog("No linked products found. Nothing to sync.");
        exit(0);
    }

    // Use the EXACT same method as the manual sync button
    cronLog("Starting sync using original syncQuantityFromAdvQB method...");
    
    $startTime = time();
    
    // This is the exact same call as the manual sync button
    $count = $advqb->syncQuantityFromAdvQB();
    
    $endTime = time();
    $duration = $endTime - $startTime;
    
    cronLog("Sync completed in $duration seconds");

    if ($count > 0) {
        cronLog("SUCCESS: Synced quantities for $count products.");
    } else {
        cronLog("INFO: No products were synced. This could mean:");
        cronLog("  - No quantity changes detected");
        cronLog("  - QuickBooks authentication issues");
        cronLog("  - All products already have correct quantities");
    }

    cronLog("Quantity sync completed successfully.");

} catch (Exception $e) {
    cronLog("ERROR: " . $e->getMessage());
    cronLog("Stack trace: " . $e->getTraceAsString());
    exit(1);
}

cronLog("Cron job finished.");
exit(0);
?>
