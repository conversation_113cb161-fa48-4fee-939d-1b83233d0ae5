<?php
/**
 * Test Script for AdvancedQB Cron Setup
 * 
 * This script helps verify that your environment is properly configured
 * for running the AdvancedQB quantity sync cron job.
 * 
 * Usage: php test_cron_setup.php
 */

echo "=== AdvancedQB Cron Setup Test ===\n\n";

// Test 1: Check if we can find the config file
echo "1. Testing configuration file access...\n";
$script_dir = dirname(__FILE__);
$config_path = $script_dir . '/admin/config.php';

if (is_file($config_path)) {
    echo "   ✓ Found admin/config.php\n";
    require_once($config_path);
} else {
    echo "   ✗ Could not find admin/config.php\n";
    echo "   Please ensure this script is in the root directory of your OpenCart installation.\n";
    exit(1);
}

// Test 2: Check required constants
echo "\n2. Testing configuration constants...\n";
$required_constants = ['DIR_SYSTEM', 'DB_HOSTNAME', 'DB_USERNAME', 'DB_PASSWORD', 'DB_DATABASE', 'DB_PREFIX'];
$missing_constants = [];

foreach ($required_constants as $constant) {
    if (defined($constant)) {
        echo "   ✓ $constant is defined\n";
    } else {
        echo "   ✗ $constant is missing\n";
        $missing_constants[] = $constant;
    }
}

if (!empty($missing_constants)) {
    echo "   Please check your admin/config.php file.\n";
    exit(1);
}

// Test 3: Check if system files exist
echo "\n3. Testing system file access...\n";
$required_files = [
    DIR_SYSTEM . 'startup.php',
    DIR_SYSTEM . 'library/advqb.php',
    DIR_SYSTEM . 'engine/registry.php',
    DIR_SYSTEM . 'library/config.php',
    DIR_SYSTEM . 'library/db.php'
];

foreach ($required_files as $file) {
    if (is_file($file)) {
        echo "   ✓ Found " . basename($file) . "\n";
    } else {
        echo "   ✗ Missing " . $file . "\n";
    }
}

// Test 4: Try to include startup
echo "\n4. Testing system initialization...\n";
try {
    require_once(DIR_SYSTEM . 'startup.php');
    echo "   ✓ Successfully loaded startup.php\n";
} catch (Exception $e) {
    echo "   ✗ Error loading startup.php: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 5: Test database connection
echo "\n5. Testing database connection...\n";
try {
    $registry = new Registry();
    
    $config = new Config();
    $config->load('default');
    $config->load('admin');
    $registry->set('config', $config);
    
    $db = new DB($config->get('db_engine'), $config->get('db_hostname'), $config->get('db_username'), $config->get('db_password'), $config->get('db_database'), $config->get('db_port'));
    $registry->set('db', $db);
    
    // Test a simple query
    $result = $db->query("SELECT 1 as test");
    if ($result && isset($result->row['test']) && $result->row['test'] == 1) {
        echo "   ✓ Database connection successful\n";
    } else {
        echo "   ✗ Database connection failed - query test failed\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "   ✗ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 6: Check AdvancedQB module status
echo "\n6. Testing AdvancedQB module configuration...\n";
try {
    $module_status = $config->get('module_opc_advqb_status');
    if ($module_status) {
        echo "   ✓ AdvancedQB module is enabled\n";
    } else {
        echo "   ⚠ AdvancedQB module is disabled\n";
        echo "   Please enable the module in your OpenCart admin panel.\n";
    }
    
    // Check for required AdvQB settings
    $advqb_settings = [
        'module_opc_advqb_client_key' => 'Client Key',
        'module_opc_advqb_client_secret' => 'Client Secret',
        'module_opc_advqb_access_token' => 'Access Token',
        'module_opc_advqb_refresh_token' => 'Refresh Token',
        'module_opc_advqb_realmid' => 'Realm ID'
    ];
    
    foreach ($advqb_settings as $setting => $name) {
        $value = $config->get($setting);
        if ($value) {
            echo "   ✓ $name is configured\n";
        } else {
            echo "   ⚠ $name is not configured\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ✗ Error checking module configuration: " . $e->getMessage() . "\n";
}

// Test 7: Check if AdvQB library can be loaded
echo "\n7. Testing AdvQB library...\n";
try {
    $log = new Log($config->get('error_filename'));
    $registry->set('log', $log);
    
    $session = new Session($config->get('session_engine'), $registry);
    $registry->set('session', $session);
    
    $cache = new Cache($config->get('cache_engine'), $config->get('cache_expire'));
    $registry->set('cache', $cache);
    
    $event = new Event($registry);
    $registry->set('event', $event);
    
    $loader = new Loader($registry);
    $registry->set('load', $loader);
    
    $registry->set('advqb', new Advqb($registry));
    $advqb = $registry->get('advqb');
    
    echo "   ✓ AdvQB library loaded successfully\n";
    
    // Check if we can access the sync method
    if (method_exists($advqb, 'syncQuantityFromAdvQB')) {
        echo "   ✓ syncQuantityFromAdvQB method is available\n";
    } else {
        echo "   ✗ syncQuantityFromAdvQB method not found\n";
    }
    
} catch (Exception $e) {
    echo "   ✗ Error loading AdvQB library: " . $e->getMessage() . "\n";
}

// Test 8: Check linked products
echo "\n8. Checking for linked products...\n";
try {
    $linked_products_query = $db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "advqb_product");
    $linked_count = $linked_products_query->row['count'];
    
    if ($linked_count > 0) {
        echo "   ✓ Found $linked_count linked products in the database\n";
    } else {
        echo "   ⚠ No linked products found\n";
        echo "   You may need to sync products from QuickBooks first.\n";
    }
} catch (Exception $e) {
    echo "   ✗ Error checking linked products: " . $e->getMessage() . "\n";
}

echo "\n=== Test Summary ===\n";
echo "If all tests show ✓ (checkmarks), your environment is ready for the cron job.\n";
echo "If you see ⚠ (warnings), the cron job may work but with limitations.\n";
echo "If you see ✗ (errors), please fix those issues before setting up the cron job.\n\n";

echo "Next steps:\n";
echo "1. Run 'php cron_sync_quantity.php' to test the actual sync\n";
echo "2. Set up your cron job as described in CRON_SETUP_INSTRUCTIONS.md\n";
echo "3. Monitor the first few runs to ensure everything works correctly\n\n";

echo "=== Test Complete ===\n";
?>
