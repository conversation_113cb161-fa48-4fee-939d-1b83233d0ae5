# AdvancedQB Product Quantity Sync - Cron Job Setup

This document provides instructions for setting up an automated cron job to sync product quantities from QuickBooks to your OpenCart store every 3 hours.

## Files Created

1. **cron_sync_quantity.php** - The main cron script that performs the quantity sync
2. **CRON_SETUP_INSTRUCTIONS.md** - This instruction file

## Prerequisites

Before setting up the cron job, ensure that:

1. Your AdvancedQB module is properly configured and working
2. You can manually sync quantities using the admin panel button
3. Your QuickBooks integration is connected and authenticated
4. PHP CLI is available on your server

## Installation Steps

### Step 1: Upload the Script

1. Upload `cron_sync_quantity.php` to the root directory of your OpenCart installation
2. The file should be in the same directory as your `admin` and `catalog` folders

### Step 2: Test the Script

Before setting up the cron job, test the script manually:

```bash
# Navigate to your OpenCart root directory
cd /path/to/your/opencart

# Run the script manually
php cron_sync_quantity.php
```

You should see output like:
```
[2024-01-01 12:00:00] === AdvancedQB Product Quantity Sync Started ===
[2024-01-01 12:00:01] Starting product quantity sync from AdvancedQB...
[2024-01-01 12:00:05] Successfully synced quantities for 15 products.
[2024-01-01 12:00:05] === AdvancedQB Product Quantity Sync Completed Successfully ===
```

### Step 3: Set Up the Cron Job

#### For cPanel Users:

1. Log into your cPanel
2. Find and click on "Cron Jobs"
3. Add a new cron job with these settings:
   - **Minute**: 0
   - **Hour**: */3 (every 3 hours)
   - **Day**: * (every day)
   - **Month**: * (every month)
   - **Weekday**: * (every day of week)
   - **Command**: `/usr/bin/php /home/<USER>/public_html/cron_sync_quantity.php`

#### For Command Line Users:

1. Open your crontab:
   ```bash
   crontab -e
   ```

2. Add this line to run every 3 hours:
   ```bash
   0 */3 * * * /usr/bin/php /path/to/your/opencart/cron_sync_quantity.php
   ```

#### Alternative Schedules:

- **Every hour**: `0 * * * * /usr/bin/php /path/to/your/opencart/cron_sync_quantity.php`
- **Every 6 hours**: `0 */6 * * * /usr/bin/php /path/to/your/opencart/cron_sync_quantity.php`
- **Daily at 2 AM**: `0 2 * * * /usr/bin/php /path/to/your/opencart/cron_sync_quantity.php`

## Important Notes

### Path Configuration

Make sure to update the paths in the cron command to match your server setup:

- Replace `/usr/bin/php` with the correct path to PHP on your server
- Replace `/path/to/your/opencart/` with the actual path to your OpenCart installation

### Common PHP Paths:
- cPanel/shared hosting: `/usr/bin/php` or `/usr/local/bin/php`
- Some servers: `/opt/php/bin/php`

To find your PHP path, run: `which php`

### Permissions

Ensure the script has proper permissions:
```bash
chmod 755 cron_sync_quantity.php
```

### Logging

The script will output logs to:
1. Standard output (visible in cron job logs)
2. PHP error log (for errors)

To redirect output to a custom log file, modify your cron command:
```bash
0 */3 * * * /usr/bin/php /path/to/your/opencart/cron_sync_quantity.php >> /path/to/logs/quantity_sync.log 2>&1
```

## Troubleshooting

### Common Issues:

1. **"Could not find admin/config.php"**
   - Ensure the script is in the correct directory
   - Check file permissions

2. **"AdvancedQB module is not enabled"**
   - Enable the module in your OpenCart admin panel
   - Verify module configuration

3. **Database connection errors**
   - Check database credentials in admin/config.php
   - Ensure database server is accessible

4. **QuickBooks authentication errors**
   - Re-authenticate your QuickBooks connection
   - Check if access tokens need refreshing

### Testing Tips:

1. Run the script manually first to ensure it works
2. Check your server's cron logs for execution confirmation
3. Monitor your product quantities to verify sync is working
4. Check OpenCart error logs for any issues

## Security Considerations

1. Keep the script in a secure location
2. Ensure proper file permissions (755 for the script)
3. Monitor cron job logs for any suspicious activity
4. Regularly check that the sync is working as expected

## Support

If you encounter issues:

1. Check the script output for error messages
2. Verify your AdvancedQB module configuration
3. Test manual sync from the admin panel
4. Check server logs for additional error details

Remember to test thoroughly before relying on the automated sync in a production environment.
