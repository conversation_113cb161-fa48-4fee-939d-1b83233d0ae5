<?php
/**
 * <PERSON><PERSON> Job Script for Syncing Product Quantities from AdvancedQB
 * 
 * This script can be run via cron job to automatically sync product quantities
 * from QuickBooks to OpenCart every 3 hours.
 * 
 * Usage: php cron_sync_quantity.php
 * 
 * Cron job example (every 3 hours):
 * 0 */3 * * * /usr/bin/php /path/to/your/opencart/cron_sync_quantity.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Get the directory of this script
$script_dir = dirname(__FILE__);

// Configuration - Update these paths to match your server setup
if (is_file($script_dir . '/admin/config.php')) {
    require_once($script_dir . '/admin/config.php');
} else {
    die("Error: Could not find admin/config.php file. Please ensure this script is in the root directory of your OpenCart installation.\n");
}

// Check if required constants are defined
if (!defined('DIR_SYSTEM') || !defined('DB_HOSTNAME') || !defined('DB_USERNAME') || !defined('DB_PASSWORD') || !defined('DB_DATABASE')) {
    die("Error: Required configuration constants not found. Please check your admin/config.php file.\n");
}

// Include required files
require_once(DIR_SYSTEM . 'startup.php');

// Initialize the registry
$registry = new Registry();

// Config
$config = new Config();
$config->load('default');
$config->load('admin');
$registry->set('config', $config);

// Log
$log = new Log($config->get('error_filename'));
$registry->set('log', $log);

// Set timezone
if ($config->get('date_timezone')) {
    date_default_timezone_set($config->get('date_timezone'));
} else {
    date_default_timezone_set('UTC');
}

// Database
$db = new DB($config->get('db_engine'), $config->get('db_hostname'), $config->get('db_username'), $config->get('db_password'), $config->get('db_database'), $config->get('db_port'));
$registry->set('db', $db);

// Sync PHP and DB time zones
$db->query("SET time_zone = '" . $db->escape(date('P')) . "'");

// Session (minimal setup for cron)
$session = new Session($config->get('session_engine'), $registry);
$registry->set('session', $session);

// Cache
$registry->set('cache', new Cache($config->get('cache_engine'), $config->get('cache_expire')));

// Event
$event = new Event($registry);
$registry->set('event', $event);

// Loader
$loader = new Loader($registry);
$registry->set('load', $loader);

// Function to log messages with timestamp
function logMessage($message, $isError = false) {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message\n";
    
    if ($isError) {
        error_log($logMessage);
        echo "ERROR: $logMessage";
    } else {
        echo $logMessage;
    }
}

// Main sync function
function syncProductQuantities($registry) {
    try {
        // Check if the AdvancedQB module is enabled
        $config = $registry->get('config');
        
        if (!$config->get('module_opc_advqb_status')) {
            logMessage("AdvancedQB module is not enabled. Sync aborted.", true);
            return false;
        }
        
        // Initialize the AdvQB library
        $registry->set('advqb', new Advqb($registry));
        $advqb = $registry->get('advqb');
        
        logMessage("Starting product quantity sync from AdvancedQB...");
        
        // Call the sync function
        $count = $advqb->syncQuantityFromAdvQB();
        
        if ($count > 0) {
            logMessage("Successfully synced quantities for $count products.");
        } else {
            logMessage("No products were synced. This could mean no linked products found or no quantity changes detected.");
        }
        
        return true;
        
    } catch (Exception $e) {
        logMessage("Error during sync: " . $e->getMessage(), true);
        return false;
    }
}

// Main execution
try {
    logMessage("=== AdvancedQB Product Quantity Sync Started ===");
    
    // Perform the sync
    $success = syncProductQuantities($registry);
    
    if ($success) {
        logMessage("=== AdvancedQB Product Quantity Sync Completed Successfully ===");
        exit(0); // Success exit code
    } else {
        logMessage("=== AdvancedQB Product Quantity Sync Failed ===", true);
        exit(1); // Error exit code
    }
    
} catch (Exception $e) {
    logMessage("Fatal error: " . $e->getMessage(), true);
    exit(1); // Error exit code
}
?>
