<?php
// Very basic test to check PHP environment
echo "<h2>Basic PHP Test</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>Current Directory: " . __DIR__ . "</p>";
echo "<p>Script Path: " . __FILE__ . "</p>";

// Test if config file exists
$config_path = __DIR__ . '/admin/config.php';
echo "<p>Config file exists: " . (file_exists($config_path) ? "YES" : "NO") . "</p>";
echo "<p>Config path: $config_path</p>";

if (file_exists($config_path)) {
    echo "<p>Attempting to include config...</p>";
    try {
        require_once($config_path);
        echo "<p>Config included successfully</p>";
        echo "<p>DIR_SYSTEM defined: " . (defined('DIR_SYSTEM') ? "YES - " . DIR_SYSTEM : "NO") . "</p>";
        echo "<p>DB_HOSTNAME defined: " . (defined('DB_HOSTNAME') ? "YES" : "NO") . "</p>";
    } catch (Exception $e) {
        echo "<p>Error including config: " . $e->getMessage() . "</p>";
    }
}

echo "<p>Test completed</p>";
?>
