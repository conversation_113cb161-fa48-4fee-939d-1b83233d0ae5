<?php
/**
 * Optimized AdvanceQB Quantity Sync Cron Job
 * Only syncs products that are ENABLED in OpenCart for better performance
 */

// Set the working directory to the OpenCart root
chdir(dirname(__FILE__));

// Set execution time limit for CLI (no limit for cron jobs)
if (php_sapi_name() === 'cli') {
    set_time_limit(0); // No time limit for CLI
} else {
    set_time_limit(300); // 5 minutes for web
}

ini_set('memory_limit', '512M');

// Include OpenCart admin config
require_once('admin/config.php');

// Include OpenCart startup
require_once(DIR_SYSTEM . 'startup.php');

// Create registry manually
$registry = new Registry();

// Database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Config
$config = new Config();
$registry->set('config', $config);

// Load settings from database into config
$query = $db->query("SELECT * FROM " . DB_PREFIX . "setting WHERE store_id = '0'");
foreach ($query->rows as $result) {
    $config->set($result['key'], $result['value']);
}

// Log function for cron output
function cronLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
    flush();

    // Also log to file
    $logFile = DIR_LOGS . 'cron_quantity_sync.log';
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

// Custom sync function that only processes ENABLED products
function syncEnabledProductsOnly($advqb, $db) {
    $count = 0;
    
    try {
        // Get only linked products that are ENABLED in OpenCart
        $linkedProducts = $db->query("
            SELECT ap.*, p.status, p.quantity as current_qty, pd.name 
            FROM " . DB_PREFIX . "advqb_product ap 
            LEFT JOIN " . DB_PREFIX . "product p ON (ap.oc_product_id = p.product_id) 
            LEFT JOIN " . DB_PREFIX . "product_description pd ON (p.product_id = pd.product_id AND pd.language_id = 1)
            WHERE p.status = 1
        ")->rows;
        
        $totalProducts = count($linkedProducts);
        cronLog("Found $totalProducts ENABLED linked products to sync");
        
        if ($totalProducts == 0) {
            cronLog("No enabled linked products found. Nothing to sync.");
            return 0;
        }
        
        // Show some sample products
        cronLog("Sample enabled products to sync:");
        for ($i = 0; $i < min(3, $totalProducts); $i++) {
            $product = $linkedProducts[$i];
            $name = $product['name'] ?: 'N/A';
            cronLog("  - Product ID {$product['oc_product_id']}: $name (Current Qty: {$product['current_qty']})");
        }
        
        foreach ($linkedProducts as $linkedProduct) {
            try {
                // Get the current quantity from QuickBooks for this specific product
                $qbProduct = $advqb->execute_curl("query", "GET", array(), "?query=" . urlencode("select * from Item WHERE Id = '" . $linkedProduct['advqb_product_id'] . "'"));
                
                if (isset($qbProduct['QueryResponse']['Item'][0])) {
                    $oneProduct = $qbProduct['QueryResponse']['Item'][0];
                    
                    if (isset($oneProduct['Type']) && $oneProduct['Type'] == 'Inventory') {
                        // Update the quantity in OpenCart from QuickBooks
                        $qtyOnHand = isset($oneProduct['QtyOnHand']) ? $oneProduct['QtyOnHand'] : 0;
                        $currentQty = $linkedProduct['current_qty'];
                        
                        // Only update if quantity has changed
                        if ($qtyOnHand != $currentQty) {
                            $db->query("UPDATE " . DB_PREFIX . "product SET quantity = '" . (int)$qtyOnHand . "' WHERE product_id = '" . (int)$linkedProduct['oc_product_id'] . "'");
                            $count++;
                            
                            // Log significant changes
                            if (abs($qtyOnHand - $currentQty) > 10) {
                                $productName = $linkedProduct['name'] ?: 'Product ID ' . $linkedProduct['oc_product_id'];
                                cronLog("  Updated $productName: $currentQty → $qtyOnHand");
                            }
                        }
                        
                        // Progress indicator
                        if ($count > 0 && $count % 50 == 0) {
                            cronLog("Progress: Updated $count products so far...");
                        }
                    }
                } else {
                    // Product not found in QB - could log this if needed
                    // cronLog("QB Product ID {$linkedProduct['advqb_product_id']} not found");
                }
                
            } catch (Exception $e) {
                cronLog("Error syncing product {$linkedProduct['oc_product_id']}: " . $e->getMessage());
            }
        }
        
    } catch (Exception $e) {
        cronLog("Error in enabled products sync: " . $e->getMessage());
    }
    
    return $count;
}

try {
    cronLog("Starting AdvanceQB quantity sync (enabled products only)...");

    // Check if AdvanceQB module is enabled
    $moduleStatus = $db->query("SELECT value FROM " . DB_PREFIX . "setting WHERE `key` = 'module_opc_advqb_status'")->row;

    if (!$moduleStatus || !$moduleStatus['value']) {
        cronLog("ERROR: AdvanceQB module is not enabled. Exiting.");
        exit(1);
    }

    cronLog("AdvanceQB module is enabled. Proceeding with sync...");

    // Load the AdvanceQB library
    require_once(DIR_SYSTEM . 'library/advqb.php');

    // Initialize AdvanceQB
    $advqb = new Advqb($registry);
    cronLog("AdvanceQB library initialized");

    // Check total vs enabled products
    $totalLinked = $db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "advqb_product")->row['count'];
    $enabledLinked = $db->query("
        SELECT COUNT(*) as count 
        FROM " . DB_PREFIX . "advqb_product ap 
        LEFT JOIN " . DB_PREFIX . "product p ON (ap.oc_product_id = p.product_id) 
        WHERE p.status = 1
    ")->row['count'];
    
    cronLog("Total linked products: $totalLinked");
    cronLog("Enabled linked products: $enabledLinked");
    cronLog("Skipping " . ($totalLinked - $enabledLinked) . " disabled products");

    if ($enabledLinked == 0) {
        cronLog("No enabled linked products found. Nothing to sync.");
        exit(0);
    }

    // Use our optimized sync for enabled products only
    cronLog("Starting sync for enabled products only...");
    
    $startTime = time();
    $count = syncEnabledProductsOnly($advqb, $db);
    $endTime = time();
    $duration = $endTime - $startTime;
    
    cronLog("Sync completed in $duration seconds");

    if ($count > 0) {
        cronLog("SUCCESS: Updated quantities for $count enabled products.");
    } else {
        cronLog("INFO: No product quantities were changed.");
        cronLog("This could mean:");
        cronLog("  - All enabled products already have correct quantities");
        cronLog("  - No quantity changes detected in QuickBooks");
    }

    cronLog("Quantity sync completed successfully.");

} catch (Exception $e) {
    cronLog("ERROR: " . $e->getMessage());
    cronLog("Stack trace: " . $e->getTraceAsString());
    exit(1);
}

cronLog("Cron job finished.");
exit(0);
?>
