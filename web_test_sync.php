<?php
/**
 * Web-accessible test version for AdvancedQB sync
 * This is a simplified version to test the sync functionality via web browser
 */

// Set error reporting and display
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Set content type for web display
header('Content-Type: text/html; charset=utf-8');

echo "<h2>AdvancedQB Sync Test</h2>";
echo "<pre>";

function webLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message<br>\n";
    flush();
}

try {
    webLog("=== Starting AdvancedQB Sync Test ===");
    
    // Get the directory of this script
    $script_dir = dirname(__FILE__);
    webLog("Script directory: $script_dir");
    
    // Check for config file
    $config_path = $script_dir . '/admin/config.php';
    if (!is_file($config_path)) {
        throw new Exception("Could not find admin/config.php file");
    }
    webLog("Found config file: $config_path");
    
    // Include config
    require_once($config_path);
    webLog("Config loaded successfully");
    
    // Check required constants
    $required_constants = ['DIR_SYSTEM', 'DB_HOSTNAME', 'DB_USERNAME', 'DB_PASSWORD', 'DB_DATABASE', 'DB_PREFIX'];
    foreach ($required_constants as $constant) {
        if (!defined($constant)) {
            throw new Exception("Required constant $constant is not defined");
        }
    }
    webLog("All required constants are defined");
    
    // Include startup
    if (!is_file(DIR_SYSTEM . 'startup.php')) {
        throw new Exception("Could not find startup.php at: " . DIR_SYSTEM . 'startup.php');
    }
    
    require_once(DIR_SYSTEM . 'startup.php');
    webLog("Startup loaded successfully");
    
    // Initialize basic components
    $registry = new Registry();
    webLog("Registry created");
    
    $config = new Config();
    $config->load('default');
    $config->load('admin');
    $registry->set('config', $config);
    webLog("Config initialized");
    
    // Database connection
    $db = new DB(
        $config->get('db_engine') ?: DB_DRIVER,
        $config->get('db_hostname') ?: DB_HOSTNAME,
        $config->get('db_username') ?: DB_USERNAME,
        $config->get('db_password') ?: DB_PASSWORD,
        $config->get('db_database') ?: DB_DATABASE,
        $config->get('db_port') ?: DB_PORT
    );
    $registry->set('db', $db);
    webLog("Database connected");
    
    // Test database query
    $test_query = $db->query("SELECT 1 as test");
    if (!$test_query || !isset($test_query->row['test'])) {
        throw new Exception("Database test query failed");
    }
    webLog("Database test query successful");
    
    // Initialize other required components
    $log = new Log($config->get('error_filename') ?: 'error.log');
    $registry->set('log', $log);
    
    $session = new Session($config->get('session_engine') ?: 'file', $registry);
    $registry->set('session', $session);
    
    $cache = new Cache($config->get('cache_engine') ?: 'file', $config->get('cache_expire') ?: 3600);
    $registry->set('cache', $cache);
    
    $event = new Event($registry);
    $registry->set('event', $event);
    
    $loader = new Loader($registry);
    $registry->set('load', $loader);
    
    webLog("All components initialized");
    
    // Check module status from database
    $module_status_query = $db->query("SELECT value FROM " . DB_PREFIX . "setting WHERE `key` = 'module_opc_advqb_status'");
    if ($module_status_query->num_rows > 0) {
        $module_status = (bool)$module_status_query->row['value'];
        webLog("Module status from database: " . ($module_status ? "enabled" : "disabled"));
    } else {
        webLog("Module status not found in database");
        $module_status = false;
    }
    
    // Check for linked products
    $linked_products_query = $db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "advqb_product");
    $linked_count = $linked_products_query->row['count'];
    webLog("Found $linked_count linked products");
    
    // Check if AdvQB library exists
    $advqb_lib_path = DIR_SYSTEM . 'library/advqb.php';
    if (!is_file($advqb_lib_path)) {
        throw new Exception("AdvQB library not found at: $advqb_lib_path");
    }
    webLog("AdvQB library found");
    
    // Try to load AdvQB library
    $registry->set('advqb', new Advqb($registry));
    $advqb = $registry->get('advqb');
    webLog("AdvQB library loaded successfully");
    
    // Check if sync method exists
    if (!method_exists($advqb, 'syncQuantityFromAdvQB')) {
        throw new Exception("syncQuantityFromAdvQB method not found in AdvQB class");
    }
    webLog("syncQuantityFromAdvQB method found");
    
    // Perform the actual sync
    webLog("Starting quantity sync...");
    $count = $advqb->syncQuantityFromAdvQB();
    webLog("Sync completed. Products updated: $count");
    
    webLog("=== Sync Test Completed Successfully ===");
    
} catch (Exception $e) {
    webLog("ERROR: " . $e->getMessage());
    webLog("Stack trace: " . $e->getTraceAsString());
} catch (Error $e) {
    webLog("FATAL ERROR: " . $e->getMessage());
    webLog("Stack trace: " . $e->getTraceAsString());
}

echo "</pre>";
echo "<p><strong>Test completed. Check the output above for results.</strong></p>";
?>
