<?php
/**
 * AdvanceQB Quantity Sync Cron Job
 *
 * This script syncs product quantities from QuickBooks to OpenCart
 * Run this script via cron to automatically update website quantities
 *
 * Usage: php cron_sync_quantities.php
 */

// Set the working directory to the OpenCart root
chdir(dirname(__FILE__));

// Include OpenCart admin config (since this is where the DB config is)
require_once('admin/config.php');

// Include OpenCart startup
require_once(DIR_SYSTEM . 'startup.php');

// Create registry manually
$registry = new Registry();

// Database
$db = new DB(DB_DRIVER, DB_HOSTNAME, DB_USERNAME, DB_PASSWORD, DB_DATABASE, DB_PORT);
$registry->set('db', $db);

// Config
$config = new Config();
$registry->set('config', $config);

// Load settings from database into config
$query = $db->query("SELECT * FROM " . DB_PREFIX . "setting WHERE store_id = '0'");
foreach ($query->rows as $result) {
    $config->set($result['key'], $result['value']);
}

// Log function for cron output
function cronLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";

    // Also log to file
    $logFile = DIR_LOGS . 'cron_quantity_sync.log';
    file_put_contents($logFile, "[$timestamp] $message\n", FILE_APPEND | LOCK_EX);
}

try {
    cronLog("Starting AdvanceQB quantity sync...");

    // Check if AdvanceQB module is enabled
    $moduleStatus = $db->query("SELECT value FROM " . DB_PREFIX . "setting WHERE `key` = 'module_opc_advqb_status'")->row;

    if (!$moduleStatus || !$moduleStatus['value']) {
        cronLog("ERROR: AdvanceQB module is not enabled. Exiting.");
        exit(1);
    }

    cronLog("AdvanceQB module is enabled. Proceeding with sync...");

    // Load the AdvanceQB library
    require_once(DIR_SYSTEM . 'library/advqb.php');

    // Initialize AdvanceQB
    $advqb = new Advqb($registry);

    // Perform the quantity sync
    $syncedCount = $advqb->syncQuantityFromAdvQB();

    if ($syncedCount > 0) {
        cronLog("SUCCESS: Synced quantities for $syncedCount products.");
    } else {
        cronLog("INFO: No products were synced. This could mean no linked products or no quantity changes.");
    }

    cronLog("Quantity sync completed successfully.");

} catch (Exception $e) {
    cronLog("ERROR: " . $e->getMessage());
    cronLog("Stack trace: " . $e->getTraceAsString());
    exit(1);
}

cronLog("Cron job finished.");
exit(0);
?>
