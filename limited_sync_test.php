<?php
/**
 * Limited sync test - sync only a few products to test functionality
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set maximum execution time to 120 seconds
set_time_limit(120);

// Set content type
header('Content-Type: text/html; charset=utf-8');

echo "<h2>Limited Sync Test (5 Products)</h2>";
echo "<pre>";

function testLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message\n";
    flush();
}

try {
    testLog("=== Starting Limited Sync Test ===");
    
    // Define VERSION constant
    if (!defined('VERSION')) {
        define('VERSION', '*******');
    }
    
    // Get script directory
    $script_dir = dirname(__FILE__);
    
    // Include admin config
    require_once($script_dir . '/admin/config.php');
    testLog("Config loaded");
    
    // Include startup
    require_once(DIR_SYSTEM . 'startup.php');
    testLog("Startup loaded");
    
    // Initialize registry
    $registry = new Registry();
    
    // Config
    $config = new Config();
    $config->load('default');
    $config->load('admin');
    $registry->set('config', $config);
    
    // Log
    $log = new Log($config->get('error_filename') ?: 'error.log');
    $registry->set('log', $log);
    
    // Database
    $db = new DB(
        $config->get('db_engine') ?: DB_DRIVER,
        $config->get('db_hostname') ?: DB_HOSTNAME,
        $config->get('db_username') ?: DB_USERNAME,
        $config->get('db_password') ?: DB_PASSWORD,
        $config->get('db_database') ?: DB_DATABASE,
        $config->get('db_port') ?: DB_PORT
    );
    $registry->set('db', $db);
    
    // Other components
    $session = new Session($config->get('session_engine') ?: 'file', $registry);
    $registry->set('session', $session);
    
    $cache = new Cache($config->get('cache_engine') ?: 'file', $config->get('cache_expire') ?: 3600);
    $registry->set('cache', $cache);
    
    $event = new Event($registry);
    $registry->set('event', $event);
    
    $loader = new Loader($registry);
    $registry->set('load', $loader);
    
    testLog("All components initialized");
    
    // Initialize AdvQB library
    $registry->set('advqb', new Advqb($registry));
    $advqb = $registry->get('advqb');
    testLog("AdvQB library loaded");
    
    // Get first 5 linked products
    $linked_products_query = $db->query("SELECT * FROM " . DB_PREFIX . "advqb_product LIMIT 5");
    $linked_products = $linked_products_query->rows;
    
    testLog("Testing sync with " . count($linked_products) . " products:");
    
    $success_count = 0;
    $error_count = 0;
    
    foreach ($linked_products as $index => $linked_product) {
        $oc_product_id = $linked_product['oc_product_id'];
        $qb_product_id = $linked_product['advqb_product_id'];
        
        testLog("Product " . ($index + 1) . ": OC ID $oc_product_id, QB ID $qb_product_id");
        
        try {
            // Get current quantity from OpenCart
            $oc_query = $db->query("SELECT quantity FROM " . DB_PREFIX . "product WHERE product_id = '" . (int)$oc_product_id . "'");
            $current_oc_qty = $oc_query->num_rows > 0 ? $oc_query->row['quantity'] : 'NOT FOUND';
            testLog("  Current OC quantity: $current_oc_qty");
            
            // Try to get QB product info
            testLog("  Querying QuickBooks for product $qb_product_id...");
            
            $qb_product = $advqb->execute_curl("query", "GET", array(), "?query=" . urlencode("select * from Item WHERE Id = '" . $qb_product_id . "'"));
            
            if (isset($qb_product['QueryResponse']['Item'][0])) {
                $qb_item = $qb_product['QueryResponse']['Item'][0];
                $qb_qty = isset($qb_item['QtyOnHand']) ? $qb_item['QtyOnHand'] : 'N/A';
                testLog("  QB quantity: $qb_qty");
                
                if (isset($qb_item['Type']) && $qb_item['Type'] == 'Inventory') {
                    // Update the quantity
                    $new_qty = (int)$qb_qty;
                    $db->query("UPDATE " . DB_PREFIX . "product SET quantity = '" . $new_qty . "' WHERE product_id = '" . (int)$oc_product_id . "'");
                    testLog("  ✓ Updated OC quantity to: $new_qty");
                    $success_count++;
                } else {
                    testLog("  ⚠ Not an inventory item, skipped");
                }
            } else {
                testLog("  ✗ Product not found in QuickBooks");
                $error_count++;
            }
            
        } catch (Exception $e) {
            testLog("  ✗ Error: " . $e->getMessage());
            $error_count++;
        }
        
        testLog("  ---");
        
        // Small delay between requests to avoid rate limiting
        sleep(1);
    }
    
    testLog("=== Sync Results ===");
    testLog("Successfully synced: $success_count products");
    testLog("Errors: $error_count products");
    
    if ($success_count > 0) {
        testLog("SUCCESS: The sync functionality is working!");
        testLog("You can now set up the cron job to sync all products.");
    } else {
        testLog("WARNING: No products were successfully synced.");
        testLog("Please check QuickBooks connection and product data.");
    }
    
    testLog("=== Limited Sync Test Completed ===");
    
} catch (Exception $e) {
    testLog("FATAL ERROR: " . $e->getMessage());
    testLog("File: " . $e->getFile() . " Line: " . $e->getLine());
} catch (Error $e) {
    testLog("PHP ERROR: " . $e->getMessage());
    testLog("File: " . $e->getFile() . " Line: " . $e->getLine());
}

echo "</pre>";
echo "<p><strong>Test completed.</strong></p>";
echo "<p>If you see 'SUCCESS' above, the sync functionality is working and you can proceed with the cron job setup.</p>";
echo "<p>The full sync might take longer due to the large number of products (2206), but it should work.</p>";
?>
