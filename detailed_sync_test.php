<?php
/**
 * Detailed sync test with timeout and error handling
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set maximum execution time to 60 seconds
set_time_limit(60);

// Set content type
header('Content-Type: text/html; charset=utf-8');

echo "<h2>Detailed Sync Test</h2>";
echo "<pre>";

function testLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message<br>\n";
    flush();
    ob_flush();
}

try {
    testLog("=== Starting Detailed Sync Test ===");
    
    // Define VERSION constant
    if (!defined('VERSION')) {
        define('VERSION', '*******');
    }
    
    // Get script directory
    $script_dir = dirname(__FILE__);
    
    // Include admin config
    require_once($script_dir . '/admin/config.php');
    testLog("Config loaded");
    
    // Include startup
    require_once(DIR_SYSTEM . 'startup.php');
    testLog("Startup loaded");
    
    // Initialize registry
    $registry = new Registry();
    
    // Config
    $config = new Config();
    $config->load('default');
    $config->load('admin');
    $registry->set('config', $config);
    testLog("Config initialized");
    
    // Log
    $log = new Log($config->get('error_filename') ?: 'error.log');
    $registry->set('log', $log);
    
    // Database
    $db = new DB(
        $config->get('db_engine') ?: DB_DRIVER,
        $config->get('db_hostname') ?: DB_HOSTNAME,
        $config->get('db_username') ?: DB_USERNAME,
        $config->get('db_password') ?: DB_PASSWORD,
        $config->get('db_database') ?: DB_DATABASE,
        $config->get('db_port') ?: DB_PORT
    );
    $registry->set('db', $db);
    testLog("Database connected");
    
    // Other components
    $session = new Session($config->get('session_engine') ?: 'file', $registry);
    $registry->set('session', $session);
    
    $cache = new Cache($config->get('cache_engine') ?: 'file', $config->get('cache_expire') ?: 3600);
    $registry->set('cache', $cache);
    
    $event = new Event($registry);
    $registry->set('event', $event);
    
    $loader = new Loader($registry);
    $registry->set('load', $loader);
    
    testLog("All components initialized");
    
    // Check QuickBooks configuration
    $qb_settings = [
        'module_opc_advqb_status' => 'Module Status',
        'module_opc_advqb_client_key' => 'Client Key',
        'module_opc_advqb_client_secret' => 'Client Secret',
        'module_opc_advqb_access_token' => 'Access Token',
        'module_opc_advqb_refresh_token' => 'Refresh Token',
        'module_opc_advqb_realmid' => 'Realm ID',
        'module_opc_advqb_sandbox' => 'Sandbox Mode'
    ];
    
    foreach ($qb_settings as $setting => $name) {
        $query = $db->query("SELECT value FROM " . DB_PREFIX . "setting WHERE `key` = '" . $setting . "'");
        if ($query->num_rows > 0) {
            $value = $query->row['value'];
            if (in_array($setting, ['module_opc_advqb_client_key', 'module_opc_advqb_client_secret', 'module_opc_advqb_access_token', 'module_opc_advqb_refresh_token'])) {
                // Don't show sensitive data, just indicate if it exists
                testLog("$name: " . (empty($value) ? "NOT SET" : "SET (length: " . strlen($value) . ")"));
            } else {
                testLog("$name: " . ($value ? $value : "NOT SET"));
            }
        } else {
            testLog("$name: NOT FOUND");
        }
    }
    
    // Check linked products
    $linked_products_query = $db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "advqb_product");
    $linked_count = $linked_products_query->row['count'];
    testLog("Found $linked_count linked products");
    
    // Get a sample of linked products
    $sample_query = $db->query("SELECT * FROM " . DB_PREFIX . "advqb_product LIMIT 3");
    testLog("Sample linked products:");
    foreach ($sample_query->rows as $row) {
        testLog("  OC Product ID: {$row['oc_product_id']}, QB Product ID: {$row['advqb_product_id']}");
    }
    
    // Initialize AdvQB library
    $registry->set('advqb', new Advqb($registry));
    $advqb = $registry->get('advqb');
    testLog("AdvQB library loaded");
    
    // Test a simple method first
    testLog("Testing AdvQB library methods...");
    if (method_exists($advqb, 'syncQuantityFromAdvQB')) {
        testLog("✓ syncQuantityFromAdvQB method exists");
    }
    if (method_exists($advqb, 'execute_curl')) {
        testLog("✓ execute_curl method exists");
    }
    if (method_exists($advqb, 'updateAccessToken')) {
        testLog("✓ updateAccessToken method exists");
    }
    
    // Try to test QuickBooks connection first
    testLog("Testing QuickBooks connection...");
    
    // Let's try to call a simple QB API to test connection
    try {
        // This is a simple test - just try to get company info
        testLog("Attempting to connect to QuickBooks API...");
        
        // Set a shorter timeout for this test
        $original_timeout = ini_get('default_socket_timeout');
        ini_set('default_socket_timeout', 10);
        
        // Try to call the sync with a timeout
        $start_time = time();
        testLog("Starting sync operation (with 30-second timeout)...");
        
        // Use output buffering to capture any output from the sync
        ob_start();
        
        // Set an alarm to interrupt if it takes too long
        if (function_exists('pcntl_alarm')) {
            pcntl_alarm(30);
        }
        
        $count = $advqb->syncQuantityFromAdvQB();
        
        if (function_exists('pcntl_alarm')) {
            pcntl_alarm(0); // Cancel alarm
        }
        
        $sync_output = ob_get_clean();
        $end_time = time();
        $duration = $end_time - $start_time;
        
        testLog("Sync completed in $duration seconds");
        testLog("Products updated: $count");
        
        if (!empty($sync_output)) {
            testLog("Sync output: " . trim($sync_output));
        }
        
        // Restore timeout
        ini_set('default_socket_timeout', $original_timeout);
        
        if ($count > 0) {
            testLog("SUCCESS: $count products were synced!");
        } else {
            testLog("INFO: No products were updated");
            testLog("This could mean:");
            testLog("  - No quantity changes detected");
            testLog("  - QuickBooks authentication issues");
            testLog("  - All products already have correct quantities");
        }
        
    } catch (Exception $e) {
        testLog("ERROR during sync: " . $e->getMessage());
        testLog("Error file: " . $e->getFile() . " line: " . $e->getLine());
    }
    
    testLog("=== Detailed Sync Test Completed ===");
    
} catch (Exception $e) {
    testLog("FATAL ERROR: " . $e->getMessage());
    testLog("File: " . $e->getFile() . " Line: " . $e->getLine());
    testLog("Stack trace: " . $e->getTraceAsString());
} catch (Error $e) {
    testLog("PHP ERROR: " . $e->getMessage());
    testLog("File: " . $e->getFile() . " Line: " . $e->getLine());
}

echo "</pre>";
echo "<p><strong>Test completed. Check the output above for detailed results.</strong></p>";
?>
