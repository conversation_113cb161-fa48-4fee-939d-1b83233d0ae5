<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionModuleUpsmodule file
 *
 * @category Module_Controller
 */

class ControllerExtensionModuleUpsmodule extends Controller
{
    private $_error = [];
    private $_link_module = 'extension/module/upsmodule';
    private $_link_module_ups = 'extension/upsmodule/';
    private $_base_model = 'extension/upsmodule/base';
    private $_module_extension = 'marketplace/extension';
    private $_user_model = 'user/user_group';
    private $_access = 'access';
    private $_modify = 'modify';
    private $_warning = 'warning';
    private $_type = '&type=module';
    private $_codename = 'upsmodule';

    /**
     * ControllerExtensionModuleUpsmodule index
     *
     * @return null
     */
    public function index()
    {
        //region khai bao
        $module = '';
        $bread_crumbs = 'breadcrumbs';
        $module_upsmodule_status = 'module_upsmodule_status';
        $user_token = 'user_token=' . $this->session->data['user_token'];
        //end
        $data = new\ stdclass();
        $this->load->model($this->_base_model);
        $this->load->model($this->_link_module);
        $this->load->language($module);
        $heading_title = $this->language->get('heading_title');

        $this->document->setTitle($heading_title);
        $this->load->model('setting/setting');
        //REQUEST
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_setting_setting->editSetting('ups_shipping_module', $this->request->post);
            $this->session->data['success'] = $this->language->get('text_success');
            $this->response->redirect(
                $this->url->link($this->_module_extension, $user_token . $this->_type, true)
            );
        }
        //S Language
        $data->heading_title = $heading_title;
        $data->text_edit = $this->language->get('text_edit');
        $data->text_enabled = $this->language->get('text_enabled');
        $data->text_disabled = $this->language->get('text_disabled');
        $data->entry_status = $this->language->get('entry_status');
        $data->button_save = $this->language->get('button_save');
        $data->button_cancel = $this->language->get('button_cancel');
        //E Language
        //S show error
        if (isset($this->_error[$this->_warning])) {
            $data->error_warning = $this->_error[$this->_warning];
        } else {
            $data->error_warning = '';
        }
        //action
        $data->action = $this->url->link($this->_link_module, $user_token, true);
        //cancel
        $data->cancel = $this->url->link(
            $this->_module_extension,
            $user_token . $this->_type,
            true
        );
        //Get Status
        if (isset($this->request->post[$module_upsmodule_status])) {
            $data->module_upsmodule_status = $this->request->post[$module_upsmodule_status];
        } else {
            $data->module_upsmodule_status = $this->config->get($module_upsmodule_status);
        }
        //Data Response
        $data->header = $this->load->controller('common/header');
        $data->column_left = $this->load->controller('common/column_left');
        $data->footer = $this->load->controller('common/footer');
        //Add permisstion
        $this->reloadRole();
        $datas = (array)$data;
        $this->response->setOutput($this->load->view($this->_link_module, $datas));
    }

    /**
     * ControllerExtensionModuleUpsmodule validate
     *
     * @return $_error
     */
    protected function validate()
    {
        //Validate
        if (!$this->user->hasPermission($this->_modify, $this->_link_module)) {
            $this->_error[$this->_warning] = $this->language->get('error_permission');
        }
        return !$this->_error;
    }

    /**
     * ControllerExtensionModuleUpsmodule install
     *
     * @return null
     */
    public function install()
    {
        //Load model
        $this->load->model($this->_link_module);
        //Load Model
        $this->load->model($this->_base_model);
        $this->model_extension_module_upsmodule->createTables();
        //Add permisstion
        $this->permissionHandler();
        //get pre registered plugin token
        $this->model_extension_upsmodule_base->getPreRegisteredPluginToken();
    }

    /**
     * ControllerExtensionModuleUpsmodule permissionHandler
     *
     * @return null
     */
    public function permissionHandler()
    {
        //Load model
        $this->load->model($this->_user_model);
        $this->load->model($this->_base_model);
        //Get User ID
        $user_group_id = $this->user->getGroupId();
        //UPSMODULE
        $this->model_user_user_group->addPermission($user_group_id, $this->_access, 'extension/'.$this->_codename);
        $this->model_user_user_group->addPermission($user_group_id, $this->_modify, 'extension/'.$this->_codename);
        //List Module
        $list_module = $this->model_extension_upsmodule_base->listModuleBefore();
        foreach ($list_module as $item) {
            $module = $this->_link_module_ups . $item;
            //remove Permission
            $this->model_user_user_group->addPermission($user_group_id, $this->_access, $module);
            $this->model_user_user_group->addPermission($user_group_id, $this->_modify, $module);
        }
    }

    /**
     * ControllerExtensionModuleUpsmodule permissionHandler
     *
     * @return null
     */
    public function reloadRole()
    {
        //Load model
        $this->load->model($this->_user_model);
        $this->load->model($this->_base_model);

        if (!$this->user->hasPermission($this->_modify, $this->_link_module_ups . 'country')) {
            //Get User ID
            $user_group_id = $this->user->getGroupId();
            //UPSMODULE
            $this->model_user_user_group->addPermission($user_group_id, $this->_access, 'extension/'.$this->_codename);
            $this->model_user_user_group->addPermission($user_group_id, $this->_modify, 'extension/'.$this->_codename);
        }
    }

    /**
     * ControllerExtensionModuleUpsmodule uninstall
     *
     * @return null
     */
    public function uninstall()
    {
        //Load Model
        $this->load->model('setting/setting');
        $this->load->model($this->_link_module);
        //Load Model
        $this->load->model($this->_base_model);
        //update merchant status
        $this->model_extension_upsmodule_base->uninstallModuleTransfer();
        //Delete setting
        $this->model_setting_setting->deleteSetting('ups_shipping_module');
        $this->load->model($this->_link_module);
        //Remove Data
        $this->model_extension_module_upsmodule->deleteDatabase();
        //remove Permission
        $this->removePermission();
    }

    /**
     * ControllerExtensionModuleUpsmodule uninstall
     *
     * @return null
     */
    public function removePermission()
    {
        //Load model
        $this->load->model($this->_user_model);
        $this->load->model($this->_base_model);
        //Get User ID
        $user_group_id = $this->user->getGroupId();
        //List Module
        $list_module = $this->model_extension_upsmodule_base->listModuleAfter();
        foreach ($list_module as $item) {
            $module = $this->_link_module_ups . $item;
            //remove Permission
            $this->model_user_user_group->removePermission($user_group_id, $this->_access, $module);
            $this->model_user_user_group->removePermission($user_group_id, $this->_modify, $module);
        }
        //list Module Before
        $list_module_before = $this->model_extension_upsmodule_base->listModuleBefore();
        foreach ($list_module_before as $item) {
            $module = $this->_link_module_ups . $item;
            //remove Permission
            $this->model_user_user_group->removePermission($user_group_id, $this->_access, $module);
            $this->model_user_user_group->removePermission($user_group_id, $this->_modify, $module);
        }
    }
}
