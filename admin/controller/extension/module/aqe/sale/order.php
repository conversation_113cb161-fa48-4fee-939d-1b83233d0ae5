<?php
class ControllerExtensionModuleAqeSaleOrder extends Controller {
	protected $error = array();
	protected $alert = array(
		'error'     => array(),
		'warning'   => array(),
		'success'   => array(),
		'info'      => array()
	);

	public function __construct($registry) {
		parent::__construct($registry);

		if (!$this->config->get('module_admin_quick_edit_installed') || !$this->config->get('module_admin_quick_edit_status')) {
			$this->response->redirect($this->url->link('sale/order', 'user_token=' . $this->session->data['user_token'], true));
		}
	}

	public function index() {
		$this->load->model('sale/order');
		$this->load->model('extension/module/aqe/sale/order');

		$this->load->language('sale/order');
		$this->load->language('extension/module/aqe/sale/general');
		$this->load->language('extension/module/aqe/sale/order');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->getList();
	}

	public function delete() {
		$this->load->language('sale/order');
		$this->load->language('extension/module/aqe/sale/general');
		$this->load->language('extension/module/aqe/sale/order');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->session->data['success'] = $this->language->get('text_success_delete');

		$url = '';

		foreach($this->config->get('module_admin_quick_edit_sale_orders') as $column => $attr) {
			if (isset($this->request->get['filter_' . $column])) {
				$url .= '&filter_' . $column . '=' . urlencode(html_entity_decode($this->request->get['filter_' . $column], ENT_QUOTES, 'UTF-8'));
			}
		}

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		if (isset($this->request->get['cf'])) {
			$data['custom_filter'] = (int)$this->request->get['cf'];
		} else if (isset($this->session->data['cf'])) {
			$data['custom_filter'] = (int)$this->session->data['cf'];
		} else {
			$data['custom_filter'] = (int)$this->config->get('module_admin_quick_edit_sale_orders_default_filter');
		}

		if ((int)$this->config->get('module_admin_quick_edit_override_menu_entry')) {
			$this->response->redirect($this->url->link('sale/order', 'user_token=' . $this->session->data['user_token'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true));
		} else {
			$this->response->redirect($this->url->link('extension/module/admin_quick_edit/sale__order__', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}
	}

	protected function getList() {
		$data['module_admin_quick_edit_tooltip'] = ($this->config->get('module_admin_quick_edit_quick_edit_on') == 'dblclick') ? $this->language->get('text_double_click_edit') : $this->language->get('text_click_edit');
		$data['module_admin_quick_edit_quick_edit_on'] = $this->config->get('module_admin_quick_edit_quick_edit_on');
		$data['notify_customer'] = $this->config->get('module_admin_quick_edit_sale_orders_notify_customer');
		$data['module_admin_quick_edit_row_hover_highlighting'] = $this->config->get('module_admin_quick_edit_row_hover_highlighting');
		$data['module_admin_quick_edit_alternate_row_colour'] = $this->config->get('module_admin_quick_edit_alternate_row_colour');
		$data['custom_filter_statuses'] = (array)$this->config->get('module_admin_quick_edit_sale_orders_default_filter_statuses');
		$data['custom_status_colours'] = (array)$this->config->get('module_admin_quick_edit_sale_orders_status_colours');
		$data['highlight_status'] = $this->config->get('module_admin_quick_edit_sale_orders_highlight_status');

		if (isset($this->request->get['cf'])) {
			$data['custom_filter'] = (int)$this->request->get['cf'];
		} elseif (isset($this->session->data['cf'])) {
			$data['custom_filter'] = (int)$this->session->data['cf'];
		} else {
			$data['custom_filter'] = (int)$this->config->get('module_admin_quick_edit_sale_orders_default_filter');
		}

		$this->document->addScript('view/javascript/aqe/catalog.min.js?v=' . EXTENSION_VERSION);

		$this->document->addStyle('view/stylesheet/aqe/catalog.min.css?v=' . EXTENSION_VERSION);

		$filters = array();

		foreach($this->config->get('module_admin_quick_edit_sale_orders') as $column => $attr) {
			$filters[$column] = (isset($this->request->get['filter_' . $column])) ? $this->request->get['filter_' . $column] : null;
		}

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = $this->config->get('module_admin_quick_edit_sale_orders_default_sort');
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = $this->config->get('module_admin_quick_edit_sale_orders_default_order');
		}

		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}

		$url = '';

		foreach($this->config->get('module_admin_quick_edit_sale_orders') as $column => $attr) {
			if (isset($this->request->get['filter_' . $column])) {
				$url .= '&filter_' . $column . '=' . urlencode(html_entity_decode($this->request->get['filter_' . $column], ENT_QUOTES, 'UTF-8'));
			}
		}

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text'      => $this->language->get('text_home'),
			'href'      => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true),
			'active'    => false
		);

		$data['breadcrumbs'][] = array(
			'text'      => $this->language->get('heading_title'),
			'href'      => (int)$this->config->get('module_admin_quick_edit_override_menu_entry') ? $this->url->link('sale/order', 'user_token=' . $this->session->data['user_token'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true) : $this->url->link('extension/module/admin_quick_edit/sale__order__', 'user_token=' . $this->session->data['user_token'] . $url, true),
			'active'    => true
		);

		$data['add'] = $this->url->link('sale/order/add', 'user_token=' . $this->session->data['user_token'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true);
		$data['delete'] = str_replace('&amp;', '&', $this->url->link('sale/order/delete', 'user_token=' . $this->session->data['user_token'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true));
		$data['invoice'] = $this->url->link('sale/order/invoice', 'user_token=' . $this->session->data['user_token'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true);
		$data['shipping'] = $this->url->link('sale/order/shipping', 'user_token=' . $this->session->data['user_token'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true);

		$actions = array(
			'view'              => array('display' => 1, 'index' => 30, 'short' =>  'vw',    'type' =>       'view', 'class' =>            '', 'rel' => array()),
			'edit'              => array('display' => 1, 'index' => 40, 'short' =>  'ed',    'type' =>       'edit', 'class' => 'btn-primary', 'rel' => array()),
			'delete'            => array('display' => 1, 'index' => 50, 'short' => 'del',    'type' =>     'delete', 'class' =>  'btn-danger', 'rel' => array()),
		);

		$actions = array_filter($actions, 'column_display');
		foreach ($actions as $action => $attr) {
			$actions[$action]['name'] = $this->language->get('action_' . $action);
		}
		uasort($actions, 'column_sort');
		$data['order_actions'] = $actions;

		$columns = $this->config->get('module_admin_quick_edit_sale_orders');
		$columns = array_filter($columns, 'column_display');
		foreach ($columns as $column => $attr) {
			$columns[$column]['name'] = $this->language->get('column_' . $column);
		}
		uasort($columns, 'column_sort');
		$data['order_columns'] = $columns;

		$displayed_columns = array_keys($columns);
		$displayed_actions = array_keys($actions);
		$related_columns = array_merge(array_map(function($v) { return isset($v['rel']) ? $v['rel'] : ''; }, $columns), array_map(function($v) { return isset($v['rel']) ? $v['rel'] : ''; }, $actions));

		$data['orders'] = array();

		$filter_data = array(
			'sort'      => $sort,
			'order'     => $order,
			'start'     => ($page - 1) * $this->config->get('config_limit_admin'),
			'limit'     => $this->config->get('config_limit_admin'),
			'columns'   => $displayed_columns,
			'actions'   => $displayed_actions,
		);

		foreach ($filters as $filter => $value) {
			$filter_data['filter_' . $filter] = $value;
		}

		if ($data['custom_filter'] > 0) {
			$filter_data['filter_order_statuses'] = $data['custom_filter_statuses'];
		}

		$results = $this->model_extension_module_aqe_sale_order->getOrders($filter_data);

		$order_total = $this->model_extension_module_aqe_sale_order->getTotalOrders();

		foreach ($results as $result) {
			$_buttons = array();

			foreach ($actions as $action => $attr) {
				switch ($action) {
					case 'view':
						$_buttons[] = array(
							'type'  => $attr['type'],
							'action'=> $action,
							'title' => $this->language->get('action_' . $action),
							'url'   => html_entity_decode($this->url->link('sale/order/info', '&order_id=' . $result['order_id'] . '&user_token=' . $this->session->data['user_token'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true), ENT_QUOTES, 'UTF-8'),
							'icon'  => 'eye',
							'name'  => null,
							'rel'   => json_encode(array()),
							'class' => $attr['class'],
						);
						break;
					case 'edit':
						$_buttons[] = array(
							'type'  => $attr['type'],
							'action'=> $action,
							'title' => $this->language->get('action_' . $action),
							'url'   => html_entity_decode($this->url->link('sale/order/edit', '&order_id=' . $result['order_id'] . '&user_token=' . $this->session->data['user_token'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true), ENT_QUOTES, 'UTF-8'),
							'icon'  => 'pencil',
							'name'  => null,
							'rel'   => json_encode(array()),
							'class' => $attr['class'],
						);
						break;
					case 'delete':
						$_buttons[] = array(
							'type'  => $attr['type'],
							'action'=> $action,
							'title' => $this->language->get('action_' . $action),
							'url'   => null,
							'icon'  => 'trash-o',
							'name'  => null,
							'rel'   => json_encode(array()),
							'class' => $attr['class'],
						);
						break;
					default:
						$_buttons[] = array(
							'type'  => $attr['type'],
							'action'=> $action,
							'title' => $this->language->get('action_' . $action),
							'url'   => null,
							'icon'  => null,
							'name'  => $this->language->get('action_' . $attr['short']),
							'rel'   => json_encode($attr['rel']),
							'class' => $attr['class'],
						);
						break;
				}
			}

			$row = array(
				'order_id'   => $result['order_id'],
				'selected'   => isset($this->request->post['selected']) && in_array($result['order_id'], $this->request->post['selected']),
				'action'     => $_buttons
			);
			if (!is_array($columns)) {
				$row['customer'] = $result['customer'];
				$row['status'] = $result['status'];
				$row['total'] = $result['total'];
				$row['date_added'] = $result['date_added'];
				$row['date_modified'] = $result['date_modified'];
			} else {
				if (!empty($result['order_status_id'])) {
					$row['status_colour'] = !empty($data['custom_status_colours'][$result['order_status_id']]) ? $data['custom_status_colours'][$result['order_status_id']] : '#FFFFFF';
				} else {
					$row['status_colour'] = !empty($data['custom_status_colours']['0']) ? $data['custom_status_colours']['0'] : '#FFFFFF';
				}
				foreach ($columns as $column => $attr) {
					if (in_array($column, array('date_added', 'date_modified'))) {
						$date = new DateTime($result[$column]);
						// $row[$column] = $date->format("Y-m-d");
						$row[$column] = $date->format($this->language->get('date_format_short'));
					} else if ($column == 'payment_country_id') {
						$row[$column] = $result['payment_country'];
					} else if ($column == 'shipping_country_id') {
						$row[$column] = $result['shipping_country'];
					} else if ($column == 'status') {
						$row[$column] = !empty($result[$column]) ? $result[$column] : $this->language->get('text_missing_order');
					} else if ($column == 'action') {
						$row[$column] = $_buttons;
					} else if ($column == 'total') {
						$row[$column] = $this->currency->format($result['total'], $result['currency_code'], $result['currency_value']);
					} else if ($column == 'customer') {
						$row[$column] = $result[$column];
						if (!empty($result['customer_id'])) {
							$row['customer_edit_url'] = html_entity_decode($this->url->link('customer/customer/edit', 'customer_id=' . $result['customer_id'] . '&user_token=' . $this->session->data['user_token'], true), ENT_QUOTES, 'UTF-8');
						} else {
							$row['customer_edit_url'] = null;
						}
					} else if ($column == 'selector') {
						$row[$column] = '';
					} else {
						$row[$column] = $result[$column];
					}
				}
			}
			$data['orders'][] = $row;
		}

		$data['language_id'] = $this->config->get('config_language_id');

		$column_classes = array();
		$type_classes = array();
		$non_sortable = array();

		if (!is_array($columns)) {
			$displayed_columns = array('selector', 'order_id', 'customer', 'status', 'total', 'date_added', 'date_modified', 'action');
			$columns = array();
		} else {
			foreach ($columns as $column => $attr) {
				if (empty($attr['sort'])) {
					$non_sortable[] = 'col_' . $column;
				}

				if (!empty($attr['type']) && !in_array($attr['type'], $type_classes)) {
					$type_classes[] = $attr['type'];
				}

				if (!empty($attr['align'])) {
					if (!empty($attr['type']) && $attr['editable']) {
						$column_classes[] = $attr['align'] . ' ' . $attr['type'];
					} else {
						$column_classes[] = $attr['align'];
					}
				} else {
					if (!empty($attr['type'])) {
						$column_classes[] = $attr['type'];
					} else {
						$column_classes[] = null;
					}
				}
			}
		}

		$data['columns'] = $displayed_columns;
		$data['actions'] = $displayed_actions;
		$data['related'] = $related_columns;
		$data['column_info'] = $columns;
		$data['non_sortable_columns'] = json_encode($non_sortable);
		$data['column_classes'] = $column_classes;
		$data['types'] = $type_classes;

		$data['update_url'] = html_entity_decode($this->url->link('extension/module/admin_quick_edit/sale__order__quick_update', 'user_token=' . $this->session->data['user_token'], true));
		$data['refresh_url'] = html_entity_decode($this->url->link('extension/module/admin_quick_edit/sale__order__refresh_data', 'user_token=' . $this->session->data['user_token'], true));

		$data['yes_no_select'] = addslashes(json_encode(array(array("id" => "0", "value" => $this->language->get('text_unopened')), array("id" => "1", "value" => $this->language->get('text_opened')))));

		$data['load_popup_url'] = html_entity_decode($this->url->link('extension/module/admin_quick_edit/sale__order__load_popup', 'user_token=' . $this->session->data['user_token'], true));

		$data['batch_edit'] = (int)$this->config->get('module_admin_quick_edit_batch_edit');

		if (in_array("status", $displayed_columns)) {
			$this->load->model('localisation/order_status');
			$data['order_statuses'] = $this->model_localisation_order_status->getOrderStatuses();
			$os_select = array();
			foreach ($data['order_statuses'] as $os) {
				$os_select[$os['order_status_id']] = $os['name'];
			}
			$data['order_statuses_select'] = addslashes(json_encode($os_select, JSON_UNESCAPED_SLASHES));
		} else {
			$data['order_statuses_select'] = addslashes(json_encode(array()));
		}

		if (in_array("payment_country_id", $displayed_columns) || in_array("shipping_country_id", $displayed_columns)) {
			$this->load->model('localisation/country');
			$data['countries'] = $this->model_localisation_country->getCountries();
			$country_select = array();
			foreach ($data['countries'] as $c) {
				$country_select[$c['country_id']] = $c['name'];
			}
			$data['countries_select'] = addslashes(json_encode($country_select, JSON_UNESCAPED_SLASHES));
		} else {
			$data['countries_select'] = addslashes(json_encode(array()));
		}

		$data['user_token'] = $this->session->data['user_token'];

		$url = '';

		foreach ($this->config->get('module_admin_quick_edit_sale_orders') as $column => $attr) {
			if (isset($this->request->get['filter_' . $column])) {
				$url .= '&filter_' . $column . '=' . urlencode(html_entity_decode($this->request->get['filter_' . $column], ENT_QUOTES, 'UTF-8'));
			}
		}
		if ($order == 'ASC') {
			$url .= '&order=DESC';
		} else {
			$url .= '&order=ASC';
		}

		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}

		$data['sorts'] = array();
		foreach ($this->config->get('module_admin_quick_edit_sale_orders') as $column => $attr) {
			if ((int)$this->config->get('module_admin_quick_edit_override_menu_entry')) {
				$data['sorts'][$column] = $this->url->link('sale/order', 'user_token=' . $this->session->data['user_token'] . '&sort=' . $attr['sort'] . $url . '&aqer=1&cf=' . $data['custom_filter'], true);
			} else {
				$data['sorts'][$column] = $this->url->link('extension/module/admin_quick_edit/sale__order__', 'user_token=' . $this->session->data['user_token'] . '&sort=' . $attr['sort'] . '&cf=' . $data['custom_filter'] . $url, true);
			}
		}

		$url = '';

		foreach ($this->config->get('module_admin_quick_edit_sale_orders') as $column => $attr) {
			if (isset($this->request->get['filter_' . $column])) {
				$url .= '&filter_' . $column . '=' . urlencode(html_entity_decode($this->request->get['filter_' . $column], ENT_QUOTES, 'UTF-8'));
			}
		}

		if (isset($this->request->get['sort'])) {
			$url .= '&sort=' . $this->request->get['sort'];
		}

		if (isset($this->request->get['order'])) {
			$url .= '&order=' . $this->request->get['order'];
		}

		$pagination = new Pagination();
		$pagination->total = $order_total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit_admin');

		if ((int)$this->config->get('module_admin_quick_edit_override_menu_entry')) {
			$pagination->url = $this->url->link('sale/order', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}' . '&aqer=1&cf=' . $data['custom_filter'], true);
		} else {
			$pagination->url = $this->url->link('extension/module/admin_quick_edit/sale__order__', 'user_token=' . $this->session->data['user_token'] . '&cf=' . $data['custom_filter'] . $url . '&page={page}', true);
		}

		$data['pagination'] = $pagination->render();

		$data['results'] = sprintf($this->language->get('text_pagination'), ($order_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($order_total - $this->config->get('config_limit_admin'))) ? $order_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $order_total, ceil($order_total / $this->config->get('config_limit_admin')));

		if (isset($this->session->data['error'])) {
			$this->error = $this->session->data['error'];

			unset($this->session->data['error']);
		}

		if (isset($this->error['warning'])) {
			$this->alert['warning']['warning'] = $this->error['warning'];
		}

		if (isset($this->error['error'])) {
			$this->alert['error']['error'] = $this->error['error'];
		}

		if (isset($this->session->data['success'])) {
			$this->alert['success']['success'] = $this->session->data['success'];

			unset($this->session->data['success']);
		}

		$data['catalog'] = $this->request->server['HTTPS'] ? HTTPS_CATALOG : HTTP_CATALOG;

		// API login
		$this->load->model('user/api');

		$api_info = $this->model_user_api->getApi($this->config->get('config_api_id'));

		if ($api_info && $this->user->hasPermission('modify', 'sale/order')) {
			$session = new Session($this->config->get('session_engine'), $this->registry);

			$session->start();

			$this->model_user_api->deleteApiSessionBySessionId($session->getId());

			$this->model_user_api->addApiSession($api_info['api_id'], $session->getId(), $this->request->server['REMOTE_ADDR']);

			$session->data['api_id'] = $api_info['api_id'];

			$data['api_token'] = $session->getId();
		} else {
			$data['api_token'] = '';
		}

		$data['filters'] = $filters;
		$data['alerts'] = $this->alert;

		$data['sort'] = $sort;
		$data['order'] = $order;

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$template = 'extension/module/aqe/sale/order_list';

		$this->response->setOutput($this->load->view($template, $data));
	}

	public function refresh_data() {
		$this->load->model('sale/order');
		$this->load->model('extension/module/aqe/sale/order');

		$this->load->language('sale/order');
		$this->load->language('extension/module/aqe/sale/general');
		$this->load->language('extension/module/aqe/sale/order');

		$response = array();

		if ($this->request->server['REQUEST_METHOD'] == 'POST' && $this->validateRefreshData($this->request->post)) {
			$response['values'] = array();

			foreach ($this->request->post['data'] as $column => $orders) {
				foreach ($orders as $id) {
					switch ($column) {
						case 'date_modified':
							$result = $this->model_sale_order->getOrder($id);
							$date = new DateTime($result[$column]);
							// $response['values'][$id][$column] = $date->format("Y-m-d");
							$response['values'][$id][$column] = $date->format($this->language->get('date_format_short'));
							break;
						default:
							$response['value'] = "";
							break;
					}
				}
			}
			$response['success'] = 1;
		}

		$response = array_merge($response, array("errors" => $this->error), array("alerts" => $this->alert));

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($response));
	}

	public function quick_update() {
		$this->load->language('sale/order');
		$this->load->language('extension/module/aqe/sale/order');
		$this->load->language('extension/module/aqe/sale/general');
		$this->load->model('sale/order');

		$alert = array("error" => array(), "success" => array());
		$response = array();

		if ($this->request->server['REQUEST_METHOD'] == 'POST' && $this->validateUpdateData($this->request->post)) {
			list($column, $id) = explode("-", $this->request->post['id']);
			$id = (array)$id;
			$value = $this->request->post['new'];

			if (isset($this->request->post['ids'])) {
				$id = array_unique(array_merge((array)$id, (array)$this->request->post['ids']));
			}

			$results = array('done' => array(), 'failed' => array());

			if (isset($this->request->post['notify'])) {
				$notify = $this->request->post['notify'];
			} else {
				$notify = $this->config->get('module_admin_quick_edit_sale_orders_notify_customer');
			}

			$post_data = array(
				'order_status_id' => $value,
				'notify' => $notify,
				'override' => "0",
				'append' => "0",
				'comment' => ""
			);

			$store_url = $this->request->server['HTTPS'] ? HTTPS_CATALOG : HTTP_CATALOG;

			// API login
			$this->load->model('user/api');

			$api_info = $this->model_user_api->getApi($this->config->get('config_api_id'));

			if ($api_info) {
				$session = new Session($this->config->get('session_engine'), $this->registry);

				$session->start();

				if (property_exists($this->model_user_api, "deleteApiSessionBySessionId")) {
					$this->model_user_api->deleteApiSessionBySessionId($session->getId());
				} else {
					$this->model_user_api->deleteApiSessionBySessonId($session->getId());
				}

				$this->model_user_api->addApiSession($api_info['api_id'], $session->getId(), $this->request->server['REMOTE_ADDR']);

				$session->data['api_id'] = $api_info['api_id'];

				$api_token = $session->getId();

				// Force write api_id to database
				$session->close();
			} else {
				$api_token = '';
			}

			if ($column == "status" && $api_token) {
				foreach ((array)$id as $_id) {
					$order_info = $this->model_sale_order->getOrder($_id);

					if (!empty($order_info)) {
						$store_id = $order_info['store_id'];
						$curl = curl_init();

						// Set SSL if required
						if (substr($store_url, 0, 5) == 'https') {
							curl_setopt($curl, CURLOPT_PORT, 443);
						}

						curl_setopt($curl, CURLOPT_HEADER, false);
						curl_setopt($curl, CURLINFO_HEADER_OUT, true);
						curl_setopt($curl, CURLOPT_USERAGENT, $this->request->server['HTTP_USER_AGENT']);
						curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
						curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
						curl_setopt($curl, CURLOPT_FORBID_REUSE, false);
						curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
						curl_setopt($curl, CURLOPT_URL, $store_url . 'index.php?route=api/order/history&api_token=' . $api_token . '&store_id=' . $store_id . '&order_id=' . $_id);
						curl_setopt($curl, CURLOPT_TIMEOUT, 10);
						curl_setopt($curl, CURLOPT_POST, true);
						curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($post_data));

						$json = curl_exec($curl);

						curl_close($curl);

						if ($json) {
							$json = json_decode($json, true);
						}

						if (!empty($json['error'])) {
							$alert['error']['msg'] = $json['error'];
							$results['failed'][] = $_id;
						} else {
							$results['done'][] = $_id;
						}
					} else {
						$results['failed'][] = $_id;
					}
				}
			} else {
				if (!empty($json['error'])) {
					$alert['error'] = array_merge($alert['error'], (array)$json['error']);
				} else if (empty($json['success']) || empty($json['token'])) {
					$alert['error']['api_login'] = $this->language->get('error_login');
				}
			}

			$response['results'] = $results;

			if ($results['done']) {
				if ($results['failed'] || (int)$this->config->get('module_admin_quick_edit_show_success_message')) {
					$alert['success']['update'] = sprintf($this->language->get('text_success_update'), count($results['done']));
				}
				if ($results['failed']) {
					$alert['error']['update'] = sprintf($this->language->get('error_failed_update'), count($results['failed']));
				}
				$response['success'] = 1;
				if (in_array($column, array('status'))) {
					$this->load->model('localisation/order_status');
					$status = $this->model_localisation_order_status->getOrderStatus($value);
					$response['value'] = $status['name'];
					$response['values']['*'] = $response['value'];
				} else {
					$response['value'] = $value;
					$response['values']['*'] = $response['value'];
				}
			} else {
				$alert['error']['result'] = $this->language->get('error_update');
			}
		} else {
			$response['error'] = $this->error['warning'];
		}

		$response = array_merge($response, array("errors" => $this->error), array("alerts" => $alert));

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($response));
	}

	protected function validateUpdateData(&$data) {
		$errors = !$this->validatePermissions();

		if (!isset($data['id']) || strpos($data['id'], "-") === false) {
			$this->error['warning'] = $this->language->get('error_update');
			return false;
		}

		list($column, $id) = explode("-", $data['id']);

		if (!isset($data['old'])) {
			$errors = true;
			$this->error['warning'] = $this->language->get('error_update');
		}

		if (!isset($data['new'])) {
			$errors = true;
			$this->error['warning'] = $this->language->get('error_update');
		}

		if ($column == "customer") {
			if ((utf8_strlen(trim($data['first_name'])) < 1) || (utf8_strlen(trim($data['first_name'])) > 32)) {
				$errors = true;
				$this->error['first_name'] = $this->language->get('error_firstname');
			}
			if ((utf8_strlen(trim($data['last_name'])) < 1) || (utf8_strlen(trim($data['last_name'])) > 32)) {
				$errors = true;
				$this->error['last_name'] = $this->language->get('error_lastname');
			}
		}

		return !$errors;
	}

	protected function validateRefreshData(&$data) {
		$errors = !$this->validatePermissions();

		if (!isset($data['data'])) {
			$errors = true;
			$this->alert['error']['request'] = $this->language->get('error_update');
			return false;
		}

		return !$errors;
	}

	private function validatePermissions() {
		if (!$this->user->hasPermission('modify', 'sale/order') || !$this->user->hasPermission('modify', 'extension/module/admin_quick_edit')) {
			$this->alert['error']['permission'] = $this->language->get('error_permission');
			return false;
		} else {
			return true;
		}
	}
}
