<?php
class ControllerExtensionModuleOpcQb extends Controller {
	private $error = array();

	public function install() {
		$this->load->model('qb/opc_qb');

		$this->load->model('setting/event');

		$this->model_qb_opc_qb->createTable();

		$this->model_setting_event->addEvent('opc_qb', 'admin/model/customer/customer/addCustomer/after', 'qb/customer/auto_sync');

		$this->model_setting_event->addEvent('opc_qb', 'admin/model/customer/customer/editCustomer/after', 'qb/customer/auto_sync');

		$this->model_setting_event->addEvent('opc_qb', 'catalog/model/account/customer/addCustomer/after', 'qb/customer/auto_sync');

		$this->model_setting_event->addEvent('opc_qb', 'catalog/model/account/customer/editCustomer/after', 'qb/customer/auto_sync');

		$this->model_setting_event->addEvent('opc_qb', 'admin/model/catalog/product/addProduct/after', 'qb/product/auto_sync');

		$this->model_setting_event->addEvent('opc_qb', 'admin/model/catalog/product/editProduct/after', 'qb/product/auto_sync');

		$this->model_setting_event->addEvent('opc_qb', 'catalog/model/checkout/order/addOrderHistory/after', 'qb/order/auto_sync');

		$this->model_setting_event->addEvent('opc_qb', 'admin/model/sale/order/deleteOrder/after', 'qb/order/auto_delete');
	}

	public function uninstall() {
		$this->load->model('qb/opc_qb');

		$this->load->model('setting/event');

		$this->model_qb_opc_qb->dropTable();

		$this->model_setting_event->deleteEventByCode('opc_qb');
	}

	public function menu(){
		$this->load->language('extension/module/opc_qb');

		$menus = array();

		$quickbook = array();

		if ($this->config->get('module_opc_qb_status')) {
			if ($this->user->hasPermission('access', 'extension/module/opc_qb')) {
				$quickbook[] = array(
					'name'	   => $this->language->get('text_configuration'),
					'href'     => $this->url->link('extension/module/opc_qb', 'user_token=' . $this->session->data['user_token'], true),
					'children' => array()
				);
			}

			if ($this->user->hasPermission('access', 'qb/customer')) {
				$quickbook[] = array(
					'name'	   => $this->language->get('text_customer_qb'),
					'href'     => $this->url->link('qb/customer', 'user_token=' . $this->session->data['user_token'], true),
					'children' => array()
				);
			}

			if ($this->user->hasPermission('access', 'qb/product')) {
				$quickbook[] = array(
					'name'	   => $this->language->get('text_product_qb'),
					'href'     => $this->url->link('qb/product', 'user_token=' . $this->session->data['user_token'], true),
					'children' => array()
				);
			}

			if ($this->user->hasPermission('access', 'qb/order')) {
				$quickbook[] = array(
					'name'	   => $this->language->get('text_order_qb'),
					'href'     => $this->url->link('qb/order', 'user_token=' . $this->session->data['user_token'], true),
					'children' => array()
				);
			}

			if ($quickbook) {
				$menus = array(
					'id'       => 'menu-quickbook',
					'icon'	   => 'fa-plug',
					'name'	   => $this->language->get('text_quickbook'),
					'href'     => '',
					'children' => $quickbook
				);
			}
		}

		return $menus;
	}

	public function index() {
		$data = $this->load->language('extension/module/opc_qb');

		if ($this->request->post) {
			function clean(&$value) {
			    $value = trim($value);
			}

			array_walk_recursive($this->request->post, 'clean');
		}

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('setting/setting');


		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {

			$this->model_setting_setting->editSetting('module_opc_qb', $this->request->post);

			$this->session->data['success'] = $this->language->get('text_success');

			$this->response->redirect($this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true));
		}

		$opc_error = array(
			'warning',
			'client_key',
			'client_secret',
			'access_token',
			'refresh_token',
			'realmid',
			'slot',
		);

		foreach ($opc_error as $key => $value) {
			if (isset($this->error[$value])) {
				$data['error_'.$value] = $this->error[$value];
			} else {
				$data['error_'.$value] = '';
			}
		}

		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_extension'),
			'href' => $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('extension/module/opc_qb', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['user_guide'] = $this->url->link('extension/module/opc_qb/user_guide', 'user_token=' . $this->session->data['user_token'], true);

		$data['action'] = $this->url->link('extension/module/opc_qb', 'user_token=' . $this->session->data['user_token'], true);

		$data['cancel'] = $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true);

    $opc_module_config = array(
			'status',
			'sandbox',
			'slot',
			'order_mapping',
			'client_key',
			'client_secret',
			'access_token',
			'refresh_token',
			'realmid',
			'auto_sync',
			'tax',
			'asset',
			'income',
			'expense',
			'discount',
			'transaction_prefix',
			'order_status',
			'date',
		);

    foreach ($opc_module_config as $key => $value) {
      if (isset($this->request->post['module_opc_qb_'.$value])) {
  			$data['module_opc_qb_'.$value] = $this->request->post['module_opc_qb_'.$value];
			} elseif (isset($this->session->data['module_opc_qb_'.$value])) {
				$data['module_opc_qb_'.$value] = $this->session->data['module_opc_qb_'.$value];
  		} else {
  			$data['module_opc_qb_'.$value] = $this->config->get('module_opc_qb_'.$value);
  		}
    }

		$this->load->model('localisation/order_status');

		$data['order_statuses'] = $this->model_localisation_order_status->getOrderStatuses();

		$data['connect_button'] = 0;

		if ($this->config->get('module_opc_qb_client_key') && $this->config->get('module_opc_qb_client_secret')) {
			$data['connect_button'] = 1;
		}

		if ($this->request->server['HTTPS']) {
			$data['redirect_uri'] = HTTPS_CATALOG . 'index.php?route=account/qb';
		} else {
			$data['redirect_uri'] = HTTP_CATALOG . 'index.php?route=account/qb';
		}

		$parameters = array(
		  'client_id' => $this->config->get('module_opc_qb_client_key'),
		  'scope' => 'com.intuit.quickbooks.accounting',
		  'redirect_uri' => $data['redirect_uri'],
		  'response_type' => 'code',
		  'state' => 'RandomState'
		);

		$data['authorizationRequestUrl'] = 'https://appcenter.intuit.com/connect/oauth2?' . http_build_query($parameters, null, '&', PHP_QUERY_RFC1738);

		$this->registry->set('qb', new Qb($this->registry));

		$data['accounts'] = $this->qb->getAccounts();

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('extension/module/opc_qb', $data));
	}

	public function user_guide() {
	  $this->document->setTitle('Opencart QuickBook Connector User Guide');

	  $data['cancel'] = $this->url->link('extension/module/opc_qb', 'user_token=' . $this->session->data['user_token'], true);

	  $data['user_token'] = $this->session->data['user_token'];

	  $data['header'] = $this->load->controller('common/header');

	  $data['column_left'] = $this->load->controller('common/column_left');

	  $data['footer'] = $this->load->controller('common/footer');

	  $this->response->setOutput($this->load->view('extension/module/opc_qb_user_guide', $data));
	}

	protected function validate() {
		if (!$this->user->hasPermission('modify', 'extension/module/opc_qb')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if ($this->request->post['module_opc_qb_status']) {

			if (!isset($this->request->post['module_opc_qb_slot']) || $this->request->post['module_opc_qb_slot'] < 5 || $this->request->post['module_opc_qb_slot'] > 50) {
				$this->error['slot'] = $this->language->get('error_slot');
			}

			$opc_error = array(
				'client_key',
				'client_secret',
			);

			foreach ($opc_error as $key => $value) {
				if (!isset($this->request->post['module_opc_qb_'.$value]) || !$this->request->post['module_opc_qb_'.$value]) {
					$this->error[$value] = $this->language->get('error_'.$value);
				}
			}

			$opc_error = array(
				'access_token',
				'refresh_token',
				'realmid',
			);

			if ($this->config->get('module_opc_qb_client_key') && $this->config->get('module_opc_qb_client_secret')) {
				foreach ($opc_error as $key => $value) {
					if (!isset($this->request->post['module_opc_qb_'.$value]) || !$this->request->post['module_opc_qb_'.$value]) {
						$this->error[$value] = $this->language->get('error_'.$value);
					}
				}
			}
		}

		return !$this->error;
	}
}
