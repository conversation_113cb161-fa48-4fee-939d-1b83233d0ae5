<?php
class ControllerExtensionModuleOpcAdvqb extends Controller {
	private $error = array();

	public function install() {
		$this->load->model('advqb/opc_advqb');

		$this->load->model('setting/event');

		$this->model_advqb_opc_advqb->createTable();

		$this->model_setting_event->addEvent('opc_advqb', 'admin/model/customer/customer/addCustomer/after', 'advqb/customer/auto_sync');

		$this->model_setting_event->addEvent('opc_advqb', 'admin/model/customer/customer/editCustomer/after', 'advqb/customer/auto_sync');

		$this->model_setting_event->addEvent('opc_advqb', 'catalog/model/account/customer/addCustomer/after', 'advqb/customer/auto_sync');

		$this->model_setting_event->addEvent('opc_advqb', 'catalog/model/account/customer/editCustomer/after', 'advqb/customer/auto_sync');

		$this->model_setting_event->addEvent('opc_advqb', 'admin/model/catalog/product/addProduct/after', 'advqb/product/auto_sync');

		$this->model_setting_event->addEvent('opc_advqb', 'admin/model/catalog/product/editProduct/after', 'advqb/product/auto_sync');

		$this->model_setting_event->addEvent('opc_advqb', 'catalog/model/checkout/order/addOrderHistory/after', 'advqb/order/auto_sync');
	}

	public function uninstall() {
		$this->load->model('advqb/opc_advqb');

		$this->load->model('setting/event');

		$this->model_advqb_opc_advqb->dropTable();

		$this->model_setting_event->deleteEventByCode('opc_advqb');
	}

	public function menu(){
		$this->load->language('extension/module/opc_advqb');

		$menus = array();

		$adv_quickbook = array();

		if ($this->config->get('module_opc_advqb_status')) {
			if ($this->user->hasPermission('access', 'extension/module/opc_advqb')) {
				$adv_quickbook[] = array(
					'name'	   => $this->language->get('text_configuration'),
					'href'     => $this->url->link('extension/module/opc_advqb', 'user_token=' . $this->session->data['user_token'], true),
					'children' => array()
				);
			}

			if ($this->user->hasPermission('access', 'advqb/customer')) {
				$adv_quickbook[] = array(
					'name'	   => $this->language->get('text_customer_advqb'),
					'href'     => $this->url->link('advqb/customer', 'user_token=' . $this->session->data['user_token'], true),
					'children' => array()
				);
			}

			if ($this->user->hasPermission('access', 'advqb/product')) {
				$adv_quickbook[] = array(
					'name'	   => $this->language->get('text_product_advqb'),
					'href'     => $this->url->link('advqb/product', 'user_token=' . $this->session->data['user_token'], true),
					'children' => array()
				);
			}

			if ($this->user->hasPermission('access', 'advqb/order')) {
				$adv_quickbook[] = array(
					'name'	   => $this->language->get('text_order_advqb'),
					'href'     => $this->url->link('advqb/order', 'user_token=' . $this->session->data['user_token'], true),
					'children' => array()
				);
			}

			if ($adv_quickbook) {
				$menus = array(
					'id'       => 'menu-adv-quickbook',
					'icon'	   => 'fa-plug',
					'name'	   => $this->language->get('text_adv_quickbook'),
					'href'     => '',
					'children' => $adv_quickbook
				);
			}
		}

		return $menus;
	}

	public function index() {
		$data = $this->load->language('extension/module/opc_advqb');

		if ($this->request->post) {
			function clean(&$value) {
			    $value = trim($value);
			}

			array_walk_recursive($this->request->post, 'clean');
		}

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('setting/setting');


		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {

			$this->model_setting_setting->editSetting('module_opc_advqb', $this->request->post);

			$this->session->data['success'] = $this->language->get('text_success');

			$this->response->redirect($this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true));
		}

		$opc_error = array(
			'warning',
			'client_key',
			'client_secret',
			'access_token',
			'refresh_token',
			'realmid',
			'slot',
		);

		foreach ($opc_error as $key => $value) {
			if (isset($this->error[$value])) {
				$data['error_'.$value] = $this->error[$value];
			} else {
				$data['error_'.$value] = '';
			}
		}

		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_extension'),
			'href' => $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('extension/module/opc_advqb', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['user_guide'] = $this->url->link('extension/module/opc_advqb/user_guide', 'user_token=' . $this->session->data['user_token'], true);

		$data['action'] = $this->url->link('extension/module/opc_advqb', 'user_token=' . $this->session->data['user_token'], true);

		$data['cancel'] = $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true);

    $opc_module_config = array(
			'status',
			'sandbox',
			'slot',
			'order_mapping',
			'client_key',
			'client_secret',
			'access_token',
			'refresh_token',
			'realmid',
			'auto_sync',
			'tax',
			'asset',
			'income',
			'expense',
			'discount',
			'transaction_prefix',
			'order_status',
			'paid_order_status',
			'date',
		);

    foreach ($opc_module_config as $key => $value) {
      if (isset($this->request->post['module_opc_advqb_'.$value])) {
  			$data['module_opc_advqb_'.$value] = $this->request->post['module_opc_advqb_'.$value];
			} elseif (isset($this->session->data['module_opc_advqb_'.$value])) {
				$data['module_opc_advqb_'.$value] = $this->session->data['module_opc_advqb_'.$value];
  		} else {
  			$data['module_opc_advqb_'.$value] = $this->config->get('module_opc_advqb_'.$value);
  		}
    }

		$this->load->model('localisation/order_status');

		$data['order_statuses'] = $this->model_localisation_order_status->getOrderStatuses();

		$data['connect_button'] = 0;

		if ($this->config->get('module_opc_advqb_client_key') && $this->config->get('module_opc_advqb_client_secret')) {
			$data['connect_button'] = 1;
		}

		if ($this->request->server['HTTPS']) {
			$data['redirect_uri'] = HTTPS_CATALOG . 'index.php?route=account/advqb';
		} else {
			$data['redirect_uri'] = HTTP_CATALOG . 'index.php?route=account/advqb';
		}

		$parameters = array(
		  'client_id' => $this->config->get('module_opc_advqb_client_key'),
		  'scope' => 'com.intuit.quickbooks.accounting',
		  'redirect_uri' => $data['redirect_uri'],
		  'response_type' => 'code',
		  'state' => 'RandomState'
		);

		$data['authorizationRequestUrl'] = 'https://appcenter.intuit.com/connect/oauth2?' . http_build_query($parameters, null, '&', PHP_QUERY_RFC1738);

		$this->registry->set('advqb', new Advqb($this->registry));

		$data['accounts'] = $this->advqb->getAccounts();

		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('extension/module/opc_advqb', $data));
	}

	public function user_guide() {
	  $this->document->setTitle('Advanced QuickBook Online Opencart Connector User Guide');

	  $data['cancel'] = $this->url->link('extension/module/opc_advqb', 'user_token=' . $this->session->data['user_token'], true);

	  $data['user_token'] = $this->session->data['user_token'];

	  $data['header'] = $this->load->controller('common/header');

	  $data['column_left'] = $this->load->controller('common/column_left');

	  $data['footer'] = $this->load->controller('common/footer');

	  $this->response->setOutput($this->load->view('extension/module/opc_advqb_user_guide', $data));
	}

	protected function validate() {
		if (!$this->user->hasPermission('modify', 'extension/module/opc_advqb')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if ($this->request->post['module_opc_advqb_status']) {

			if (!isset($this->request->post['module_opc_advqb_slot']) || $this->request->post['module_opc_advqb_slot'] < 5 || $this->request->post['module_opc_advqb_slot'] > 50) {
				$this->error['slot'] = $this->language->get('error_slot');
			}

			$opc_error = array(
				'client_key',
				'client_secret',
			);

			foreach ($opc_error as $key => $value) {
				if (!isset($this->request->post['module_opc_advqb_'.$value]) || !$this->request->post['module_opc_advqb_'.$value]) {
					$this->error[$value] = $this->language->get('error_'.$value);
				}
			}

			$opc_error = array(
				'access_token',
				'refresh_token',
				'realmid',
			);

			if ($this->config->get('module_opc_advqb_client_key') && $this->config->get('module_opc_advqb_client_secret')) {
				foreach ($opc_error as $key => $value) {
					if (!isset($this->request->post['module_opc_advqb_'.$value]) || !$this->request->post['module_opc_advqb_'.$value]) {
						$this->error[$value] = $this->language->get('error_'.$value);
					}
				}
			}
		}

		return !$this->error;
	}
}
