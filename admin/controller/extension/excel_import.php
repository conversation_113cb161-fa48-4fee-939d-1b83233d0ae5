<?php
header('Cache-Control: no-cache, no-store');
ini_set('memory_limit', '1024M');
ini_set('max_execution_time', 900);
ini_set('error_reporting', E_ALL);
include DIR_SYSTEM.'library/excel_point_tool/PHPExcel.php';
class ControllerExtensionExcelImport extends Controller {
	private $error = array();

	public function index(){
		$this->load->language('catalog/product');
		
		$this->load->language('extension/excel_point');
	

		$this->document->setTitle($this->language->get('heading_title'));
		
		$this->load->model('extension/excel_point');

		$data['heading_title'] = $this->language->get('heading_title');

		$data['text_form'] = !isset($this->request->get['product_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
		
		$data['text_default'] = $this->language->get('text_default');
		$data['text_enabled'] = $this->language->get('text_enabled');
		$data['text_disabled'] = $this->language->get('text_disabled');
		$data['text_yes'] = $this->language->get('text_yes');
		$data['text_no'] = $this->language->get('text_no');
		$data['text_missing'] = $this->language->get('text_missing');
		$data['text_email_already'] = $this->language->get('text_email_already');
		$data['text_password'] = $this->language->get('text_password');
		$data['help_email'] = $this->language->get('help_email');
		$data['help_password'] = $this->language->get('help_password');
		
		$data['entry_store'] = $this->language->get('entry_store');
		$data['entry_import'] = $this->language->get('entry_import');
		$data['entry_language'] = $this->language->get('entry_language');
		
		$data['tab_import'] = $this->language->get('tab_import');
		
		$data['entry_store'] = $this->language->get('entry_store');
		$data['entry_name'] = $this->language->get('entry_name');
		$data['entry_price'] = $this->language->get('entry_price');
		$data['entry_status'] = $this->language->get('entry_status');
		$data['entry_quantity'] = $this->language->get('entry_quantity');
		$data['entry_model'] = $this->language->get('entry_model');
		$data['entry_language'] = $this->language->get('entry_language');
		$data['entry_stock_status'] = $this->language->get('entry_stock_status');
		$data['entry_limit'] = $this->language->get('entry_limit');
		$data['entry_categories'] = $this->language->get('entry_categories');
		$data['entry_manufacturer'] = $this->language->get('entry_manufacturer');
		$data['entry_pricerange'] = $this->language->get('entry_pricerange');
		$data['entry_quantityrange'] = $this->language->get('entry_quantityrange');
		$data['entry_email'] = $this->language->get('entry_email');
		$data['entry_customer_group'] = $this->language->get('entry_customer_group');
		$data['entry_approved'] = $this->language->get('entry_approved');
		$data['entry_ip'] = $this->language->get('entry_ip');
		$data['entry_date_added'] = $this->language->get('entry_date_added');
		$data['entry_return_id'] = $this->language->get('entry_return_id');
		$data['entry_order_id'] = $this->language->get('entry_order_id');
		$data['entry_customer'] = $this->language->get('entry_customer');
		$data['entry_order_status'] = $this->language->get('entry_order_status');
		$data['entry_total'] = $this->language->get('entry_total');
		$data['entry_date_modified'] = $this->language->get('entry_date_modified');
		$data['button_filter'] = $this->language->get('button_filter');
		$data['button_save'] = $this->language->get('button_save');
		$data['button_cancel'] = $this->language->get('button_cancel');
		$data['button_export'] = $this->language->get('button_export');
		
		
		$data['user_token'] = $this->session->data['user_token'];
		
		if(isset($this->error['warning'])){
			$data['error_warning'] = $this->error['warning'];
		}elseif(isset($this->session->data['error_warning'])){
			$data['error_warning'] = $this->session->data['error_warning'];
			unset($this->session->data['error_warning']);
		}else{
			$data['error_warning'] = '';
		}
		
		if(isset($this->session->data['success'])){
			$data['success'] = $this->session->data['success'];
			unset($this->session->data['success']);
		}else{
			$data['success'] = '';
		}
		
		if (isset($this->request->get['filter_name'])) {
			$data['filter_name'] = $this->request->get['filter_name'];
		} else {
			$data['filter_name'] = null;
		}

		if (isset($this->request->get['filter_model'])) {
			$data['filter_model'] = $this->request->get['filter_model'];
		} else {
			$data['filter_model'] = null;
		}

		if (isset($this->request->get['filter_price'])) {
			$data['filter_price'] = $this->request->get['filter_price'];
		} else {
			$data['filter_price'] = null;
		}

		if (isset($this->request->get['filter_quantity'])) {
			$data['filter_quantity'] = $this->request->get['filter_quantity'];
		} else {
			$data['filter_quantity'] = null;
		}

		if (isset($this->request->get['filter_status'])) {
			$data['filter_status'] = $this->request->get['filter_status'];
		} else {
			$data['filter_status'] = null;
		}
		
		if (isset($this->request->get['filter_limit'])) {
			$data['filter_limit'] = $this->request->get['filter_limit'];
		} else {
			$data['filter_limit'] = $this->config->get('config_limit_admin');
		}
		
		$url = '';
		
		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], 'SSL')
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'] . $url, 'SSL')
		);
		
		$this->load->model('localisation/language');
		$data['languages'] = $this->model_localisation_language->getLanguages();
		
		$this->load->model('setting/store');
		$data['stores'] = $this->model_setting_store->getStores();
		
		$this->load->model('localisation/stock_status');
		$data['stock_statuses'] = $this->model_localisation_stock_status->getStockStatuses();
		
		$this->load->model('catalog/category');
		$data['categories'] = $this->model_catalog_category->getCategories();
		
		$this->load->model('catalog/manufacturer');
		$data['manufacturers'] = $this->model_catalog_manufacturer->getManufacturers();
		
		$data['customer_groups'] = $this->model_extension_excel_point->getCustomerGroups();
		
		$this->load->model('localisation/order_status');
		$data['order_statuses'] = $this->model_localisation_order_status->getOrderStatuses();
		
		if (isset($this->request->get['filter_limit'])) {
			$data['filter_limit'] = $this->request->get['filter_limit'];
		} else {
			$data['filter_limit'] = $this->config->get('config_limit_admin');
		}
		
		////Import Start
		$examplefiles = '';
		
		$data['entry_store'] = $this->language->get('entry_store');
		
		$exampleproductfiles = HTTP_CATALOG.'system/excel_point/productexample.xls';
		
		$data['entry_prodimportimport'] = sprintf($this->language->get('entry_prodimportimport'),$exampleproductfiles);
		
		$examplecategoriesfiles = HTTP_CATALOG.'system/excel_point/categoriesexample.xls';
		
		$data['entry_categoriesimport'] = sprintf($this->language->get('entry_categoriesimport'),$examplecategoriesfiles);
		
		$examplemanufiles = HTTP_CATALOG.'system/excel_point/manufacturerexample.xls';
		
		$data['entry_manufacturerimport'] = sprintf($this->language->get('entry_manufacturerimport'),$examplemanufiles);
		
		$examplecustomerfiles = HTTP_CATALOG.'system/excel_point/customerexample.xls';
		$data['text_import_customer'] = sprintf($this->language->get('text_import_customer'),$examplecustomerfiles);
		
		$examplecustomergroupfiles = HTTP_CATALOG.'system/excel_point/customerGroupexample.xls';
		
		$data['entry_customergroupimport'] = sprintf($this->language->get('entry_customergroupimport'),$examplecustomergroupfiles);
		
		$exampleaffillatesfiles = HTTP_CATALOG.'system/excel_point/affiliateslistexample.xls';
		
		$data['text_import_affiliates'] = sprintf($this->language->get('text_import_affiliates'),$exampleaffillatesfiles);
		
		$examplecouponfiles = HTTP_CATALOG.'system/excel_point/couponlistexample.xls';
		$data['entry_couponimport'] = sprintf($this->language->get('entry_couponimport'),$examplecouponfiles);
		
		$exampleusersfiles = HTTP_CATALOG.'system/excel_point/userslistexample.xls';
		$data['entry_userimport'] = sprintf($this->language->get('entry_userimport'),$exampleusersfiles);
		
		$exampleoptionsfiles = HTTP_CATALOG.'system/excel_point/optionslistexample.xls';
		$data['entry_optionimport'] = sprintf($this->language->get('entry_optionimport'),$exampleoptionsfiles);
		
		$exampleusersfiles = HTTP_CATALOG.'system/excel_point/orderexample.xls';
		$data['entry_order_import'] = sprintf($this->language->get('entry_order_import'),$exampleusersfiles);
		
		$exampleproductreviewfiles = HTTP_CATALOG.'system/excel_point/productreviewexample.xls';
		$data['text_import_productreview'] = sprintf($this->language->get('text_import_productreview'),$exampleproductreviewfiles);
		
		$data['entry_language'] = $this->language->get('entry_language');
		$data['text_importtype'] = $this->language->get('text_importtype');
		$data['text_productid'] = $this->language->get('text_productid');
		$data['text_model'] = $this->language->get('text_model');
		$data['productaction'] = $this->url->link('extension/excel_import/importproduct','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['categoriesaction'] = $this->url->link('extension/excel_import/importcategories','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['manufactureaction'] = $this->url->link('extension/excel_import/importmanufacture','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['customergroupaction'] = $this->url->link('extension/excel_import/importcsutomergroup','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['customeraction'] = $this->url->link('extension/excel_import/importcustomer','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['affiliatesaction'] = $this->url->link('extension/excel_import/importaffiliate','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['counponaction'] = $this->url->link('extension/excel_import/importcoupon','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['useraction'] = $this->url->link('extension/excel_import/importuser','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['optionsaction'] = $this->url->link('extension/excel_import/optionsaction','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['orderaction'] = $this->url->link('extension/excel_import/importorder','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['productreviewaction'] = $this->url->link('extension/excel_import/importproductreview','user_token='.$this->session->data['user_token'],'SSL');
		
		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('extension/excel_import', $data));
	}
	
	public function optionsaction(){
		$this->load->language('extension/excel_point');
	    $this->load->model('extension/excel_point');
		 if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()){
			 
		 if(!empty($this->request->post['language_id'])){
			  $language_id = $this->request->post['language_id'];
		 }else{
			  $language_id = '1';
		 }
		 
		  if($this->request->files){
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls'){
					try{
						$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$updateproduct = 0;
					$newproduct = 0;
					
					foreach($allDataInSheet as $k=> $value){
						if($i!=0 && $value['B']){
							$data=array(
							  'option_id' 				=> (isset($value['A']) ? $value['A'] : ''),
							  'option_type' 			=> (isset($value['B']) ? $value['B'] : ''),
							  'language_id'  			=> (isset($value['C']) ? $value['C'] : ''),
							  'option_name'    			=> (isset($value['D']) ? $value['D'] : ''),
							  'sort_order'    			=> (isset($value['E']) ? $value['E'] : ''),
							  'select_language'    		=>  $language_id,
							);
							
							if((int)$value['A']){
								$userresult = $this->model_extension_excel_point->getsoptions($value['A']);
								if($userresult){
									$this->model_extension_excel_point->editoptions($data,$value['A']);
									$updateproduct++;
								}else{
									$this->model_extension_excel_point->addoldoptions($data,$value['A']);
									$newproduct++;
								}
							}else{
								$this->model_extension_excel_point->addoptions($data);
								$newproduct++;
							}
						}
						$i++;
					}
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_option'),$updateproduct,$newproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				} else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	    }
		$this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importorder(){
		$this->load->language('extension/excel_point');
	    $this->load->model('extension/excel_point');
		
		if(($this->request->server['REQUEST_METHOD'] == 'POST')){
			if($this->request->files){
				$file = basename($this->request->files['import']['name']);
				if(!file_exists(DIR_SYSTEM.'webxtemp')){
					mkdir(DIR_SYSTEM.'webxtemp');
				}
				$xfile = DIR_SYSTEM.'webxtemp/'.$file;
				move_uploaded_file($this->request->files['import']['tmp_name'], $xfile);
				$inputFileName = $xfile;
				$extension = pathinfo($inputFileName);
				if($extension['basename']){
				if(isset($extension['extension']) && ($extension['extension']=='xlsx' || $extension['extension']=='xls')){
					PHPExcel_Cell::setValueBinder( new PHPExcel_Cell_AdvancedValueBinder() );
					$inputFileType = 'Excel5';
					$objReader = PHPExcel_IOFactory::createReader($inputFileType);
					$objPHPExcel = $objReader->load($inputFileName);
					$objPHPExcel->getActiveSheet()->setTitle(pathinfo($inputFileName,PATHINFO_BASENAME));
					$loadedSheetNames = $objPHPExcel->getSheetNames();
					$sheetOrderData = $objPHPExcel->getSheet(0)->toArray(null,true,true,true);
					$i=0;
					$order_new = 0;
					$order_update = 0;
					foreach($sheetOrderData as $sheet){
					  if($i!=0){
						$orderproducts=array();
						$sheetproductData = $objPHPExcel->getSheet(1)->toArray(null,true,true,true);
						$op=0;
						foreach($sheetproductData as $PDATA){
						 if($op!=0){
							if($sheet['A']==$PDATA['B']){
							  $productoptions=array();
							  $sheetoptionData = $objPHPExcel->getSheet(2)->toArray(null,true,true,true);
							  $po=0;
							  foreach($sheetoptionData as $option){
								 if($op!=0){
									 if($PDATA['A']==$option['C']){
										$productoptions[]=array(
										  'order_option_id'			=> $option['A'],
										  'order_id'				=> $option['B'],
										  'order_product_id'		=> $option['C'],
										  'product_option_id'		=> $option['D'],
										  'product_option_value_id'	=> $option['E'],
										  'name'					=> $option['F'],
										  'value'					=> $option['G'],
										  'type'					=> $option['H'],
										);
									 }
								 }
							  }
								
							  $orderproducts[]=array(
								'order_product_id'	=> $PDATA['A'],
								'order_id'			=> $PDATA['B'],
								'product_id'		=> $PDATA['C'],
								'name'				=> $PDATA['D'],
								'model'				=> $PDATA['E'],
								'quantity'			=> $PDATA['F'],
								'price'				=> $PDATA['G'],
								'total'				=> $PDATA['H'],
								'tax'				=> $PDATA['I'],
								'reward'			=> $PDATA['J'],
								'productoptions'	=> $productoptions,
							  );
							}
						  }
						 $op++;
						}
						
						//Order Total Sheet
						$sheetotals = $objPHPExcel->getSheet(3)->toArray(null,true,true,true);
						$order_totals=array();
						$ot=0;
						foreach($sheetotals as $totalsheet){
						  if($ot!=0){
							if($sheet['A']==$totalsheet['B']){
								$order_totals[]=array(
								  'order_total_id'	=> $totalsheet['A'],
								  'order_id'		=> $totalsheet['B'],
								  'code'			=> $totalsheet['C'],
								  'title'			=> $totalsheet['D'],
								  'value'			=> $totalsheet['E'],
								  'sort_order'		=> $totalsheet['F'],
								);
							}
						  }
						  $ot++;
						}
						
						//Order History
						$sheehistorys = $objPHPExcel->getSheet(4)->toArray(null,true,true,true);
						$oh=0;
						$orderhistorys=array();
						foreach($sheehistorys as $history){
							if($oh!=0){
								if($sheet['A']==$history['B']){
								  $orderhistorys[]=array(
									'order_history_id'	=> $history['A'],
									'order_id'			=> $history['B'],
									'order_status_id'	=> $history['C'],
									'order_status'		=> $history['D'],
									'notify'			=> $history['E'],
									'comment'			=> $history['F'],
									'date_added'		=> $history['G'],
								  );
								}
						    }
							$oh++;
						}
						
						$sheetvouchers = $objPHPExcel->getSheet(5)->toArray(null,true,true,true);
						$ordervouchers=array();
						$ov=0;
						foreach($sheetvouchers as $voucher){
						  if($ov!=0){
							  if($sheet['A']==$voucher['B']){
								  $ordervouchers[]=array(
									'order_voucher_id' => $voucher['A'],
									'order_id' 		   => $voucher['B'],
									'voucher_id' 	   => $voucher['C'],
									'description' 	   => $voucher['D'],
									'code' 	   		   => $voucher['E'],
									'from_name' 	   => $voucher['F'],
									'from_email' 	   => $voucher['G'],
									'to_name' 	   	   => $voucher['H'],
									'vocher_theme_id'  => $voucher['I'],
									'message'  		   => $voucher['J'],
									'amount'  		   => $voucher['K'],
								  );
							  }
						   }
						   $ov++;
						}
						
						 $orders=array(
						  'order_id' 				=> $sheet['A'],
						  'invoice_no'  			=> $sheet['B'],
						  'invoice_prefix'  		=> $sheet['C'],
						  'store_id' 	 			=> $sheet['D'],
						  'store_name' 	 			=> $sheet['E'],
						  'store_url' 	 			=> $sheet['F'],
						  'customer_id' 			=> $sheet['G'],
						  'customer' 				=> $sheet['H'],
						  'customer_group_id' 		=> $sheet['I'],
						  'firstname' 				=> $sheet['J'],
						  'lastname' 				=> $sheet['K'],
						  'email' 					=> $sheet['L'],
						  'telephone' 				=> $sheet['M'],
						  'fax' 					=> $sheet['N'],
						  'custom_field' 			=> $sheet['O'],
						  'payment_firstname' 		=> $sheet['P'],
						  'payment_lastname' 		=> $sheet['Q'],
						  'payment_company' 		=> $sheet['R'],
						  'payment_address_1' 		=> $sheet['S'],
						  'payment_address_2' 		=> $sheet['T'],
						  'payment_postcode' 		=> $sheet['U'],
						  'payment_city' 			=> $sheet['V'],
						  'payment_zone_id' 		=> $sheet['W'],
						  'payment_zone' 			=> $sheet['X'],
						  'payment_zone_code' 		=> $sheet['Y'],
						  'payment_country_id' 		=> $sheet['Z'],
						  'payment_country' 		=> $sheet['AA'],
						  'payment_iso_code_2' 		=> $sheet['AB'],
						  'payment_iso_code_3' 		=> $sheet['AC'],
						  'payment_address_format'  => $sheet['AD'],
						  'payment_custom_field'    => $sheet['AE'],
						  'payment_method'          => $sheet['AF'],
						  'payment_code'            => $sheet['AG'],
						  'shipping_firstname'      => $sheet['AH'],
						  'shipping_lastname'       => $sheet['AI'],
						  'shipping_company'        => $sheet['AJ'],
						  'shipping_address_1'      => $sheet['AK'],
						  'shipping_address_2'      => $sheet['AL'],
						  'shipping_postcode'       => $sheet['AM'],
						  'shipping_city'           => $sheet['AN'],
						  'shipping_zone_id'        => $sheet['AO'],
						  'shipping_zone'           => $sheet['AP'],
						  'shipping_zone_code'      => $sheet['AQ'],
						  'shipping_country_id'     => $sheet['AR'],
						  'shipping_country'        => $sheet['AS'],
						  'shipping_iso_code_2'     => $sheet['AT'],
						  'shipping_iso_code_3'     => $sheet['AU'],
						  'shipping_address_format' => $sheet['AV'],
						  'shipping_custom_field'   => $sheet['AW'],
						  'shipping_method'         => $sheet['AX'],
						  'shipping_code'           => $sheet['AY'],
						  'comment'                 => $sheet['AZ'],
						  'total'                   => $sheet['BA'],
						  'reward'                  => $sheet['BB'],
						  'order_status_id'         => $sheet['BC'],
						  'affiliate_id'            => $sheet['BD'],
						  'affiliate_firstname'     => $sheet['BE'],
						  'affiliate_lastname'      => $sheet['BF'],
						  'commission'              => $sheet['BG'],
						  'language_id'             => $sheet['BH'],
						  'language_code'           => $sheet['BI'],
						  'language_directory'      => $sheet['BJ'],
						  'currency_id'             => $sheet['BK'],
						  'currency_code'           => $sheet['BL'],
						  'currency_value'          => $sheet['BM'],
						  'ip'                      => $sheet['BN'],
						  'forwarded_ip'            => $sheet['BO'],
						  'user_agent'              => $sheet['BP'],
						  'accept_language'         => $sheet['BQ'],
						  'date_added'              => $sheet['BR'],
						  'date_modified'           => $sheet['BS'],
						  'orderproduct'			=> $orderproducts,
						  'order_total'				=> $order_totals,
						  'orderhistorys'			=> $orderhistorys,
						  'ordervouchers'			=> $ordervouchers,
						  );
						  if((int)$sheet['A']){
							$order_info = $this->model_extension_excel_point->getOrder($sheet['A']);
							if($order_info){
								$this->model_extension_excel_point->editOrder($orders,$sheet['A']);
								$order_update++;
							}else{
								$this->model_extension_excel_point->addorder($orders);
								$order_new++;
							}
						  }
					 }
					$i++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_order_success'),$order_update,$order_new);
				}elseif($extension['extension']=='xml'){
					$order_new = 0;
					$order_update = 0;
					try{
						$xml = simplexml_load_file($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}

					foreach($xml->children() as $k=> $sheet){
						
						$orderproducts=array();
						if(isset($sheet->products->product)){
							foreach($sheet->products->product as $PDATA){
								if($PDATA){
									$productoptions=array();
									if(isset($PDATA->order_product_options->option)){
										foreach($PDATA->order_product_options->option as $option){
											  $productoptions[]=array(
												  'order_option_id'			=> $option->order_product_id,
												  'order_id'				=> $option->order_id,
												  'order_product_id'		=> $option->order_product_id,
												  'product_option_id'		=> $option->product_option_id,
												  'product_option_value_id'	=> $option->product_option_value_id,
												  'name'					=> $option->name,
												  'value'					=> $option->value,
												  'type'					=> $option->type,
											);
										}
									}
									
									$orderproducts[]=array(
										'order_product_id'	=> $PDATA->order_product_id,
										'order_id'			=> $PDATA->order_id,
										'product_id'		=> $PDATA->product_id,
										'name'				=> $PDATA->name,
										'model'				=> $PDATA->model,
										'quantity'			=> $PDATA->quantity,
										'price'				=> $PDATA->price,
										'total'				=> $PDATA->total,
										'tax'				=> $PDATA->tax,
										'reward'			=> $PDATA->reward,
										'productoptions'	=> $productoptions,
									);
								}
							}
						}
						
						//Order Total Sheet
						$order_totals=array();
						if(isset($sheet->totals->total)){
							foreach($sheet->totals->total as $totalsheet){
								if($totalsheet){
									$order_totals[]=array(
										  'order_total_id'	=> $totalsheet->order_total_id,
										  'order_id'		=> $totalsheet->order_id,
										  'code'			=> $totalsheet->code,
										  'title'			=> $totalsheet->title,
										  'value'			=> $totalsheet->value,
										  'sort_order'		=> $totalsheet->sort_order,
									);
								}
							}
						}
						
						
						//Order History
						$orderhistorys=array();
						if(isset($sheet->historys->history)){
							foreach($sheet->historys->history as $history){
								$orderhistorys[]=array(
										'order_history_id'	=> $history->order_history_id,
										'order_id'			=> $history->order_id,
										'order_status_id'	=> $history->order_status_id,
										'order_status'		=> $history->order_status,
										'notify'			=> $history->notify,
										'comment'			=> $history->comment,
										'date_added'		=> $history->date_added,
								);
							}
						}
						
						$ordervouchers=array();
						$ov=0;
						if(isset($sheet->Vouchers->voucher)){
							foreach($sheet->Vouchers->voucher as $voucher){
								if($voucher){
									$ordervouchers[]=array(
										'order_voucher_id' => $voucher->order_voucher_id,
										'order_id' 		   => $voucher->order_id,
										'voucher_id' 	   => $voucher->voucher_id,
										'description' 	   => $voucher->description,
										'code' 	   		   => $voucher->code,
										'from_name' 	   => $voucher->from_name,
										'from_email' 	   => $voucher->from_email,
										'to_name' 	   	   => $voucher->to_name,
										'vocher_theme_id'  => $voucher->vocher_theme_id,
										'message'  		   => $voucher->message,
										'amount'  		   => $voucher->amount,
									);
								}
							}
						}
						
						 $orders=array(
						  'order_id' 				=> $sheet->order_id,
						  'invoice_no'  			=> $sheet->invoice_no,
						  'invoice_prefix'  		=> $sheet->invoice_prefix,
						  'store_id' 	 			=> $sheet->store_id,
						  'store_name' 	 			=> $sheet->store_name,
						  'store_url' 	 			=> $sheet->store_url,
						  'customer_id' 			=>$sheet->customer_id,
						  'customer' 				=> $sheet->customer,
						  'customer_group_id' 		=> $sheet->customer_group_id,
						  'firstname' 				=> $sheet->firstname,
						  'lastname' 				=> $sheet->lastname,
						  'email' 					=> $sheet->email,
						  'telephone' 				=> $sheet->telephone,
						  'fax' 					=> $sheet->fax,
						  'custom_field' 			=> $sheet->custom_field,
						  'payment_firstname' 		=> $sheet->payment_firstname,
						  'payment_lastname' 		=> $sheet->payment_lastname,
						  'payment_company' 		=> $sheet->payment_company,
						  'payment_address_1' 		=> $sheet->payment_address_1,
						  'payment_address_2' 		=> $sheet->payment_address_2,
						  'payment_postcode' 		=> $sheet->payment_postcode,
						  'payment_city' 			=> $sheet->payment_city,
						  'payment_zone_id' 		=> $sheet->payment_zone_id,
						  'payment_zone' 			=> $sheet->payment_zone,
						  'payment_zone_code' 		=> $sheet->payment_zone_code,
						  'payment_country_id' 		=> $sheet->payment_country_id,
						  'payment_country' 		=> $sheet->payment_country,
						  'payment_iso_code_2' 		=> $sheet->payment_iso_code_2,
						  'payment_iso_code_3' 		=> $sheet->payment_iso_code_3,
						  'payment_address_format'  => $sheet->payment_address_format,
						  'payment_custom_field'    => $sheet->payment_custom_field,
						  'payment_method'          => $sheet->payment_method,
						  'payment_code'            => $sheet->payment_code,
						  'shipping_firstname'      => $sheet->shipping_firstname,
						  'shipping_lastname'       => $sheet->shipping_lastname,
						  'shipping_company'        => $sheet->shipping_company,
						  'shipping_address_1'      => $sheet->shipping_address_1,
						  'shipping_address_2'      => $sheet->shipping_address_2,
						  'shipping_postcode'       => $sheet->shipping_postcode,
						  'shipping_city'           => $sheet->shipping_city,
						  'shipping_zone_id'        => $sheet->shipping_zone_id,
						  'shipping_zone'           => $sheet->shipping_zone,
						  'shipping_zone_code'      => $sheet->shipping_zone_code,
						  'shipping_country_id'     => $sheet->shipping_country_id,
						  'shipping_country'        => $sheet->shipping_country,
						  'shipping_iso_code_2'     => $sheet->shipping_iso_code_2,
						  'shipping_iso_code_3'     => $sheet->shipping_iso_code_3,
						  'shipping_address_format' => $sheet->shipping_address_format,
						  'shipping_custom_field'   => $sheet->shipping_custom_field,
						  'shipping_method'         => $sheet->shipping_method,
						  'shipping_code'           => $sheet->shipping_code,
						  'comment'                 => $sheet->comment,
						  'total'                   => $sheet->total,
						  'reward'                  => $sheet->reward,
						  'order_status_id'         => $sheet->order_status_id,
						  'affiliate_id'            => $sheet->affiliate_id,
						  'affiliate_firstname'     => $sheet->affiliate_firstname,
						  'affiliate_lastname'      => $sheet->affiliate_lastname,
						  'commission'              => $sheet->commission,
						  'language_id'             => $sheet->language_id,
						  'language_code'           => $sheet->language_code,
						  'language_directory'      => $sheet->language_directory,
						  'currency_id'             => $sheet->currency_id,
						  'currency_code'           => $sheet->currency_code,
						  'currency_value'          => $sheet->currency_value,
						  'ip'                      => $sheet->ip,
						  'forwarded_ip'            => $sheet->forwarded_ip,
						  'user_agent'              => $sheet->user_agent,
						  'accept_language'         => $sheet->accept_language,
						  'date_added'              => $sheet->date_added,
						  'date_modified'           => $sheet->date_modified,
						  'orderproduct'			=> $orderproducts,
						  'order_total'				=> $order_totals,
						  'orderhistorys'			=> $orderhistorys,
						  'ordervouchers'			=> $ordervouchers,
						  );
						  
						  if((int)$sheet->order_id){
							$order_info = $this->model_extension_excel_point->getOrder($sheet->order_id);
							if($order_info){
								$this->model_extension_excel_point->editOrder($orders,$sheet->order_id);
								$order_update++;
							}else{
								$this->model_extension_excel_point->addorder($orders);
								$order_new++;
							}
						  }
					}
					$this->session->data['success'] = sprintf($this->language->get('text_order_success'),$order_update,$order_new);
				}else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			  }else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
			  }
			}
		}
		
		$this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
		
	}
	
	public function importuser(){
		$this->load->language('extension/excel_point');
	    $this->load->model('extension/excel_point');
		 if(($this->request->server['REQUEST_METHOD'] == 'POST')){
		 if(!empty($this->request->post['password_format'])){
			  $password_format = $this->request->post['password_format'];
		 }else{
			  $password_format = 'P';
		 }
		 
		 
		 if($this->request->files){
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls'){
					try{
						$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$updateproduct = 0;
					$newproduct = 0;
					
					foreach($allDataInSheet as $k=> $value){
						if($i!=0 && $value['B']){
							$data=array(
							  'user_id' 				=> (isset($value['A']) ? $value['A'] : ''),
							  'user_group_id' 			=> (isset($value['B']) ? $value['B'] : ''),
							  'username'  				=> (isset($value['C']) ? $value['C'] : ''),
							  'firstname'    			=> (isset($value['D']) ? $value['D'] : ''),
							  'lastname'    			=> (isset($value['E']) ? $value['E'] : ''),
							  'email'    				=> (isset($value['F']) ? $value['F'] : ''),
							  'image'    				=> (isset($value['G']) ? $value['G'] : ''),
							  'password'    			=> (isset($value['H']) ? $value['H'] : ''),
							  'salt'    				=> (isset($value['I']) ? $value['I'] : ''),
							  'status'    				=> (isset($value['J']) ? $value['J'] : ''),
							  'password_format'    		=> $password_format,
							);
							
							if((int)$value['A']){
								$userresult = $this->model_extension_excel_point->getUser($value['A']);
								if($userresult){
									$this->model_extension_excel_point->editUser($data,$value['A']);
									$updateproduct++;
								}else{
									$this->model_extension_excel_point->addoldUser($data,$value['A']);
									$newproduct++;
								}
							}else{
								$this->model_extension_excel_point->addUser($data);
								$newproduct++;
							}
						}
						$i++;
					}
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_user'),$newproduct,$updateproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				} else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	    }
		$this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importcoupon(){
		$this->load->language('extension/excel_point');
	    $this->load->model('extension/excel_point');
	    if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()){
		  if($this->request->files){
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls'){
					try{
						$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$updateproduct = 0;
					$newproduct = 0;
					
					foreach($allDataInSheet as $k=> $value){
						if($i!=0){
							$data=array(
							  'coupon_id' 				=> (isset($value['A']) ? $value['A'] : ''),
							  'name' 					=> (isset($value['B']) ? $value['B'] : ''),
							  'code'  					=> (isset($value['C']) ? $value['C'] : ''),
							  'type'    				=> (isset($value['D']) ? $value['D'] : ''),
							  'discount'    			=> (isset($value['E']) ? $value['E'] : ''),
							  'total'    				=> (isset($value['F']) ? $value['F'] : ''),
							  'logged'    				=> (isset($value['G']) ? $value['G'] : ''),
							  'shipping'    			=> (isset($value['H']) ? $value['H'] : ''),
							  'products'    			=> (isset($value['I']) ? $value['I'] : ''),
							  'categories'    			=> (isset($value['J']) ? $value['J'] : ''),
							  'date_start'    			=> (isset($value['K']) ? $value['K'] : ''),
							  'date_end'    			=> (isset($value['L']) ? $value['L'] : ''),
							  'uses_total'    			=> (isset($value['M']) ? $value['M'] : ''),
							  'uses_customer'    		=> (isset($value['N']) ? $value['N'] : ''),
							  'status'    				=> (isset($value['O']) ? $value['O'] : ''),
							);
							
							if((int)$value['A']){
								$couponresult = $this->model_extension_excel_point->getCoupon($value['A']);
								if($couponresult){
									$this->model_extension_excel_point->editCoupon($data,$value['A']);
									$updateproduct++;
								}else{
									$this->model_extension_excel_point->addoldCoupon($data,$value['A']);
									$newproduct++;
								}
							}else{
								$this->model_extension_excel_point->addCoupon($data);
								$newproduct++;
							}
						}
						$i++;
					}
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_customergroup'),$newproduct,$updateproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				} else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	    }
		$this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importaffiliate(){
		$this->load->model('extension/excel_point');
		$this->load->language('extension/excel_point');
		if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validatecustomerForm()){
			if(!empty($this->request->post['password_format'])){
			  $password_format = $this->request->post['password_format'];
			}else{
			  $password_format = 'P';
			}
			
			if($this->request->files){
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls') {
					try{
						$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$newentry=0;
					$updateentry=0;
					foreach($allDataInSheet as $value){
						if($i!=0){
							$filter_data=array(
								'affiliate_id'  => $value['A'],
								'firstname'  	=> $value['B'],
								'lastname'   	=> $value['C'],
								'email' 	    => $value['D'],
								'telephone'  	=> $value['E'],
								'fax' 			=> $value['F'],
								'password'   	=> $value['G'],
								'salt'  		=> $value['H'],
								'company' 		=> $value['I'],
								'website' 		=> $value['J'],
								'address_1' 	=> $value['K'],
								'address_2' 	=> $value['L'],
								'city' 			=> trim($value['M']),
								'postcode' 		=> trim($value['N']),
								'country_id' 	=> $value['O'],
								'zone_id' 		=> $value['P'],
								'code' 			=> trim($value['Q']),
								'commission' 	=> trim($value['R']),
								'tax_id' 		=> trim($value['S']),
								'payment_method' => trim($value['T']),
								'cheque_payee' 	=> trim($value['U']),
								'paypal_email' 	=> trim($value['V']),
								'bankname' 		=> trim($value['W']),
								'branch' 		=> trim($value['X']),
								'switchcode' 	=> trim($value['Y']),
								'accountname' 	=> trim($value['Z']),
								'accountnumber' => trim($value['AA']),
								'ip' 			=> trim($value['AB']),
								'status' 		=> $value['AC'],
								//'approved' 		=> $value['AD'],
							);
							
							if((int)$value['A']){
								$affiliateinfo = $this->model_extension_excel_point->getaffiliate($value['A']);
								if($affiliateinfo){
								  $this->model_extension_excel_point->Editaffiliate($filter_data,$password_format,$value['A']);
								  $updateentry++;
							  }else{
								  $return_data = $this->model_extension_excel_point->addoldaffiliate($filter_data,$password_format,$value['A']);
								  $newentry++;
							  }
							}else{
								$return_data = $this->model_extension_excel_point->addAffiliates($filter_data,$password_format);
								$newentry++;
							}
							
						}
						$i++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_affilatessuccess'),$newentry,$updateentry);
				} else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
			
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	  }
	  $this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importcustomer(){
		$this->load->model('extension/excel_point');
		$this->load->language('extension/excel_point');
		if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validatecustomerForm()){
			if(!empty($this->request->post['password_format'])){
			  $password_format = $this->request->post['password_format'];
			}else{
			  $password_format = 'P';
			}
			
			if($this->request->files) {
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls') {
					PHPExcel_Cell::setValueBinder( new PHPExcel_Cell_AdvancedValueBinder() );
					$inputFileType = 'Excel5';
					$objReader = PHPExcel_IOFactory::createReader($inputFileType);
					$objPHPExcel = $objReader->load($inputFileName);
					$objPHPExcel->getActiveSheet()->setTitle(pathinfo($inputFileName,PATHINFO_BASENAME));
					$loadedSheetNames = $objPHPExcel->getSheetNames();
					$sheetOrderData = $objPHPExcel->getSheet(0)->toArray(null,true,true,true);
					$i=0;
					$newentry=0;
					$updateentry=0;
					foreach($sheetOrderData as $value){
						if($i!=0){
							$sheetrewardData = $objPHPExcel->getSheet(1)->toArray(null,true,true,true);
							$rd=0;
							$rewards=array();
							foreach($sheetrewardData as $reward){
							  if($rd!=0){
								if($value['A']==$reward['A']){
								  $rewards[]=array(
									'customer_id'	=> $reward['A'],
									'order_id'		=> $reward['B'],
									'description'	=> $reward['C'],
									'points'		=> $reward['D'],
									'date_added'	=> $reward['E'],
								  );
								}
							  }
							  $rd++;
							}
							
							$sheettranslateData = $objPHPExcel->getSheet(2)->toArray(null,true,true,true);
							$td=0;
							$transactions=array();
							foreach($sheettranslateData as $TData){
							  if($td!=0){
								if($value['A']==$TData['A']){
								  $transactions[]=array(
									'customer_id'	=> $TData['A'],
									'order_id'		=> $TData['B'],
									'description'	=> $TData['C'],
									'amount'		=> $TData['D'],
									'date_added'	=> $TData['E'],
								  );
								}
							  }
							   $td++;
							}
							
							$customer_id  	= $value['A'];
							$firstname  	= $value['B'];
							$lastname   	= $value['C'];
							$email 	    	= $value['D'];
							$password   	= $value['E'];
							$salt   		= $value['F'];
							$telephone  	= $value['G'];
							$fax 			= $value['H'];
							$customergroup_id = $value['I'];
							$company 		= $value['J'];
							$address1 		= $value['K'];
							$address2 		= $value['L'];
							$postcode 		= trim($value['M']);
							$city 			= trim($value['N']);
							$zone_id 		= $value['O'];
							$country_id 	= $value['P'];
							//$approved 		= $value['Q'];
							$newsletter 	= $value['Q'];
							$status 		= $value['R'];
							
							$filter_data=array(
								'firstname' 		=> $firstname,
								'lastname'			=> $lastname,
								'email'				=> $email,
								'password'			=> $password,
								'salt'				=> $salt,
								'telephone'			=> $telephone,
								'fax'				=> $fax,
								'customer_group_id'	=> $customergroup_id,
								'company'			=> $company,
								'address1'			=> $address1,
								'address2'			=> $address2,
								'postcode'			=> $postcode,
								'city'				=> $city,
								'state'				=> $zone_id,
								'country'			=> $country_id,
								//'approved'			=> $approved,
								'newsletter'		=> $newsletter,
								'status'			=> $status,
								'transactions'		=> $transactions,
								'rewards'			=> $rewards,
							);
							
							if((int)$customer_id){
							  $customerinfo = $this->model_extension_excel_point->getcustomer($customer_id);
							  if($customerinfo){
								  $this->model_extension_excel_point->EditCustomer($filter_data,$password_format,$customer_id);
								  $updateentry++;
							  }else{
								  $return_data = $this->model_extension_excel_point->addoldcustomer($filter_data,$password_format,$customer_id);
								  $newentry++;
							  }
							}else{
								$return_data = $this->model_extension_excel_point->addcustomer($filter_data,$password_format);
								$newentry++;
							}
						}
						$i++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_customersuccess'),$newentry,$updateentry);
				}elseif($extension['extension']=='xml'){
					try{
						$xml = simplexml_load_file($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$i=0;
					$newentry=0;
					$updateentry=0;
					foreach($xml->children() as $k=> $value){
							$rewards=array();
							if(isset($value->Rewards->Reward)){
								foreach($value->Rewards->Reward as $reward){
									if($reward){
										$rewards[]=array(
											'customer_id'	=> $reward->customer_id,
											'order_id'		=> $reward->order_id,
											'description'	=> $reward->description,
											'points'		=> $reward->points,
											'date_added'	=> $reward->date_added,
										);
									}
								}
							}
							
							$transactions=array();
							if(isset($value->Transactions->Transaction)){
								foreach($value->Transactions->Transaction as $TData){
									$transactions[]=array(
										'customer_id'	=> $TData->customer_id,
										'order_id'		=> $TData->order_id,
										'description'	=> $TData->description,
										'amount'		=> $TData->amount,
										'date_added'	=> $TData->date_added,
									);
								}
							}
							
							$customer_id  	= $value->customer_id;
							$firstname  	= $value->firstname;
							$lastname   	= $value->lastname;
							$email 	    	= $value->email;
							$password   	= $value->password;
							$salt   		= $value->salt;
							$telephone  	= $value->telephone;
							$fax 			= $value->fax;
							$customergroup_id = $value->customer_group_id;
							$company 		= $value->company;
							$address1 		= $value->address_1;
							$address2 		= $value->address_2;
							$postcode 		= $value->postcode;
							$city 			= $value->city;
							$zone_id 		= $value->zone_id;
							$country_id 	= $value->country_id;
							//$approved 		= $value->approved;
							$newsletter 	= $value->newsletter;
							$status 		= $value->status;
							
							$filter_data=array(
								'firstname' 		=> $firstname,
								'lastname'			=> $lastname,
								'email'				=> $email,
								'password'			=> $password,
								'salt'				=> $salt,
								'telephone'			=> $telephone,
								'fax'				=> $fax,
								'customer_group_id'	=> $customergroup_id,
								'company'			=> $company,
								'address1'			=> $address1,
								'address2'			=> $address2,
								'postcode'			=> $postcode,
								'city'				=> $city,
								'state'				=> $zone_id,
								'country'			=> $country_id,
								//'approved'			=> $approved,
								'newsletter'		=> $newsletter,
								'status'			=> $status,
								'transactions'		=> $transactions,
								'rewards'			=> $rewards,
							);
							
							if((int)$customer_id){
							  $customerinfo = $this->model_extension_excel_point->getcustomer($customer_id);
							  if($customerinfo){
								  $this->model_extension_excel_point->EditCustomer($filter_data,$password_format,$customer_id);
								  $updateentry++;
							  }else{
								  $return_data = $this->model_extension_excel_point->addoldcustomer($filter_data,$password_format,$customer_id);
								  $newentry++;
							  }
							}else{
								$return_data = $this->model_extension_excel_point->addcustomer($filter_data,$password_format);
								$newentry++;
							}
						}
						$i++;
					
					$this->session->data['success'] = sprintf($this->language->get('text_customersuccess'),$newentry,$updateentry);
				} else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
			
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	  }
	  $this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importcsutomergroup(){
		$this->load->language('extension/excel_point');
	    $this->load->model('extension/excel_point');
	    if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()){
		  if($this->request->files){
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls'){
					try{
						$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$updateproduct = 0;
					$newproduct = 0;
					
					foreach($allDataInSheet as $k=> $value){
						if($i!=0){
							$data=array(
							  'customer_group_id' 		=> (isset($value['A']) ? $value['A'] : ''),
							  'name' 					=> (isset($value['B']) ? $value['B'] : ''),
							  'description'  			=> (isset($value['C']) ? $value['C'] : ''),
							  'approval'    			=> (isset($value['D']) ? $value['D'] : ''),
							  'sort_order'    			=> (isset($value['E']) ? $value['E'] : ''),
							);
							
							if((int)$value['A']){
								$customergroup = $this->model_extension_excel_point->getCustomerGroup($value['A']);
								if($customergroup){
									$this->model_extension_excel_point->editCustomerGroup($data,$value['A']);
									$updateproduct++;
								}else{
									$this->model_extension_excel_point->addoldCustomerGroup($data,$value['A']);
									$newproduct++;
								}
							}else{
								$this->model_extension_excel_point->addCustomerGroup($data);
								$newproduct++;
							}
						}
						$i++;
					}
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_customergroup'),$newproduct,$updateproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				} else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	    }
		$this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importmanufacture(){
	    $this->load->language('extension/excel_point');
	    $this->load->model('extension/excel_point');
	    $this->load->model('catalog/manufacturer');
		$this->load->model('localisation/language');
		
		if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()){
			if(!empty($this->request->post['store_id'])){
				$store_id = $this->request->post['store_id'];
			}else{
				$store_id = 0;
			}
			if(!empty($this->request->post['language_id'])){
					$language_id = $this->request->post['language_id'];
				}else{
					$language_id = $this->config->get('config_langauge_id');
				}
			
				//$languages = $this->model_localisation_language->getLanguages();
		
		  if($this->request->files){
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls'){
					try{
						$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$updateproduct = 0;
					$newproduct = 0;
					
					foreach($allDataInSheet as $k=> $value){
						if($i!=0){
							/* $stores=array();
							$storesx = explode(',',$value['B']);
							foreach($storesx as $store){
							  $stores[]= $this->model_extension_excel_point->getstoreidbyname($store);
							} */
							
							$data=array(
							  'manufacture_id' 		=> (isset($value['A']) ? $value['A'] : ''),
							  'store_id' 				=> $store_id,
							  'name' 				=> (isset($value['C']) ? $value['C'] : ''),
							  'keyword'  			=> (isset($value['D']) ? $value['D'] : ''),
							  'image'    			=> (isset($value['E']) ? $value['E'] : ''),
							  'sort_order'    		=> (isset($value['F']) ? $value['F'] : ''),
							);
							
							if((int)$value['A']){
								$manufacture = $this->model_catalog_manufacturer->getManufacturer($value['A']);
								if($manufacture){
									$this->model_extension_excel_point->editmanufacturer($data,$value['A'],$language_id);
									$updateproduct++;
								}else{
									$this->model_extension_excel_point->addoldmanufacturer($data,$value['A'],$language_id);
									$newproduct++;
								}
							}else{
								$this->model_extension_excel_point->addmanufacturer($data,$language_id);
								$newproduct++;
							}
						}
						$i++;
					}
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_manufacture'),$newproduct,$updateproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				} else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	    }
		$this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importcategories(){
	  $this->load->language('extension/excel_point');
	  $this->load->model('extension/excel_point');
	  $this->load->model('extension/product_import');
	  $this->load->model('catalog/category');
	  if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()){
		  if($this->request->files){
			if(!empty($this->request->post['store_id'])){
				$store_id = $this->request->post['store_id'];
			}else{
				$store_id = 0;
			}
			
			if(!empty($this->request->post['language_id'])){
				$language_id = $this->request->post['language_id'];
			}else{
				$language_id = $this->config->get('config_langauge_id');
			}
				
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls' || $extension['extension']=='csv') {
					try{
						if($extension['extension']=='csv'){
							$inputFileType = 'CSV';
							$objReader = PHPExcel_IOFactory::createReader($inputFileType);
							$objPHPExcel = $objReader->load($inputFileName);
						}else{
							$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
						}
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$updateproduct = 0;
					$newproduct = 0;
					
					foreach($allDataInSheet as $k=> $value){
						if($i!=0){
							
							//Image
							$imagen = str_replace(' ','_',$value['D']);
							$mainimage = $value['K'];
							if(!empty($value['K'])){
							  $value['K'] = str_replace('?dl=0','?raw=1',$value['K']);
							  $mainimage = $this->model_extension_product_import->fetchingimage($value['K'],$imagen);	
							}
							
							$data=array(
							  'category_id' 		=> (isset($value['A']) ? $value['A'] : ''),
							  'language_id' 		=> $language_id,
							  'store_id' 			=> $store_id,
							  'name' 				=> (isset($value['D']) ? $value['D'] : ''),
							  'description' 		=> (isset($value['E']) ? $value['E'] : ''),
							  'meta_title'  		=> (isset($value['F']) ? $value['F'] : ''),
							  'meta_description'    => (isset($value['G']) ? $value['G'] : ''),
							  'meta_keyword'    	=> (isset($value['H']) ? $value['H'] : ''),
							  'parent_id'    		=> (isset($value['I']) ? $value['I'] : ''),
							  'keyword'    			=> (isset($value['J']) ? $value['J'] : ''),
							  'image'    			=> $mainimage,
							  'top'    				=> (isset($value['L']) ? $value['L'] : ''),
							  'column'    			=> (isset($value['M']) ? $value['M'] : ''),
							  'sort_order'    		=> (isset($value['N']) ? $value['N'] : ''),
							  'status'    			=> (isset($value['O']) ? $value['O'] : ''),
							);
							
							if((int)$value['A']){
								$categories = $this->model_catalog_category->getCategory($value['A']);
								if($categories){
									$this->model_extension_excel_point->editcategories($data,$value['A'],$language_id,$store_id);
									$updateproduct++;
								}else{
									$this->model_extension_excel_point->addoldcategories($data,$language_id,$store_id,$value['A']);
									$newproduct++;
								}
							}else{
								$this->model_extension_excel_point->addcategories($data,$language_id,$store_id);
								$newproduct++;
							}
						}
						$i++;
					}
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_categories'),$newproduct,$updateproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				} elseif($extension['extension']=='xml'){
					try{
						$xml = simplexml_load_file($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$i=0;
					$updateproduct = 0;
					$newproduct = 0;					
					foreach($xml->children() as $k=> $value){
					
							if($value->category_name){
								//Image
								$imagen = str_replace(' ','_',$value->category_name);
								$mainimage = $value->Image;
								if(!empty($value->Image)){
								  $value->Image = str_replace('?dl=0','?raw=1',$value->Image);
								  $mainimage = $this->model_extension_product_import->fetchingimage($value->Image,$imagen);	
								}
								
								$data=array(
								  'category_id' 		=> (isset($value->category_id) ? $value->category_id : ''),
								  'language_id' 		=> $language_id,
								  'store_id' 			=> $store_id,
								  'name' 				=> $value->category_name,
								  'description' 		=> $value->description,
								  'meta_title'  		=> $value->meta_title,
								  'meta_description'    => $value->meta_description,
								  'meta_keyword'    	=> $value->meta_keyword,
								  'parent_id'    		=> $value->parent_id,
								  'keyword'    			=> $value->keyword,
								  'image'    			=> $mainimage,
								  'top'    				=> $value->Top,
								  'column'    			=> $value->Column,
								  'sort_order'    		=> $value->Sort_Order,
								  'status'    			=> $value->Status,
								);
								
								if((int)$value->category_id){
									$categories = $this->model_catalog_category->getCategory($value->category_id);
									if($categories){
										$this->model_extension_excel_point->editcategories($data,$value->category_id);
										$updateproduct++;
									}else{
										$this->model_extension_excel_point->addoldcategories($data,$value->category_id);
										$newproduct++;
									}
								}else{
									$this->model_extension_excel_point->addcategories($data);
									$newproduct++;
								}
							}
					
						$i++;
					}
					
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_categories'),$newproduct,$updateproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				}else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	    }
		$this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importproduct(){
		$this->load->language('extension/excel_point');
		$this->load->model('extension/product_import');
		$this->load->model('catalog/product');
		if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
			if($this->request->files) {
				if(!empty($this->request->post['store_id'])){
					$store_id = $this->request->post['store_id'];
				}else{
					$store_id = 0;
				}
				
				if(!empty($this->request->post['language_id'])){
					$language_id = $this->request->post['language_id'];
				}else{
					$language_id = $this->config->get('config_langauge_id');
				}
				
				
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls' || $extension['extension']=='csv') {
					try{
						if($extension['extension']=='csv'){
							$inputFileType = 'CSV';
							$objReader = PHPExcel_IOFactory::createReader($inputFileType);
							$objPHPExcel = $objReader->load($inputFileName);
						}else{
							$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
						}
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$updateproduct = 0;
					$newproduct = 0;
					
					$this->load->model('localisation/tax_class');
					foreach($allDataInSheet as $k=> $value){
						if($i!=0){
							if($value['D']){
						
							if(!empty($value['Z'])){
								 $value['Z'] = str_replace(' ','_',$value['Z']);
							}
							
							//Categories
							$categoryids=array();
							if(!empty($value['AK'])){
								 $categoryids = explode(',',$value['AK']);
							}
							
							//Filter
							$filters=array();
							if(!empty($value['AL'])){
								$filters = $this->model_extension_product_import->checkFilter($value['AL']);
							}
							
							//Download
							$downloads = array();
							if(!empty($value['AM'])){
								$downloads = explode(',',trim($value['AM']));
							}
							
							//Relaled Products
							$relaled_products = array();
							if(!empty($value['AN'])){
								$relaled_products = explode(',',trim($value['AN']));
							}
							
							//Attribute Group
							$attributes = array();
							if(!empty($value['AO'])){
								$attributes = $this->model_extension_product_import->checkAttribute($value['AO']);
							}
							
							//Options
							$options = array();
							if(!empty($value['AP'])){
								$options = $this->model_extension_product_import->checkoptions($value['AP']);
							}
							
							//Discount
							$discounts = array();
							if(!empty($value['AR'])){
								$discounts = $this->model_extension_product_import->checkdiscount($value['AR']);
							}
							
							//Specail
							$specails = array();
							if(!empty($value['AS'])){
								$specails = $this->model_extension_product_import->checkspecial($value['AS']);
							}
							
							//main Image
							$imagen = str_replace(' ','_',$value['D']);
							$mainimage = $value['K'];
							if(!empty($value['K'])){
							  $value['K'] = str_replace('?dl=0','?raw=1',$value['K']);
							  $mainimage = $this->model_extension_product_import->fetchingimage($value['K'],$imagen);	
							}
							
							//Image
							$images = array();
							if(!empty($value['AT'])){
								$ic=1;
								foreach(explode(';',trim($value['AT'])) as $imageurl){
								  $imageurl = str_replace('?dl=0','?raw=1',$imageurl);
								  $imagename = $imagen.$ic++;
								  $images[] = $this->model_extension_product_import->fetchingimage($imageurl,$imagename);
								}
							}
							
							//Options Required
							$optionsrequired = array();
							if(!empty($value['AQ'])){
								$optionsrequired = $this->model_extension_product_import->checkoptionsrequred($value['AQ']);
							}
							
							$importdata=array(
							  'name' 	 			=> $value['D'],
							  'model'  	 			=> $value['E'],
							  'description' 		=> $value['F'],
							  'meta_titile' 		=> $value['G'],
							  'meta_description' 	=> $value['H'],
							  'meta_keyword' 		=> $value['I'],
							  'tag' 				=> $value['J'],
							  'image' 				=> $mainimage,
							  'sku' 				=> $value['L'],
							  'upc' 				=> $value['M'],
							  'ean' 				=> $value['N'],
							  'jan' 				=> $value['O'],
							  'isbn' 				=> $value['P'],
							  'mpn' 				=> $value['Q'],
							  'location' 			=> $value['R'],
							  'price' 				=> $value['S'],
							  'tax_class_id' 		=> $value['T'],
							  'quantity' 			=> $value['U'],
							  'minimum' 			=> $value['V'],
							  'subtract' 			=> $value['W'],
							  'stock_status_id' 	=> $value['X'],
							  'shipping' 			=> $value['Y'],
							  'keyword' 			=> $value['Z'],
							  'date_available' 		=> ($value['AA'] ? $value['AA'] : date('Y-m-d')),
							  'length' 				=> $value['AB'],
							  'length_class_id' 	=> $value['AC'],
							  'width' 				=> $value['AD'],
							  'height' 				=> $value['AE'],
							  'weight' 				=> $value['AF'],
							  'weight_class_id' 	=> $value['AG'],
							  'status' 				=> $value['AH'],
							  'sort_order' 			=> $value['AI'],
							  'manufacturer_id' 	=> $value['AJ'],
							  'categories'			=> array_unique($categoryids),
							  'filters'				=> array_unique($filters),
							  'downloads' 			=> $downloads,
							  'relaled_products' 	=> $relaled_products,
							  'attributes'			=> $attributes,
							  'options'				=> $options,
							  'discounts'			=> $discounts,
							  'specails'			=> $specails,
							  'images'				=> $images,
							  'points'				=> $value['AU'],
							  'viewed'				=> $value['AV'],
							  'optionsrequired'		=> $optionsrequired,
							);
							
							if($this->request->post['importtype']==2){
							 $product_id = $this->model_extension_product_import->getproductIDbymodel($value['E']);
								 if($product_id){
									 $this->model_extension_product_import->Editproduct($importdata,$product_id,$language_id,$store_id);
									 $updateproduct++;
								 }else{
									 $this->model_extension_product_import->addproduct($importdata,$language_id,$store_id);
									 $newproduct++;
								 }
							}else{
								if((int)$value['A']){
								$product_info = $this->model_catalog_product->getProduct($value['A']);
									if($product_info){
										$this->model_extension_product_import->Editproduct($importdata,$value['A'],$language_id,$store_id);
										$updateproduct++;
									}else{
										$this->model_extension_product_import->addoldproduct($importdata,$language_id,$store_id,$value['A']);
										$newproduct++;
									}
								}else{
									$this->model_extension_product_import->addproduct($importdata,$language_id,$store_id);
									$newproduct++;
								}
							}
						 }
						}
						$i++;
					}
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_product'),$newproduct,$updateproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				}elseif($extension['extension']=='xml'){
					try{
						$xml = simplexml_load_file($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}

					$i=0;
					$updateproduct = 0;
					$newproduct = 0;					
					$this->load->model('localisation/tax_class');
					foreach($xml->children() as $k=> $value){
						 if($value->Name){
								if(!empty($value->SEO)){
									 $value->SEO = str_replace(' ','_',$value->SEO);
								}
							
							//Categories
							$categoryids=array();
							if(!empty($value->Categories_IDs)){
								 $categoryids = explode(',',$value->Categories_IDs);
							}
							
							//Filter
							$filters=array();
							if(!empty($value->Filter_Data)){
								$filters = $this->model_extension_product_import->checkFilter($value->Filter_Data);
							}
							
							//Download
							$downloads = array();
							if(!empty($value->Downloads)){
								$downloads = explode(',',trim($value->Downloads));
							}
							
							//Relaled Products
							$relaled_products = array();
							if(!empty($value->Related)){
								$relaled_products = explode(',',trim($value->Related));
							}
							
							//Attribute Group
							$attributes = array();
							if(!empty($value->Attribute)){
								$attributes = $this->model_extension_product_import->checkAttribute($value->Attribute);
							}
							
							//Options
							$options = array();
							if(!empty($value->Options)){
								$options = $this->model_extension_product_import->checkoptions($value->Options);
							}
							
							//Discount
							$discounts = array();
							if(!empty($value->Discounts)){
								$discounts = $this->model_extension_product_import->checkdiscount($value->Discounts);
							}
							
							//Specail
							$specails = array();
							if(!empty($value->Specials)){
								$specails = $this->model_extension_product_import->checkspecial($value->Specials);
							}
							
							//main Image
							$imagen = str_replace(' ','_',$value->Image);
							$mainimage = $value->Image;
							if(!empty($value->Image)){
							  $value->Image = str_replace('?dl=0','?raw=1',$value->Image);
							  $mainimage = $this->model_extension_product_import->fetchingimage($value->Image,$imagen);	
							}
							
							//Image
							$images = array();
							if(!empty($value->SubImages)){
								$ic=1;
								foreach(explode(';',trim($value->SubImages)) as $imageurl){
								  $imageurl = str_replace('?dl=0','?raw=1',$imageurl);
								  $imagename = $imagen.$ic++;
								  $images[] = $this->model_extension_product_import->fetchingimage($imageurl,$imagename);
								}
							}
							
							//Options Required
							$optionsrequired = array();
							if(!empty($value->Option_Required)){
								$optionsrequired = $this->model_extension_product_import->checkoptionsrequred($value->Option_Required);
							}
							
							$importdata=array(
							  'name' 	 			=> $value->Name,
							  'model'  	 			=> $value->Model,
							  'description' 		=> $value->Description,
							  'meta_titile' 		=> $value->MetaTitle,
							  'meta_description' 	=> $value->MetaDescription,
							  'meta_keyword' 		=> $value->MetaKeyword,
							  'tag' 				=> $value->Tag,
							  'image' 				=> $mainimage,
							  'sku' 				=> $value->SKU,
							  'upc' 				=> $value->UPC,
							  'ean' 				=> $value->EAN,
							  'jan' 				=> $value->JAN,
							  'isbn' 				=> $value->ISBN,
							  'mpn' 				=> $value->MPN,
							  'location' 			=> $value->Location,
							  'price' 				=> $value->Price,
							  'tax_class_id' 		=> $value->Tax_Class_ID,
							  'quantity' 			=> $value->Quantity,
							  'minimum' 			=> $value->Minimum,
							  'subtract' 			=> $value->Subtract,
							  'stock_status_id' 	=> $value->Stock_Status_ID,
							  'shipping' 			=> $value->Shipping,
							  'keyword' 			=> $value->SEO,
							  'date_available' 		=> ($value->Date_Available ? $value->Date_Available : date('Y-m-d')),
							  'length' 				=> $value->length,
							  'length_class_id' 	=> $value->Length_Class_ID,
							  'width' 				=> $value->Width,
							  'height' 				=> $value->Height,
							  'weight' 				=> $value->Weight,
							  'weight_class_id' 	=> $value->Weight_Class_ID,
							  'status' 				=> $value->Status,
							  'sort_order' 			=> $value->Sort_Order,
							  'manufacturer_id' 	=> $value->Manufacturer_ID,
							  'categories'			=> array_unique($categoryids),
							  'filters'				=> array_unique($filters),
							  'downloads' 			=> $downloads,
							  'relaled_products' 	=> $relaled_products,
							  'attributes'			=> $attributes,
							  'options'				=> $options,
							  'discounts'			=> $discounts,
							  'specails'			=> $specails,
							  'images'				=> $images,
							  'points'				=> $value->Points,
							  'viewed'				=> $value->Viewed,
							  'optionsrequired'		=> $optionsrequired,
							);
							
							if($this->request->post['importtype']==2){
							 $product_id = $this->model_extension_product_import->getproductIDbymodel($value->Model);
								 if($product_id){
									 $this->model_extension_product_import->Editproduct($importdata,$product_id,$language_id,$store_id);
									 $updateproduct++;
								 }else{
									 $this->model_extension_product_import->addproduct($importdata,$language_id,$store_id);
									 $newproduct++;
								 }
							}else{
								if((int)$value->product_id){
								$product_info = $this->model_catalog_product->getProduct($value->product_id);
									if($product_info){
										$this->model_extension_product_import->Editproduct($importdata,$value->product_id,$language_id,$store_id);
										$updateproduct++;
									}else{
										$this->model_extension_product_import->addoldproduct($importdata,$language_id,$store_id,$value->product_id);
										$newproduct++;
									}
								}else{
									$this->model_extension_product_import->addproduct($importdata,$language_id,$store_id);
									$newproduct++;
								}
							}
						 }
						
						$i++;						
					}
					if($newproduct || $updateproduct){
						$this->session->data['success'] = sprintf($this->language->get('text_success_product'),$newproduct,$updateproduct);
					}
				
					if(!$newproduct && !$updateproduct){
						$this->session->data['error_warning'] = $this->language->get('text_no_data');
					}
				}else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
			
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
		}
		$this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	public function importproductreview(){
		$this->load->model('extension/excel_point');
		$this->load->language('extension/excel_point');
		if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()){
						
			if($this->request->files) {
			$file = basename($this->request->files['import']['name']);
			move_uploaded_file($this->request->files['import']['tmp_name'], $file);
			$inputFileName = $file;
			$extension = pathinfo($inputFileName);
			if($extension['basename']){
				if($extension['extension']=='xlsx' || $extension['extension']=='xls') {
					try{
						$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
					}catch(Exception $e){
						die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
					}
					$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
					$i=0;
					$newentry=0;
					$updateentry=0;
					foreach($allDataInSheet as $value){

						if($i!=0){
							$filter_data=array(
								'review_id' 	=> $value['A'],
								'product_id'  	=> $value['B'],
								'customer_id'  	=> $value['C'],
								'author' 	   	=> $value['D'],
								'text'   		=> $value['E'],
								'rating'   		=> $value['F'],
								'status'  		=> $value['G'],
								'date_added' 	=> $value['H'],
								'date_modified'	=> $value['I']
							);
							if((int)$value['A']){
								$return_data = $this->model_extension_excel_point->getReview($value['A']);
								if($return_data){
									 $this->model_extension_excel_point->editproductreview($filter_data);
									 $updateentry++;
								}else{
									 $this->model_extension_excel_point->addexsitproductreview($filter_data);
									 $newentry++;
								}
							}else{
							  $this->model_extension_excel_point->addproductreview($filter_data);
							  $newentry++;
							}
						}
						$i++;
					}
					$this->session->data['success'] = sprintf($this->language->get('text_productreviewsuccess'),$newentry,$updateentry);
				} else{
					$this->session->data['error_warning'] = $this->language->get('error_warning');
				}
			}else{
				$this->session->data['error_warning'] = $this->language->get('error_warning');
			}
			if($inputFileName){
				unlink($inputFileName);
			}
			
		  }else{
			$this->session->data['error_warning'] = $this->language->get('error_warning');
		  }
	  }
	  $this->response->redirect($this->url->link('extension/excel_import', 'user_token=' . $this->session->data['user_token'], 'SSL'));
	}
	
	protected function validate(){
		if(!$this->user->hasPermission('modify', 'extension/excel_import')){
			$this->error['warning'] = $this->language->get('error_permission');
			$this->session->data['error_warning'] = $this->language->get('error_permission');
		}
		return !$this->error;
	}
	
	protected function validatecustomerForm(){
		if(!$this->user->hasPermission('modify', 'extension/excel_import')){
			$this->error['warning'] = $this->language->get('error_permission');
			$this->session->data['error_warning'] = $this->language->get('error_permission');
		}
		
		if(empty($this->request->post['password_format'])){
			$this->error['warning'] = $this->language->get('error_password_format');
			$this->session->data['error_warning'] = $this->error['warning'];
		}
		
		return !$this->error;
	}
}