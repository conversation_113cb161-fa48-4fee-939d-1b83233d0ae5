<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionShippingUpsshipping file
 *
 * @category Shipping_Controller
 */

class ControllerExtensionShippingUpsshipping extends Controller
{
    private $_error = [];
    private $_shipping_upsshipping_status = 'shipping_upsshipping_status';
    private $_shipping_upsshipping_sort_order = 'shipping_upsshipping_sort_order';
    private $_shipping_upsshipping_cost = 'shipping_upsshipping_cost';
    private $_shipping_upsshipping_tax_class_id = 'shipping_upsshipping_tax_class_id';
    private $_type = '&type=shipping';
    private $_warning = 'warning';
    private $_marketplace = 'marketplace/extension';
    private $_upsshipping = 'extension/shipping/upsshipping';

    /**
     * ControllerExtensionShippingUpsshipping index
     *
     * @return null
     */
    public function index()
    {
        //language
        $this->load->language($this->_upsshipping);
        //setTitle
        $this->document->setTitle($this->language->get('heading_title'));
        //token link
        $user_token = 'user_token=' . $this->session->data['user_token'];
        $data = new\ stdclass();
        //setting model
        $this->load->model('setting/setting');
        //request post
        if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
            $this->model_setting_setting->editSetting('shipping_upsshipping', $this->request->post);
            $this->session->data['success'] = $this->language->get('text_success');
            $this->response->redirect($this->url->link($this->_marketplace, $user_token . $this->_type, true));
        }
        //error warning
        if (isset($this->_error[$this->_warning])) {
            $data->error_warning = $this->_error[$this->_warning];
        } else {
            $data->error_warning = '';
        }
        //extension/shipping/upsshipping
        $data->action = $this->url->link($this->_upsshipping, $user_token, true);
        //marketplace/extension
        $data->cancel = $this->url->link($this->_marketplace, $user_token . $this->_type, true);
        //shipping_ups_cost
        if (isset($this->request->post[$this->_shipping_upsshipping_cost])) {
            $data->shipping_upsshipping_cost = $this->request->post[$this->_shipping_upsshipping_cost];
        } else {
            $data->shipping_upsshipping_cost = $this->config->get($this->_shipping_upsshipping_cost);
        }
        //shipping_ups_class_id
        if (isset($this->request->post[$this->_shipping_upsshipping_tax_class_id])) {
            $data->shipping_upsshipping_tax_class_id = $this->request->post[$this->_shipping_upsshipping_tax_class_id];
        } else {
            $data->shipping_upsshipping_tax_class_id = $this->config->get($this->_shipping_upsshipping_tax_class_id);
        }
        //model localisation tax_class
        $this->load->model('localisation/tax_class');
        $data->tax_classes = $this->model_localisation_tax_class->getTaxClasses();
        //model localisation geo_zone
        $this->load->model('localisation/geo_zone');
        $data->geo_zones = $this->model_localisation_geo_zone->getGeoZones();
        //shipping_ups_status
        if (isset($this->request->post[$this->_shipping_upsshipping_status])) {
            $data->shipping_upsshipping_status = $this->request->post[$this->_shipping_upsshipping_status];
        }
        //shipping_ups_order
        if (isset($this->request->post[$this->_shipping_upsshipping_sort_order])) {
            $data->shipping_upsshipping_sort_order = $this->request->post[$this->_shipping_upsshipping_sort_order];
        }
        //General
        $data->header = $this->load->controller('common/header');
        $data->column_left = $this->load->controller('common/column_left');
        $data->footer = $this->load->controller('common/footer');
        //sort_order
        $data->entry_sort_order = $this->language->get('entry_sort_order');
        $data->entry_status = $this->language->get('entry_status');
        $datas = (array)$data;
        $this->response->setOutput($this->load->view($this->_upsshipping, $datas));
    }

    /**
     * ControllerExtensionModuleUpsmodule validate
     *
     * @return null
     */
    protected function validate()
    {
        //check validate
        if (!$this->user->hasPermission('modify', $this->_upsshipping)) {
            $this->_error[$this->_warning] = $this->language->get('error_permission');
        }
        return !$this->_error;
    }
}
