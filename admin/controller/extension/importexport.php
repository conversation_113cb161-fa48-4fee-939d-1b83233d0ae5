<?php
header('Cache-Control: no-cache, no-store');
ini_set('memory_limit', '1024M');
ini_set('max_execution_time', 900);
ini_set('error_reporting', E_ALL);
include DIR_SYSTEM.'library/excel_point_tool/PHPExcel.php';
class ControllerExtensionImportexport extends Controller {
	private $error = array();
	
	public function __construct($registry){
		 parent::__construct($registry);
		$path = 'exceltool/';
		if (!is_dir(DIR_UPLOAD . $path)) {
			mkdir(DIR_UPLOAD . $path, 0777);
		}
		define('DIR_EXCEL', DIR_UPLOAD . $path);
	}

	public function index() {
		$this->load->language('extension/importexport');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('setting/module');
		$this->load->model('extension/wimportexport');

		if (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}
		
		$this->model_extension_wimportexport->CreateTable();

		if (isset($this->error['name'])) {
			$data['error_name'] = $this->error['name'];
		} else {
			$data['error_name'] = '';
		}
		
		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_extension'),
			'href' => $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true)
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('extension/importexport', 'user_token=' . $this->session->data['user_token'], true)
		);

		$data['action'] = $this->url->link('extension/importexport', 'user_token=' . $this->session->data['user_token'], true);

		$data['cancel'] = $this->url->link('marketplace/extension', 'user_token=' . $this->session->data['user_token'] . '&type=module', true);
		
		$data['backupdb'] = $this->url->link('extension/importexport/backupdb', 'user_token=' . $this->session->data['user_token'], true);
		
		$data['addexport'] = $this->url->link('extension/importexport/addexport', 'user_token=' . $this->session->data['user_token'], true);
		
		$data['addimport'] = $this->url->link('extension/importexport/addimport', 'user_token=' . $this->session->data['user_token'], true);
		
		$data['user_token'] = $this->session->data['user_token'];
		
		$this->load->model('user/user');
		
		$data['users'] = $this->model_user_user->getUsers();
		
		$this->load->model('extension/wimportexport');
		
		$cases = $this->model_extension_wimportexport->getCases();
		$data['cases'] = array();
		foreach($cases as $case){
			$data['cases'][$case['tablename']] = array(
				'excel_id' => $case['excel_import_export_id'],
				'name' => $case['name'],
				'tablename' => $case['tablename'],
				'table' => ucfirst($case['tablename']),
				'total' => $this->model_extension_wimportexport->getTotalCases($filter_data = array('filter_table' => $case['tablename']))
			);
		}
		
		$data['caseslists'] = array();
		foreach($cases as $case){
			$data['caseslists'][] = array(
				'excel_id' => $case['excel_import_export_id'],
				'name' => $case['name'],
				'type' => $case['type'],
				'casename' 				=> substr($case['name'],0,1),
				'formdata' => json_decode($case['formdata'],true),
				'date' 				=> date('d M Y', strtotime($case['date_added'])),
			);
		}
		
		$data['tables'] = array(
			array('value' => 'category','name' => 'Categories'),
			array('value' => 'product','name' => 'Products'),
			array('value' => 'filter','name' => 'Filters'),
			array('value' => 'filter_group','name' => 'Filter Groups'),
			array('value' => 'attribute','name' => 'Attributes'),
			array('value' => 'attribute_group','name' => 'Attribute Groups'),
			array('value' => 'option','name' => 'Options'),
			array('value' => 'option_value','name' => 'Option Values'),
			array('value' => 'manufacturer','name' => 'Manufacturers'),
			array('value' => 'download','name' => 'Downloads'),
			array('value' => 'review','name' => 'Reviews'),
			array('value' => 'information','name' => 'Informations'),
			array('value' => 'modification','name' => 'Modifications'),
			array('value' => 'layout','name' => 'Layouts'),
			array('value' => 'banner','name' => 'Banners'),
			array('value' => 'seo_url','name' => 'Seo Urls'),
			array('value' => 'order','name' => 'Orders'),
			array('value' => 'recurring','name' => 'Recurrings'),
			array('value' => 'voucher','name' => 'Vouchers'),
			array('value' => 'customer','name' => 'Customers'),
			array('value' => 'address','name' => 'Addresses'),
			array('value' => 'customer_group','name' => 'Customer Groups'),
			array('value' => 'custom_field','name' => 'Customer Fields'),
			array('value' => 'marketing','name' => 'Marketings'),
			array('value' => 'coupon','name' => 'Coupons'),
			array('value' => 'location','name' => 'Locations'),
			array('value' => 'language','name' => 'Languages'),
			array('value' => 'currency','name' => 'Currencies'),
			array('value' => 'stock_status','name' => 'Stock Status'),
			array('value' => 'order_status','name' => 'Order Status'),
			array('value' => 'return','name' => 'Returns'),
			array('value' => 'country','name' => 'Countries'),
			array('value' => 'zone','name' => 'Zones'),
			array('value' => 'geo_zone','name' => 'Geo Zones'),
			array('value' => 'tax','name' => 'Taxes'),
			array('value' => 'length','name' => 'Lenths'),
			array('value' => 'weight','name' => 'Weights'),
			array('value' => 'upload','name' => 'Uploads'),
			array('value' => 'statistics','name' => 'Statistics'),
		);
		$data['customtables'] = array();
		$custom_tables = $this->model_extension_wimportexport->getCustomTables();
		foreach($custom_tables as $custom_table){
			$data['customtables'][] = array('value' => $custom_table,'name' => ucwords(str_replace('_',' ',$custom_table)));
		}
		
		$data['descriptiontables'] = array('product,option,option_value,filter,filter_group,attribute,attribute_group,customer_group,product_attribute,category');
		
		$data['totalcases'] = $this->model_extension_wimportexport->getTotalCases();
		$data['totalenablecases'] = $this->model_extension_wimportexport->getTotalCases($filter_data = array('filter_status' => 1));
		$data['totaldisablecases'] = $this->model_extension_wimportexport->getTotalCases($filter_data = array('filter_status' => 0));
		
		if(isset($this->session->data['newitem'])){
			unset($this->session->data['newitem']);
		}
		if(isset($this->session->data['updateitem'])){
			unset($this->session->data['updateitem']);
		}
		if(isset($this->session->data['deleteitem'])){
			unset($this->session->data['deleteitem']);
		}
		
		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('extension/import_export', $data));
	}
	
	public function caselist(){
		$this->load->model('extension/wimportexport');
		$this->load->language('extension/importexport');
		$data['user_token'] = $this->session->data['user_token'];
		
		if (isset($this->request->get['filter_user'])) {
			$filter_user = $this->request->get['filter_user'];
		} else {
			$filter_user = '';
		}

		if (isset($this->request->get['filter_status'])) {
			$filter_status = $this->request->get['filter_status'];
		} else {
			$filter_status = '';
		}
		
		if (isset($this->request->get['filter_table'])) {
			$filter_table = $this->request->get['filter_table'];
		} else {
			$filter_table = '';
		}
		
		if (isset($this->request->get['filter_name'])) {
			$filter_name = $this->request->get['filter_name'];
		} else {
			$filter_name = '';
		}
		
		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}
		
		$url = '';
		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_user'])) {
			$url .= '&filter_user=' . $this->request->get['filter_user'];
		}

		if (isset($this->request->get['filter_table'])) {
			$url .= '&filter_table=' . $this->request->get['filter_table'];
		}

		if (isset($this->request->get['filter_status'])) {
			$url .= '&filter_status=' . $this->request->get['filter_status'];
		}
		
		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$data['cases'] = array();
		$filter_data = array(
			'filter_name' => $filter_name,
			'filter_status' => $filter_status,
			'filter_user' => $filter_user,
			'filter_table' => $filter_table,
			'start'           => ($page - 1) * $this->config->get('config_limit_admin'),
			'limit'           => $this->config->get('config_limit_admin')
		);
		
		$cases_total = $this->model_extension_wimportexport->getTotalCases($filter_data);
		
		$cases = $this->model_extension_wimportexport->getCases($filter_data);
		
		foreach($cases as $case){
			$data['cases'][] = array(
				'excel_id' => $case['excel_import_export_id'],
				'name' => $case['name'],
				'type' => $case['type'],
				'casename' 				=> substr($case['name'],0,1),
				'formdata' => json_decode($case['formdata'],true),
				'date' 				=> date('d M Y', strtotime($case['date_added'])),
				'edit'       => $this->url->link('extension/importexport/edit'.$case['type'], 'user_token=' . $this->session->data['user_token'] . '&excel_id=' . $case['excel_import_export_id'] . $url, true)
			);
		}
		
		$url = '';
		if (isset($this->request->get['filter_name'])) {
			$url .= '&filter_name=' . urlencode(html_entity_decode($this->request->get['filter_name'], ENT_QUOTES, 'UTF-8'));
		}

		if (isset($this->request->get['filter_user'])) {
			$url .= '&filter_user=' . $this->request->get['filter_user'];
		}

		if (isset($this->request->get['filter_table'])) {
			$url .= '&filter_table=' . $this->request->get['filter_table'];
		}

		if (isset($this->request->get['filter_status'])) {
			$url .= '&filter_status=' . $this->request->get['filter_status'];
		}
		
		if (isset($this->request->get['page'])) {
			$url .= '&page=' . $this->request->get['page'];
		}
		
		$pagination = new Pagination();
		$pagination->total = $cases_total;
		$pagination->page = $page;
		$pagination->limit = $this->config->get('config_limit_admin');
		$pagination->url = $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'] . $url . '&page={page}', true);

		$data['pagination'] = $pagination->render();

		$data['results'] = sprintf($this->language->get('text_pagination'), ($cases_total) ? (($page - 1) * $this->config->get('config_limit_admin')) + 1 : 0, ((($page - 1) * $this->config->get('config_limit_admin')) > ($cases_total - $this->config->get('config_limit_admin'))) ? $cases_total : ((($page - 1) * $this->config->get('config_limit_admin')) + $this->config->get('config_limit_admin')), $cases_total, ceil($cases_total / $this->config->get('config_limit_admin')));
		
		$this->response->setOutput($this->load->view('extension/importexport/caselist', $data));
	}

	protected function validate() {
		if (!$this->user->hasPermission('modify', 'extension/importexport')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		return !$this->error;
	}
	
	protected function validateform() {
		if (!$this->user->hasPermission('modify', 'extension/importexport')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}

		if ((utf8_strlen($this->request->post['case_name']) < 3) || (utf8_strlen($this->request->post['case_name']) > 64)) {
			$this->error['case_name'] = $this->language->get('error_name');
		}
		
		if(empty($this->request->post['export_table'])){
			$this->error['export_table'] = $this->language->get('error_export_table');
		}
		if(isset($this->request->post['import_to']) && $this->request->post['import_to'] == 1){
			if(empty($this->request->post['import']) && $this->request->post['import'] = ''){
				$this->error['import'] = $this->language->get('error_import');
			}
		}elseif(isset($this->request->post['import_to']) && $this->request->post['import_to'] == 2){
			if(empty($this->request->post['ftp_host']) || empty($this->request->post['ftp_port']) || empty($this->request->post['ftp_username']) || empty($this->request->post['ftp_password']) || empty($this->request->post['file_root'])){
				$this->error['import'] = $this->language->get('error_import');
			}
		}elseif(isset($this->request->post['import_to']) && $this->request->post['import_to'] == 3){
			if(empty($this->request->post['file_url'])){
				$this->error['import'] = $this->language->get('error_import');
			}
		}elseif(isset($this->request->post['import_to']) && $this->request->post['import_to'] == 4){
			if(empty($this->request->post['google_sheet_id']) || empty($this->request->post['google_sheet_name']) || empty($this->request->post['google_sheet_json'])){
				$this->error['import'] = $this->language->get('error_import');
			}
		}
		
		if(isset($this->request->post['export_to']) && $this->request->post['export_to'] == 3){
			if(empty($this->request->post['google_sheet_id']) || empty($this->request->post['google_sheet_name']) || empty($this->request->post['google_sheet_json'])){
				$this->error['import'] = $this->language->get('error_import');
			}
		}
		
		if ($this->error && !isset($this->error['warning'])) {
			$this->error['warning'] = $this->language->get('error_warning');
		}
		
		return !$this->error;
	}
	
	public function getcolumns() {
		$json = array();

		$this->load->model('extension/wimportexport');
		$repeatcolumn = array();
		$totaldefaultfield = 0;
		$allcolumns = array();
		$allcustomfieldscolumns = array();
		if(!empty($this->request->get['tablevalue'])){
			$columns = $this->model_extension_wimportexport->getColumns($this->request->get['tablevalue']);
			
			foreach($columns['fields'] as $key => $column){
				if(!in_array($column['column'],$repeatcolumn)){
					$repeatcolumn[] = $column['column'];
					$allcolumns[] = $column;
				}
			}
			
			foreach($columns['customfields'] as $key => $column){
				if(!in_array($column['column'],$repeatcolumn)){
					$repeatcolumn[] = $column['column'];
					$allcustomfieldscolumns[] = $column;
				}
			}
			
			
			foreach($allcolumns as $key => $column){
				$totaldefaultfield++;
		
				$json['columns'][] = array(
					'name' => $column['name'],
					'fname' => $column['fname'],
					'column' => $column['column'],
					'value' => $column['value'],
					'sort_order' => $key + 1,
					'excel_column' => strtoupper($this->model_extension_wimportexport->toNum($key + 1)),
				);
				
			}
			
			foreach($allcustomfieldscolumns as $key => $column){
				$json['columns'][] = array(
					'name' => $column['name'],
					'fname' => $column['fname'],
					'column' => $column['column'],
					'value' => $column['value'],
					'sort_order' => $totaldefaultfield + 1,
					'excel_column' => strtoupper($this->model_extension_wimportexport->toNum($totaldefaultfield + 1)),
				);
				$totaldefaultfield++;
			}
		}
		
		$json['row'] = isset($this->request->get['row']) ? $this->request->get['row'] : '';

		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	
	public function addexport() {
		$this->load->language('extension/importexport');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('extension/wimportexport');
		
		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			
			$this->model_extension_wimportexport->addcase($this->request->post,'export');

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('extension/importexport', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}else{
			$this->getForm('export');
		}
	}
	
	public function addimport() {
		$this->load->language('extension/importexport');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('extension/wimportexport');
		
		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			
			$this->model_extension_wimportexport->addcase($this->request->post,'import');

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('extension/importexport', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}else{
			$this->getForm('import');
		}
	}
	
	public function editexport() {
		$this->load->language('extension/importexport');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('extension/wimportexport');
		
		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			
			$this->model_extension_wimportexport->editcase($this->request->post,$this->request->get['excel_id'],'export');

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('extension/importexport', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}else{
			$this->getForm('export');
		}
	}
	
	public function editimport() {
		$this->load->language('extension/importexport');

		$this->document->setTitle($this->language->get('heading_title'));

		$this->load->model('extension/wimportexport');
		
		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validateForm()) {
			$this->model_extension_wimportexport->editcase($this->request->post,$this->request->get['excel_id'],'import');

			$this->session->data['success'] = $this->language->get('text_success');

			$url = '';

			if (isset($this->request->get['sort'])) {
				$url .= '&sort=' . $this->request->get['sort'];
			}

			if (isset($this->request->get['order'])) {
				$url .= '&order=' . $this->request->get['order'];
			}

			if (isset($this->request->get['page'])) {
				$url .= '&page=' . $this->request->get['page'];
			}

			$this->response->redirect($this->url->link('extension/importexport', 'user_token=' . $this->session->data['user_token'] . $url, true));
		}else{
			$this->getForm('import');
		}
	}
	
	public function getform($type) {
		$this->load->language('tool/upload');
		$this->load->language('extension/importexport');
		$this->load->model('tool/upload');
		$this->load->model('tool/image');
		
		if (isset($this->error['warning'])) {
			$data['error_warning'] = $this->error['warning'];
		} else {
			$data['error_warning'] = '';
		}
		
		if (isset($this->error['export_table'])) {
			$data['error_export_table'] = $this->error['export_table'];
		} else {
			$data['error_export_table'] = '';
		}
		
		if (isset($this->error['export_table'])) {
			$data['error_export_table'] = $this->error['export_table'];
		} else {
			$data['error_export_table'] = '';
		}
		
		if (isset($this->error['case_name'])) {
			$data['error_name'] = $this->error['case_name'];
		} else {
			$data['error_name'] = '';
		}
		
		if (isset($this->error['import'])) {
			$data['error_import'] = $this->error['import'];
		} else {
			$data['error_import'] = '';
		}
		
		$data['user_token'] = $this->session->data['user_token'];
		
		$this->load->model('user/user');
		
		$data['users'] = $this->model_user_user->getUsers();
		
		
		$this->load->model('localisation/language');
		$data['languages'] = $this->model_localisation_language->getLanguages();
		
		$this->load->model('setting/store');
		$data['stores'] = $this->model_setting_store->getStores();
		
		$this->load->model('catalog/category');
		$data['categories'] = $this->model_catalog_category->getCategories(array());
		
		$this->load->model('catalog/manufacturer');
		$data['manufacturers'] = $this->model_catalog_manufacturer->getManufacturers();
		
		$data['user_id'] = $this->user->getId();
		
		if($type == 'export'){
			$data['text_form'] = !isset($this->request->get['excel_id']) ? $this->language->get('text_export_add') : $this->language->get('text_export_edit');
		}elseif($type == 'import'){
			$data['text_form'] = !isset($this->request->get['excel_id']) ? $this->language->get('text_import_add') : $this->language->get('text_import_edit');
		}
		
		if($type == 'export'){
			if (!isset($this->request->get['excel_id'])) {
				$data['action'] = $this->url->link('extension/importexport/addexport', 'user_token=' . $this->session->data['user_token'], true);
			} else {
				$data['action'] = $this->url->link('extension/importexport/editexport', 'user_token=' . $this->session->data['user_token'] . '&excel_id=' . $this->request->get['excel_id'], true);
			}
		}elseif($type == 'import'){
			if (!isset($this->request->get['excel_id'])) {
				$data['action'] = $this->url->link('extension/importexport/addimport', 'user_token=' . $this->session->data['user_token'], true);
			} else {
				$data['action'] = $this->url->link('extension/importexport/editimport', 'user_token=' . $this->session->data['user_token'] . '&excel_id=' . $this->request->get['excel_id'], true);
			}
		}
		
		
		$data['cancel'] = $this->url->link('extension/importexport', 'user_token=' . $this->session->data['user_token'], true);
		
		$this->load->model('user/user');
		
		$data['users'] = $this->model_user_user->getUsers();
		if (isset($this->request->get['excel_id'])) {
			$case = $this->model_extension_wimportexport->getCase($this->request->get['excel_id']);
		}
		
		if(isset($this->request->get['excel_id'])){
			$data['excel_id'] = $this->request->get['excel_id'];
		}else{
			$data['excel_id'] = 0;
		}

		if (isset($this->request->post['export_table'])) {
			$data['tablename'] = $this->request->post['export_table'];
		} elseif (!empty($case)) {
			$data['tablename'] = $case['tablename'];
		} else {
			$data['tablename'] = '';
		}
		
		if (isset($this->request->post['case_name'])) {
			$data['case_name'] = $this->request->post['case_name'];
		} elseif (!empty($case)) {
			$data['case_name'] = $case['name'];
		} else {
			$data['case_name'] = '';
		}
		
		if (isset($this->request->post) && !empty($this->request->post)) {
			$data['formdata'] = $this->request->post;
		} elseif (!empty($case)) {
			$data['formdata'] = json_decode($case['formdata'],true);
		} else {
			$data['formdata'] = array();
		}
		
		$data['sort_order_field'] = isset($data['formdata']['sort_order_field']) ? $data['formdata']['sort_order_field'] : '';
		
		$data['filters'] = isset($data['formdata']['filter']) ? $data['formdata']['filter'] : array();
		$data['fetch_image'] = isset($data['formdata']['fetch_image']) ? $data['formdata']['fetch_image'] : 'no';
		$data['filename'] = '';
		
		if(!empty($data['formdata']['import'])){
			$filedata = $this->model_tool_upload->getUploadByCode($data['formdata']['import']);
			$data['filename'] = !empty($filedata['filename']) ? $filedata['filename'] : '';
		}
		if($data['tablename']){
			$columns = $this->model_extension_wimportexport->getColumns($data['tablename']);
			
			$data['columns'] = array_merge($columns['fields'],$columns['customfields']);
			if(!empty($data['formdata']['import'])){
				$filedata = $this->model_tool_upload->getUploadByCode($data['formdata']['import']);
				$data['filename'] = !empty($filedata['filename']) ? $filedata['filename'] : '';
			}
			$data['status'] =  $data['formdata']['status'];
			$data['import_type'] = "";
			if($data['tablename'] == 'product'){
				$data['import_type'] =  isset($data['formdata']['import_type']) ? $data['formdata']['import_type'] : '';
			}
			
			$data['filter_language_id'] = $data['formdata']['filter_language_id'];
			$data['cron_job_link'] = isset($case['cron_job_link']) ? $case['cron_job_link'] : '';
			
			$repeatcolumn = array();
			$data['mappings'] = array();
			$totalcolumns = count($data['columns']);
			foreach($data['columns'] as $column){
				if(!in_array($column['column'],$repeatcolumn)){
					$repeatcolumn[] = $column['column'];
					
					if(isset($data['formdata']['mapping'][$column['column']]['sort_order'])){
						$sort_order = $data['formdata']['mapping'][$column['column']]['sort_order'];
					}else{
						$sort_order = $totalcolumns;
						$totalcolumns++;
					}
					$data['mappings'][] = array(
						'name' => $column['name'],
						'fname' => $column['fname'],
						'column' => $column['column'],
						'excel_column' => !empty($sort_order) ? strtoupper($this->model_extension_wimportexport->toNum($sort_order)) : '',
						'sort_order' => $sort_order,
						'status' => isset($data['formdata']['mapping'][$column['column']]['status']) ? $data['formdata']['mapping'][$column['column']]['status'] : 0,
					);
				}
			}
			
			function sortIt( $a, $b ){
				return $a['sort_order'] < $b['sort_order'] ? -1 : 1;
			}

			usort($data['mappings'], "sortIt" );
		}
		
		$this->load->model('localisation/language');
		$data['languages'] = $this->model_localisation_language->getLanguages();
		
		$this->load->model('setting/store');
		$data['stores'] = $this->model_setting_store->getStores();
		
		$this->load->model('catalog/category');
		$data['categories'] = $this->model_catalog_category->getCategories(array());
		
		$this->load->model('catalog/manufacturer');
		$data['manufacturers'] = $this->model_catalog_manufacturer->getManufacturers();
		
		$data['export_tables'] = array(
			array('value' => 'product','name' => 'Products'),
			array('value' => 'product_special','name' => 'Products - Specials'),
			array('value' => 'product_discount','name' => 'Products - Discount'),
			array('value' => 'product_attribute','name' => 'Products - Attributes'),
			array('value' => 'product_option','name' => 'Products - Option'),
			array('value' => 'product_option_value','name' => 'Products - Option Values'),
			array('value' => 'category','name' => 'Categories'),
			array('value' => 'manufacturer','name' => 'Manufacturers'),
			array('value' => 'attribute','name' => 'Attributes'),
			array('value' => 'attribute_group','name' => 'Attribute Groups'),
			array('value' => 'option','name' => 'Options'),
			array('value' => 'option_value','name' => 'Option Values'),
			array('value' => 'filter','name' => 'Filters'),
			array('value' => 'filter_group','name' => 'Filter Groups'),
			array('value' => 'customer','name' => 'Customers'),
			array('value' => 'customer_group','name' => 'Customer Groups'),
			array('value' => 'order','name' => 'Orders'),
			array('value' => 'order_product','name' => 'Orders - Products'),
			array('value' => 'order_total','name' => 'Orders - Totals'),
			array('value' => 'coupon','name' => 'Coupons'),
		);
		$data['customtables'] = array();
		$custom_tables = $this->model_extension_wimportexport->getCustomTables();
		foreach($custom_tables as $custom_table){
			$data['customtables'][] = array('value' => $custom_table,'name' => ucwords(str_replace('_',' ',$custom_table)));
		}
		
		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');
		
		$this->response->setOutput($this->load->view('extension/importexport/'.$type.'form', $data));
		
	}
	
	public function savecronjobsettings() {
		$this->load->language('extension/importexport');
		$this->load->model('setting/setting');
		$json = array();
		if (($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()) {
			$this->model_setting_setting->editSetting('module_importexport', $this->request->post);
			$json['success'] = 'Successfully Added';
		}else{
			$json['error'] = $this->language->get('error_permissionn');
		}
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function deletecase(){
		$this->load->language('extension/importexport');
		$json=array();
		$this->load->model('extension/wimportexport');
		if(isset($this->request->post['selected'])){
			foreach($this->request->post['selected'] as $excel_id){
				$this->model_extension_wimportexport->deletecase($excel_id);
			}
		}else{
			$json['error'] = $this->language->get('error_select_once');
		}
		
		
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function exportcase() {
		$this->load->model('extension/wimportexport');
		$this->load->model('setting/store');
		$this->load->model('localisation/language');
		
		$json = array();
		
		if(isset($this->request->get['excel_import_export_id'])){
			$excel_import_export_id = $this->request->get['excel_import_export_id'];
		}else{
			$excel_import_export_id = 0;
		}
		
		$case = $this->model_extension_wimportexport->getCase($excel_import_export_id);
		if(isset($this->session->data['alldata'])){
			unset($this->session->data['alldata']);
		}
		
		if($case['status']){
			$formdata = json_decode($case['formdata'],true);
			
			$exportdata = $this->model_extension_wimportexport->getData($formdata);
			$this->session->data['alldata'] = count($exportdata);
			$mapping = array();
			foreach($formdata['mapping'] as $key => $value){
				$mapping[] = array(
					'key' => !empty($value['status']) ? $key : '',
					'sort_order' => $value['sort_order'],
					'status' => isset($value['status']) ? $value['status'] : 0,
				);
			}
			
			function sortIt( $a, $b ){
				return $a['sort_order'] < $b['sort_order'] ? -1 : 1;
			}

			usort($mapping, "sortIt" );
			
			try{
				if($formdata['file_format'] == 'xml'){
					$doc = new DOMDocument();
					$doc->formatOutput = true;
					$r = $doc->createElement($case['tablename'].'s');
					$doc->appendChild( $r );
					foreach($exportdata as $export){
						$PRODUCT = $doc->createElement($case['tablename']);
						foreach($mapping as $value){
							if(isset($export[$value['key']])){
								$PRODUCTID = $doc->createElement($value['key']);
								if(isset($formdata['fetch_image']) && $formdata['fetch_image'] == 'yes' && $value['key'] == 'image'){
									if($export[$value['key']] != '' || file_exists(HTTP_CATALOG.'image/'.$export[$value['key']])){
										$PRODUCTID->appendChild($doc->createTextNode(HTTP_CATALOG.'image/'.$export[$value['key']]));
									}else{
										$PRODUCTID->appendChild($doc->createTextNode(''));
									}
								}else{
									$PRODUCTID->appendChild($doc->createTextNode($export[$value['key']]));
								}
								$PRODUCT->appendChild($PRODUCTID);
							}
							
							$r->appendChild($PRODUCT);
						}
					}
					$mask = time();
					$doc->saveXML();
					$doc->save(DIR_EXCEL.$case['name'].$mask.".xml");
					$file = $case['name'].$mask.".xml";
					$json['filename'] = $file;
					$json['filesize'] = $this->formatSizeUnits(filesize(DIR_EXCEL.$file));
				}else{
					$objPHPExcel = new PHPExcel();
					$objPHPExcel->setActiveSheetIndex(0);
					$objPHPExcel->getActiveSheet()->setTitle($case['tablename']);
					
					$column=1;$row = 1;
					foreach($mapping as $value){
						$col = $this->model_extension_wimportexport->toNum($row);
						$objPHPExcel->getActiveSheet()->setCellValue($col.$column, $value['key'])->getColumnDimension($col)->setAutoSize(true);
						$row++;
					}
				
					$vrow = 1;
					foreach($mapping as $value){
						$column = 2;
						$i = 0;
						foreach($exportdata as $export){						
							if(isset($export[$value['key']])){
								if(isset($this->session->data['processing'])){
									unset($this->session->data['processing']);
								}
								$this->session->data['processing'] = $i;
								$col = $this->model_extension_wimportexport->toNum($vrow);
								if(isset($formdata['fetch_image']) && $formdata['fetch_image'] == 'yes' && $value['key'] == 'image'){
									if($export[$value['key']] != '' || file_exists(HTTP_CATALOG.'image/'.$export[$value['key']])){
									  $objPHPExcel->getActiveSheet()->setCellValue($col.$column, HTTP_CATALOG.'image/'.$export[$value['key']]);
									}else{
									 $objPHPExcel->getActiveSheet()->setCellValue($col.$column, '');
									}
								}else{
									$objPHPExcel->getActiveSheet()->setCellValue($col.$column, $export[$value['key']]);
								}
								$column++;
								$i++;
							}
						}
						$vrow++;
					}
					
					$mask = time();
					if($formdata['file_format'] == 'csv'){
						$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'CSV');
						$filename = $case['name'].$mask.'.csv';
					}elseif($formdata['file_format'] == 'xls'){
						$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
						$filename = $case['name'].$mask.'.xls';
					}elseif($formdata['file_format'] == 'xlsx'){
						$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
						$filename = $case['name'].$mask.'.xlsx';
					}
					
					$objWriter->save(DIR_EXCEL . $filename);
					
					$json['filename'] = $filename;
					$json['filesize'] = $this->formatSizeUnits(filesize(DIR_EXCEL.$filename));
				}
				
				if($formdata['export_to'] == '2'){
					$filepath = DIR_EXCEL . $filename;
					
					$ftp_server = $formdata['ftp_host']; // Address of FTP server.
					$ftp_user_name = $formdata['ftp_username']; // Username
					$ftp_user_pass = $formdata['ftp_password']; // Password
					$destination_file = $formdata['file_root'];
					$conn_id = ftp_connect($ftp_server) or die("<span style='color:#FF0000'><h2>Couldn't connect to $ftp_server</h2></span>");
					$login_result = ftp_login($conn_id, $ftp_user_name, $ftp_user_pass) or die("<span style='color:#FF0000'><h2>You do not have access to this ftp server!</h2></span>");   // login with username and password, or give invalid user message
					if ((!$conn_id) || (!$login_result)) {  // check connection
						// wont ever hit this, b/c of the die call on ftp_login
						die("<span style='color:#FF0000'><h2>FTP connection has failed! <br />");
						//echo "Attempted to connect to $ftp_server for user $ftp_user_name</h2></span>";
						//exit;
					}
					
					$upload = ftp_put($conn_id, $destination_file.$filename, $filepath, FTP_BINARY);  // upload the file
					if (!$upload) {  // check upload status
						die("<span style='color:#FF0000'><h2>FTP upload of $filename has failed!</h2></span>");
					} else {
						$json['success'] = true;
						//echo "<span style='color:#339900'><h2>Uploading $filename Completed Successfully!</h2></span><br /><br />";
					}
					ftp_close($conn_id); // close the FTP stream
				}elseif($formdata['export_to'] == '3'){
					require_once DIR_SYSTEM.'library/excel_point_tool/vendor/autoload.php';
					$client = new \Google_Client();
					$client->setApplicationName('My PHP App');
					$client->setScopes(array('https://www.googleapis.com/auth/drive',    
					  'https://www.googleapis.com/auth/spreadsheets.readonly',     
					  'https://www.googleapis.com/auth/drive.file'));
					$client->setAccessType('offline');
					
					$auth = html_entity_decode($formdata['google_sheet_json']);
					$client->setAuthConfig($auth);
					$service = new Google_Service_Sheets($client);
					$SHEET_ID = $formdata['google_sheet_id'];
					
					$column=1;$row = 1;
					foreach($mapping as $value){
						$col = $this->model_extension_wimportexport->toNum($row);
						$range = str_replace(' ','',$formdata['google_sheet_name']).'!'.$col.$column;
						$values = [
							[$value['key']],
						];
						$body = new Google_Service_Sheets_ValueRange([
							'values' => $values,
						]);
						$params = [
							'valueInputOption' => 'RAW'
						];
						$result = $service->spreadsheets_values->update(
							$SHEET_ID,
							$range,
							$body,
							$params
						);
						
						$row++;
					}
					$vrow = 1;
					foreach($mapping as $value){
						$column = 2;
						$i = 0;
						foreach($exportdata as $export){						
							if(isset($export[$value['key']])){
								if(isset($this->session->data['processing'])){
									unset($this->session->data['processing']);
								}
								$this->session->data['processing'] = $i;
								$col = $this->model_extension_wimportexport->toNum($vrow);
								$range = str_replace(' ','',$formdata['google_sheet_name']).'!'.$col.$column;
								$values = [
									[$export[$value['key']]],
								];
								$body = new Google_Service_Sheets_ValueRange([
									'values' => $values,
								]);
								$params = [
									'valueInputOption' => 'RAW'
								];
								$result = $service->spreadsheets_values->update(
									$SHEET_ID,
									$range,
									$body,
									$params
								);
								
								$column++;
								$i++;
							}
						}
						$vrow++;
					}
					$json['success'] = true;
				}else{
					$json['success'] = true;
					$json['download'] = $this->url->link('extension/importexport/downloadfile','user_token='.$this->session->data['user_token'].'&filter_eformat='.$formdata['file_format'].'&mask='.$mask.'&filename='.$case['name'].'&batches='.false);
				}
			}
			catch(Exception $e){
				$errormsg = $e->getErrors();
				$errorexe = 'Error loading file "'.pathinfo($filename,PATHINFO_BASENAME).'": '.$errormsg[0]['message'];
			}
			Finally {
				if(isset($errorexe)){
					$json['error'] = $errorexe;
				}
			}
		}else{
			$json['error'] = 'Status is disable';
		}
		
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function downloadfile(){
		$file = DIR_EXCEL . $this->request->get['filename'].$this->request->get['mask'].'.'.$this->request->get['filter_eformat'];
		if(is_file($file)){
			if($this->request->get['filter_eformat'] != 'xml'){
					header('Content-Type: application/vnd.ms-excel'); 
					header('Content-Disposition: attachment;filename='.basename($file)); 
					header('Cache-Control: max-age=0'); 
					header('Expires: 0');
					header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
					header('Pragma: public');
					header('Content-Length: ' . filesize($file));
					readfile($file, 'rb');
					unlink($file);
					exit();
			}else{
				header('Content-type: text/xml');
				header('Content-Disposition: attachment; filename='.basename($file));
				header('Expires: 0');
				header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
				header('Pragma: public');
				header('Content-Length: ' . filesize($file));
				readfile($file, 'rb');
				unlink($file);
				exit();
			}
		}
	}
	
	public function importcase(){
		$this->load->model('extension/wimportexport');
		$this->load->model('setting/store');
		$this->load->model('tool/upload');

		$json = array();
		
		if(isset($this->request->get['excel_import_export_id'])){
			$excel_import_export_id = $this->request->get['excel_import_export_id'];
		}else{
			$excel_import_export_id = 0;
		}
		
		if(isset($this->request->get['processing'])){
			$processing = $this->request->get['processing'];
		}else{
			$processing = 1;
		}
		
		$case = $this->model_extension_wimportexport->getCase($excel_import_export_id);
		if(isset($case['status'])){
			$formdata = json_decode($case['formdata'],true);
			$mapping = array();
			foreach($formdata['mapping'] as $key => $value){
				$mapping[] = array(
					'key' => !empty($value['status']) ? $key : '',
					'sort_order' => $value['sort_order'],
				);
			}
			
			function sortIt( $a, $b ){
				return $a['sort_order'] < $b['sort_order'] ? -1 : 1;
			}

			usort($mapping, "sortIt" );
			
			if($formdata['import_to'] == '2'){
				$ftp_server = $formdata['ftp_host']; // Address of FTP server.
				$ftp_user_name = $formdata['ftp_username']; // Username
				$ftp_user_pass = $formdata['ftp_password']; // Password
				$destination_file = $formdata['file_root'];
				$file = $formdata['file_name'];
				$conn_id = ftp_connect($ftp_server) or die("<span style='color:#FF0000'><h2>Couldn't connect to $ftp_server</h2></span>");        // set up basic connection
				$login_result = ftp_login($conn_id, $ftp_user_name, $ftp_user_pass) or die("<span style='color:#FF0000'><h2>You do not have access to this ftp server!</h2></span>");   // login with username and password, or give invalid user message
				if ((!$conn_id) || (!$login_result)) {  // check connection
					// wont ever hit this, b/c of the die call on ftp_login
					echo "<span style='color:#FF0000'><h2>FTP connection has failed! <br />";
					echo "Attempted to connect to $ftp_server for user $ftp_user_name</h2></span>";
					exit;
				}
				$inputfilename = DIR_EXCEL.$file;
				$download = ftp_get($conn_id, $inputfilename,$destination_file.$file,  FTP_BINARY);  // Download the file
			}elseif($formdata['import_to'] == '4'){
				require_once DIR_SYSTEM.'library/excel_point_tool/vendor/autoload.php';
				$client = new \Google_Client();
				$client->setApplicationName('My PHP App');
				$client->setScopes(array('https://www.googleapis.com/auth/drive',    
				  'https://www.googleapis.com/auth/spreadsheets.readonly',     
				  'https://www.googleapis.com/auth/drive.file'));
				$client->setAccessType('offline');
				
				$auth = html_entity_decode($formdata['google_sheet_json']);
				$client->setAuthConfig($auth);
				$service = new Google_Service_Sheets($client);
				$SHEET_ID = $formdata['google_sheet_id'];
				
				$range = str_replace(' ','',$formdata['google_sheet_name']).'!'."A1:ZZZ";
				$sheetInfo = $service->spreadsheets->get($SHEET_ID)->getProperties();
				$sheetdata = $service->spreadsheets_values->get($SHEET_ID, $range);
				$allDataInSheet = $sheetdata->getValues();
				$i = 0;		
				if(isset($this->session->data['alldata'])){
					unset($this->session->data['alldata']);
				}
				$this->session->data['alldata'] = count($allDataInSheet);
				$limit = round($this->session->data['alldata'] / 10);
						
				if($limit < 1){
					$limit = 1;
				}
				array_unshift($mapping,"");
				unset($mapping[0]);
				$newarray = array_slice($allDataInSheet, $processing, (int)$limit);
				
				foreach($newarray as $k=> $value){
					for($u = 0;$u<count($value);$u++){
						if(!empty($mapping[$u]['key'])){
							$sheets[$mapping[$u]['key']] = $value[$u];
						}
					}
					if($sheets){
						if($u>=count($value)){
							$result = $this->model_extension_wimportexport->setData($sheets,$formdata);
							if(isset($result['new'])){
								$this->session->data['newitem'][] = $result['new'];
								$json['newitem'][] = $result['new'];
							}
							if(isset($result['update'])){
								$this->session->data['updateitem'][] = $result['update'];
								$json['updateitem'][] = $result['update'];
							}
							if(isset($result['delete'])){
								$this->session->data['deleteitem'][] = $result['delete'];
								$json['deleteitem'][] = $result['delete'];
							}
						}
					}else{
					    $json['error'] = 'No data';
					}
				}
				if($processing < $this->session->data['alldata']){
					$processing += $limit;
					$json['next'] = str_replace('&amp;', '&', $this->url->link('extension/importexport/importcase', 'user_token=' . $this->session->data['user_token'] . '&excel_import_export_id=' . $excel_import_export_id . '&processing=' . $processing, true));
				}else{
					$json['success'] = 'Successfully Imported';
					if(isset($this->session->data['newitem'])){
						$json['totalnewitem'] = count($this->session->data['newitem']);
						unset($this->session->data['newitem']);
					}
					if(isset($this->session->data['updateitem'])){
						$json['totalupdateitem'] = count($this->session->data['updateitem']);
						unset($this->session->data['updateitem']);
					}
					if(isset($this->session->data['deleteitem'])){
						$json['totaldeleteitem'] = count($this->session->data['deleteitem']);
						unset($this->session->data['deleteitem']);
					}
				}
				$json['completepercentage'] = round(($processing / $this->session->data['alldata']) * 100);
			}elseif($formdata['import_to'] == '3'){
				$filepath = $formdata['file_url'];
				$extension = pathinfo($filepath);
				if($extension['basename']){
					$file = $extension['basename'];
					move_uploaded_file($file, DIR_EXCEL);
					$inputfilename = DIR_EXCEL.$file;
				}
			}elseif($formdata['import_to'] == '1'){
				$filedata = $this->model_tool_upload->getUploadByCode($formdata['import']);
				if($filedata){
					$file = basename($filedata['filename']);
					$inputfilename = DIR_EXCEL.$file;
				}
			}
			
			if(isset($file) && $inputfilename){
				$extension = pathinfo($inputfilename);
				if($extension['basename']){
					if($extension['extension']=='xlsx' || $extension['extension']=='xls' || $extension['extension']=='csv') {
						try{
							if($extension['extension']=='csv'){
								$inputFileType = 'CSV';
								$objReader = PHPExcel_IOFactory::createReader($inputFileType);
								$objPHPExcel = $objReader->load($inputfilename);
							}else{
								$objPHPExcel = PHPExcel_IOFactory::load($inputfilename);
							}
						}catch(Exception $e){
							die('Error loading file "'.pathinfo($inputfilename,PATHINFO_BASENAME).'": '.$e->getMessage());
						}
						$allDataInSheet = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);
						
						$sheets = array();
						if(isset($this->session->data['alldata'])){
							unset($this->session->data['alldata']);
						}
						$this->session->data['alldata'] = count($allDataInSheet) - 1;
						$limit = round($this->session->data['alldata'] / 10);
						
						if($limit < 1){
							$limit = 1;
						}
						
						array_unshift($mapping,"");
						unset($mapping[0]);
						
						$newarray = array_slice($allDataInSheet, $processing, (int)$limit);
						
						foreach($newarray as $key => $value){
							for($u = 1;$u<=count($value);$u++){
								if(!empty($mapping[$u]['key'])){
									$sheets[$mapping[$u]['key']] = $value[strtoupper($this->model_extension_wimportexport->toNum($u))];
								}
							}
							if($sheets){
								if($u>count($value)){
									$result = $this->model_extension_wimportexport->setData($sheets,$formdata);
									if(isset($result['new'])){
										$this->session->data['newitem'][] = $result['new'];
										$json['newitem'][] = $result['new'];
									}
									if(isset($result['update'])){
										$this->session->data['updateitem'][] = $result['update'];
										$json['updateitem'][] = $result['update'];
									}
									if(isset($result['delete'])){
										$this->session->data['deleteitem'][] = $result['delete'];
										$json['deleteitem'][] = $result['delete'];
									}
								}
							}else{
								$json['error'] = 'No data';
							}
						}
					}elseif($extension['extension']=='xml'){
						if(isset($this->request->get['processing'])){
							$processing = $this->request->get['processing'];
						}else{
							$processing = 0;
						}
						try{
							$xml = simplexml_load_file($inputfilename);
						}catch(Exception $e){
							die('Error loading file "'.pathinfo($inputfilename,PATHINFO_BASENAME).'": '.$e->getMessage());
						}
						
						$sheets = array();
						 if(isset($this->session->data['alldata'])){
							unset($this->session->data['alldata']);
						}
						$this->session->data['alldata'] = count($xml->children());
						$limit = round($this->session->data['alldata'] / 10);
						
						if($limit < 1){
							$limit = 1;
						}
						
						array_unshift($mapping,"");
						unset($mapping[0]);
						
						$alldata = array();
						foreach($xml->children() as $k => $value){
							$alldata[] = (array)$value;
						}
						
						$newarray = array_slice($alldata, $processing, (int)$limit);
						foreach($newarray as $key => $value){
							for($u = 1;$u<=count($value);$u++){
								if(!empty($mapping[$u]['key'])){
									$sheets[$mapping[$u]['key']] = $value[$mapping[$u]['key']];
								}
							}
							if($sheets){
								if($u>count($value)){
									$result = $this->model_extension_wimportexport->setData($sheets,$formdata);
									if(isset($result['new'])){
										$this->session->data['newitem'][] = $result['new'];
										$json['newitem'][] = $result['new'];
									}
									if(isset($result['update'])){
										$this->session->data['updateitem'][] = $result['update'];
										$json['updateitem'][] = $result['update'];
									}
									if(isset($result['delete'])){
										$this->session->data['deleteitem'][] = $result['delete'];
										$json['deleteitem'][] = $result['delete'];
									}
								}
							}else{
								$json['error'] = 'No data';
							}
						}
					}
				}
				
				if($processing < $this->session->data['alldata']){
					$processing += $limit;
					$json['next'] = str_replace('&amp;', '&', $this->url->link('extension/importexport/importcase', 'user_token=' . $this->session->data['user_token'] . '&excel_import_export_id=' . $excel_import_export_id . '&processing=' . $processing, true));
				}else{
					$json['success'] = 'Successfully Imported';
					if(isset($this->session->data['newitem'])){
						$json['totalnewitem'] = count($this->session->data['newitem']);
						unset($this->session->data['newitem']);
					}
					if(isset($this->session->data['updateitem'])){
						$json['totalupdateitem'] = count($this->session->data['updateitem']);
						unset($this->session->data['updateitem']);
					}
					if(isset($this->session->data['deleteitem'])){
						$json['totaldeleteitem'] = count($this->session->data['deleteitem']);
						unset($this->session->data['deleteitem']);
					}
				}
				if($this->session->data['alldata'] > 1){
					$json['completepercentage'] = round(($processing / $this->session->data['alldata']) * 100);
				}else{
					$json['completepercentage'] = 100;
				}
				
				$json['filename'] = $file;
				$json['filesize'] = $this->formatSizeUnits(filesize(DIR_EXCEL.$file));
			}
		}else{
			$json['error'] = 'Status is disable';
		}
		
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function formatSizeUnits($bytes) {
		if ($bytes >= 1073741824)
        {
            $bytes = number_format($bytes / 1073741824, 2) . ' GB';
        }
        elseif ($bytes >= 1048576)
        {
            $bytes = number_format($bytes / 1048576, 2) . ' MB';
        }
        elseif ($bytes >= 1024)
        {
            $bytes = number_format($bytes / 1024, 2) . ' KB';
        }
        elseif ($bytes > 1)
        {
            $bytes = $bytes . ' bytes';
        }
        elseif ($bytes == 1)
        {
            $bytes = $bytes . ' byte';
        }
        else
        {
            $bytes = '0 bytes';
        }

        return $bytes;
	}
	
	public function exportdb(){
		$this->load->model('extension/wimportexport');
		$this->load->language('extension/importexport');
		$json = array();
		$alltables = array();
		$repeattables = array();
		if(!isset($this->request->post['db_table'])){
		    $json['error'] = $this->language->get('text_select_table');
		}
		
		if(!$json){
			foreach($this->request->post['db_table'] as $key => $tablename){
				if($this->model_extension_wimportexport->getCustomdbTables($tablename)){
					$alltables[] = $this->model_extension_wimportexport->getCustomdbTables($tablename);
				}else{
					$alltables[] = $this->model_extension_wimportexport->getTables($tablename);
				}
			}
			if (isset($this->request->post['fetch_image'])){
				$fetch_image = $this->request->post['fetch_image'];
			} else {
				$fetch_image = null;
			}
			
			if($this->request->post['file_format'] == 'xml'){
				$doc = new DOMDocument();
				$doc->formatOutput = true;
				$r = $doc->createElement('Migration');
				$doc->appendChild( $r );
				foreach($alltables as $tables){
					foreach($tables as $table){
						if(!in_array($table,$repeattables)){
							$repeattables[] = $table;
							$tablename = str_replace(DB_PREFIX,'',$table);
							$tablecolumns = $this->model_extension_wimportexport->getMigratecolumns($tablename);
							$exportTables = $this->model_extension_wimportexport->getMigratedata($tablename);
							$PRODUCTS = $doc->createElement($tablename);
							foreach($exportTables as $tabledata){
								$PRODUCT = $doc->createElement($tablename.'s');
								foreach($tablecolumns as $tablecolumn){
									$PRODUCTID = $doc->createElement($tablecolumn['Field']);
									if($fetch_image == 'yes' && $tablecolumn['Field'] == 'image'){
										if($tabledata[$tablecolumn['Field']] != '' || file_exists(HTTP_CATALOG.'image/'.$tabledata[$tablecolumn['Field']])){
										  $PRODUCTID->appendChild($doc->createTextNode(HTTP_CATALOG.'image/'.$tabledata[$tablecolumn['Field']]));
										}else{
										 $PRODUCTID->appendChild($doc->createTextNode(''));
										}
									}else{
										$PRODUCTID->appendChild($doc->createTextNode($tabledata[$tablecolumn['Field']]));
									}
									$PRODUCT->appendChild($PRODUCTID);
								}
								$PRODUCTS->appendChild($PRODUCT);
							}
							$r->appendChild($PRODUCTS);
						}
					}
				}
				$mask = time();
				$doc->saveXML();
				$doc->save(DIR_EXCEL.'Migration'.$mask.".xml");
			}else{
				$objPHPExcel = new PHPExcel();
				foreach($alltables as $tables){
					foreach($tables as $table){
						if(!in_array($table,$repeattables)){
							$repeattables[] = $table;
							$column = 1;$row = 1;
							$sheet = 0;
							$objWorkSheet = $objPHPExcel->createSheet($sheet);
							$tablename = str_replace(DB_PREFIX,'',$table);
							$objWorkSheet->setTitle($tablename);
							$tablecolumns = $this->model_extension_wimportexport->getMigratecolumns($tablename);
							$exportTables = $this->model_extension_wimportexport->getMigratedata($tablename);
							foreach($tablecolumns as $tablecolumn){
								$col = $this->model_extension_wimportexport->toNum($row);
								$objWorkSheet->setCellValue($col.$column, $tablecolumn['Field'])->getColumnDimension($col)->setAutoSize(true);
								$row++;
							}
							$sheet++;
							$column++;
							$rows = 2;
							foreach($exportTables as $tabledata){
								$excelcol = 1;
								foreach($tablecolumns as $tablesdata){
									$col = $this->model_extension_wimportexport->toNum($excelcol);
									if($fetch_image == 'yes' && $tablesdata['Field'] == 'image'){
										if($tabledata[$tablesdata['Field']] != '' || file_exists(HTTP_CATALOG.'image/'.$tabledata[$tablesdata['Field']])){
										  $objWorkSheet->setCellValue($col.$rows, HTTP_CATALOG.'image/'.$tabledata[$tablesdata['Field']]);
										}else{
										 $objWorkSheet->setCellValue($col.$rows, '');
										}
									}else{
										$objWorkSheet->setCellValue($col.$rows, $tabledata[$tablesdata['Field']]);
									}
									$excelcol++;
								}
								$rows++;
							}
						}
					}
				}  
			
				$objPHPExcel->removeSheetByIndex(
					$objPHPExcel->getIndex(
						$objPHPExcel->getSheetByName('Worksheet')
					)
				);
				
				$objPHPExcel->setActiveSheetIndex(0);
			
				$mask = time();
				if($this->request->post['file_format'] == 'csv'){
					$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'CSV');
					$filename = 'Migration'.$mask.'.csv';
				}elseif($this->request->post['file_format'] == 'xls'){
					$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
					$filename = 'Migration'.$mask.'.xls';
				}elseif($this->request->post['file_format'] == 'xlsx'){
					$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
					$filename = 'Migration'.$mask.'.xlsx';
				}
			
				$objWriter->save(DIR_EXCEL . $filename);
			
			}
			
			$json['filename'] = $filename;
    		$json['filesize'] = $this->formatSizeUnits(filesize(DIR_EXCEL.$filename));
			$json['success'] = true;
			
			$json['download'] = $this->url->link('extension/importexport/downloadfile','user_token='.$this->session->data['user_token'].'&filter_eformat='.$this->request->post['file_format'].'&mask='.$mask.'&filename=Migration&batches='.false);
		}
		
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function importdb(){
		$json = array();
		
		$this->load->model('extension/wimportexport');
		$this->load->language('extension/importexport');
		$this->load->model('setting/store');
		$this->load->model('tool/upload');
		
		if(($this->request->server['REQUEST_METHOD'] == 'POST') && $this->validate()){
			if($this->request->files && !empty($this->request->files['import']['tmp_name'])){
				$file = basename($this->request->files['import']['name']);
				move_uploaded_file($this->request->files['import']['tmp_name'], $file);
				$inputFileName = $file;
				$extension = pathinfo($inputFileName);
				if($extension['basename']){
					$filename = $extension['basename'];
					if($extension['extension']=='xlsx' || $extension['extension']=='xls' || $extension['extension']=='csv') {
						try{
							if($extension['extension']=='csv'){
								$inputFileType = 'CSV';
								$objReader = PHPExcel_IOFactory::createReader($inputFileType);
								$objPHPExcel = $objReader->load($inputFileName);
							}else{
								$objPHPExcel = PHPExcel_IOFactory::load($inputFileName);
							}
						}catch(Exception $e){
							die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
						}
						$loadedSheetNames = $objPHPExcel->getSheetNames();
						foreach($loadedSheetNames as $sheet => $tablename){
							$sheetOrderData = $objPHPExcel->getSheet($sheet)->toArray(null,true,true,true);
							$i = 0;
							$sheets = array();
							$alldata = array();
							foreach($sheetOrderData as $sheetdata){
								if($i!=0){
									for($u = 1;$u<=count($sheetdata);$u++){
										$sheets[$sheetOrderData[1][strtoupper($this->model_extension_wimportexport->toNum($u))]] = $sheetdata[strtoupper($this->model_extension_wimportexport->toNum($u))];
									}
									if($u>count($sheetdata)){
										$alldata[] = $sheets;
									}
								}
								$i++;
							}
							$this->model_extension_wimportexport->importdb($alldata,$tablename);
						}
						$json['success'] = $this->language->get('text_import_success');
						$json['filename'] = $filename;
						$json['filesize'] = is_file(DIR_EXCEL.$filename) ? $this->formatSizeUnits(filesize(DIR_EXCEL.$filename)) : '';
					}elseif($extension['extension']=='xml'){
						try{
							$xml = simplexml_load_file($inputFileName);
						}catch(Exception $e){
							die('Error loading file "'.pathinfo($inputFileName,PATHINFO_BASENAME).'": '.$e->getMessage());
						}
						
						foreach($xml->children() as $k => $table){
							$alldata = array();
							$sheets = array();
							foreach($table as $value){
								$alldata[] = (array)$value;
							}
							$this->model_extension_wimportexport->importdb($alldata,$k);
						}
						$json['success'] = $this->language->get('text_import_success');
						$json['filename'] = $filename;
						$json['filesize'] = is_file(DIR_EXCEL.$filename) ? $this->formatSizeUnits(filesize(DIR_EXCEL.$filename)) : '';
					}
				}
			}else{
				$json['error'] = $this->language->get('text_no_upload');
			}
		}else{
			$json['error'] = $this->language->get('text_no_upload');
		}
		
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function upload() {
		$this->load->language('sale/order');

		$json = array();

		if (!$json) {
			
			if (!empty($this->request->files['file']['name']) && is_file($this->request->files['file']['tmp_name'])) {
				// Sanitize the filename
				$filename = html_entity_decode($this->request->files['file']['name'], ENT_QUOTES, 'UTF-8');

				if ((utf8_strlen($filename) < 3) || (utf8_strlen($filename) > 128)) {
					$json['error'] = $this->language->get('error_filename');
				}
				
				// Allowed file extension types
				
				$allowed = array();
				
				$allowed = array('xls','xlsx','xml','csv');
				
				if (!in_array(strtolower(substr(strrchr($filename, '.'), 1)), $allowed)) {
					$json['error'] = $this->language->get('error_filetype');
				}
				
				// Allowed file mime types
				/* if (!in_array($this->request->files['file']['type'], $allowed)) {
					$json['error'] = $this->language->get('error_filetype');
				} */

				// Check to see if any PHP files are trying to be uploaded
				/* $content = file_get_contents($this->request->files['file']['tmp_name']);

				if (preg_match('/\<\?php/i', $content)) {
					$json['error'] = $this->language->get('error_filetype');
				} */

				// Return any upload error
				if ($this->request->files['file']['error'] != UPLOAD_ERR_OK) {
					$json['error'] = $this->language->get('error_upload_' . $this->request->files['file']['error']);
				}
			} else {
				$json['error'] = $this->language->get('error_upload');
			}
		}

		if (!$json) {
			$file = $filename;

			move_uploaded_file($this->request->files['file']['tmp_name'], DIR_EXCEL . $file);

			// Hide the uploaded file name so people can not link to it directly.
			$this->load->model('tool/upload');
			
			$json['file'] = $file;
			
			$json['code'] = $this->model_tool_upload->addUpload($filename, $file);

			$json['success'] = $this->language->get('text_upload');
		}

		$this->response->addHeader('Content-Type: application/json');
		$this->response->setOutput(json_encode($json));
	}
	
	public function cronjob(){
		$this->load->model('extension/wimportexport');
		$this->load->model('setting/store');
		$this->load->model('tool/upload');
		
		$json = array();
		
		if(isset($this->request->get['excel_import_export_id'])){
			$excel_id = $this->request->get['excel_import_export_id'];
		}else{
			$excel_id = 0;
		}
		
		$link = HTTP_CATALOG.'index.php?route=extension/excel_point&excel_id='.$excel_id;
		$this->model_extension_wimportexport->updatelink($link,$excel_id);
		
		$json['cron_job_link'] = $link;
		
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
}
