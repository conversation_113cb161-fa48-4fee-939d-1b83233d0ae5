<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleOpenorders file
 *
 * @category Openorders_Controller
 */

class ControllerExtensionUpsmoduleOpenorders extends Controller
{
    private $_base_model = 'extension/upsmodule/base';
    private $_country_model = 'extension/upsmodule/country';

    /**
     * ControllerExtensionUpsmoduleOpenorders index
     *
     * @return null
     */
    public function index()
    {
        $this->load->model($this->_base_model);

        #region setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "openorders",
            "extension/upsmodule/openorders"
        );
        #endregion

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->language($constants->link_translate);
        $this->document->setTitle($this->language->get($constants->text_open_orders));
        $this->load->model($constants->link_openorders);
        $this->load->model('extension/upsmodule/account');
        $this->load->model($constants->link_opencartsetting);
        $this->load->model('extension/upsmodule/packagedimension');
        $this->load->model('extension/upsmodule/shippingservice');
        $this->load->model('extension/upsmodule/accessorial');
        $check_date = $this->model_extension_upsmodule_openorders->checkDateArchivedOrders();
        foreach ($check_date as $date) {
            $order_id = '';
            $date_check_update = date($constants->ymd_orders);
            $date_create = date($constants->ymd_orders, strtotime($date['date_created']));
            $date_update = date($constants->ymd_orders, strtotime(date("Y-m-d", strtotime($date_create))." +90 days"));
            if (strtotime($date_update) <= strtotime($date_check_update)) {
                $order_id = $date['id'];
                $this->model_extension_upsmodule_openorders->checkUpdateDateArchivedOrders($order_id);
            }
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders checkLink
     *
     * @return $check_link
     */
    protected function checkLink()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $check_link = '';
        $list_billing_preference = $this->model_extension_upsmodule_openorders->getListCheckBillingPreference();
        foreach ($list_billing_preference as $row) {
            if ($row[$constants->value_data] == 1) {
                $check_link = $this->url->link(
                    $constants->link_openorders,
                    $constants->token .
                    $constants->sort_id_orders . $constants->link_sort_page,
                    true
                );
            } else {
                $check_link = $this->response->redirect(
                    $this->url->link('extension/upsmodule/billingpreference', $constants->token, true)
                );
            }
        }
        return $check_link;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders getProductName
     *
     * @return $list_open_order_array
     */
    protected function getProductName()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $url = '';
        $request = $this->request->get;
        $sort = 'id';
        $order = 'DESC';
        $page = 1;
        foreach ($request as $key => $val) {
            if ($key != "page" && $key != $constants->order_data && $key != "sort") {
                $url .= '&' .  $key . '=' . $val;
            }
        }
        if (isset($this->request->get['page']) && ctype_digit($this->request->get['page'])) {
            $url .= $this->orderPage . $this->request->get['page'];
            $page = $this->request->get['page'];
        } else {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        if (isset($this->request->get[$constants->order_data]) &&
            (strtolower($this->request->get[$constants->order_data]) != 'asc' &&
            strtolower($this->request->get[$constants->order_data]) != 'desc')
        ) {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        $url_order = 'DESC';
        if ($request[$constants->order_data] == 'ASC') {
            $url_order = 'ASC';
        }
        $filter_data = [
            'sort' => $request['sort'],
            $constants->order_data => $url_order,
            'page' => $page,
            'limit' => $this->config->get($constants->config_admin),
            'start' => $this->config->get($constants->config_admin) * ($page - 1)
        ];
        $list_open_orders = $this->model_extension_upsmodule_openorders->getListSetting($filter_data);
        $list_open_order_array = [];
        foreach ($list_open_orders as $key => $value) {
            $list_product = [];
            $list_product = explode(',', $value[$constants->list_product_name]);
            $str = "";
            if (count($list_product) > 3) {
                $str .= trim($list_product[0]) . $constants->drop_down . trim($list_product[1]) .
                    $constants->drop_down . trim($list_product[2]) . '<br /> ...';
            } else {
                $str .= implode($constants->drop_down, $list_product);
            }
            $value[$constants->list_product_name] = $str;
            $list_open_order_array[$key] = $value;
        }
        return $list_open_order_array;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders linkOpenOrder
     *
     * @return $link_order
     */
    protected function linkOpenOrder()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $link_order = $this->url->link(
            $constants->link_openorders,
            $constants->token . $constants->sort_id_orders .
            $constants->link_sort_page,
            true
        );
        return $link_order;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders linkShipment
     *
     * @return $link_shipment
     */
    protected function linkShipment()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $link_shipment = $this->url->link(
            'extension/upsmodule/shipments',
            $constants->token .
            '&sort=shipment_number&order=DESC&page=1',
            true
        );
        return $link_shipment;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders linkArchived
     *
     * @return $link_archived
     */
    protected function linkArchived()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $link_archived = $this->url->link(
            'extension/upsmodule/archivedorders',
            $constants->token .
            $constants->sort_id_orders . $constants->link_sort_page,
            true
        );
        return $link_archived;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders linkExportCSV
     *
     * @return $link_export_csv
     */
    protected function linkExportCsv()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $link_export_csv = $this->url->link('extension/upsmodule/openorders/exportCSV', $constants->token, true);
        return $link_export_csv;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders linkUpdateArchived
     *
     * @return $link_update_archived
     */
    protected function linkUpdateArchived()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $link_update_archived = $this->url->link(
            'extension/upsmodule/openorders/updateStatusArchivedOrders',
            $constants->token,
            true
        );
        return $link_update_archived;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders linkCheckDateArchived
     *
     * @return $link_update_archived
     */
    protected function linkCheckDateArchived()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $link_update_archived = $this->url->link(
            'extension/upsmodule/openorders/checkDateArchivedOrders',
            $constants->token,
            true
        );
        return $link_update_archived;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders linkCreateBatch
     *
     * @return $link_create_batch
     */
    protected function linkCreateBatch()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $link_create_batch = $this->url->link(
            'extension/upsmodule/shipments/createBatch&user_token=' . $this->session->data[$constants->user_token],
            true
        );
        return $link_create_batch;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders linkDetailOrder
     *
     * @return $link_detail_order
     */
    protected function linkDetailOrder()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $link_detail_order = $this->url->link(
            'extension/upsmodule/openorders/detailOrder&user_token=' .
            $this->session->data[$constants->user_token],
            true
        );
        return $link_detail_order;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders checkCount
     *
     * @return $check_count_list
     */
    protected function checkCount()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $data[$constants->list_data_orders] = $this->getProductName();
        $check_count_list = count($data[$constants->list_data_orders]);
        if ($check_count_list == 0) {
            $data[$constants->disabled_data] = $constants->disabled_data;
        } else {
            $data[$constants->disabled_data] = '';
        }
        return $check_count_list;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders getForm
     *
     * @return null
     */
    protected function getForm()
    {
        $this->load->model($this->_base_model);
        $this->load->model($this->_country_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->checkLink();
        $url = '';
        $request = $this->request->get;
        $page = 1;
        if (isset($this->request->get['page']) && ctype_digit($this->request->get['page'])) {
            $url .= $this->orderPage . $this->request->get['page'];
            $page = $this->request->get['page'];
        } else {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        $data['list_account'] = $this->model_extension_upsmodule_account->getListAccount();
        $data[$constants->list_data_orders] = $this->getProductName();
        foreach ($data[$constants->list_data_orders] as $key => $value) {
            $data['countID'] = $value['id'];
        }
        $count_list = $this->checkCount();
        $data['urlopenorders'] = $this->url->link(
            'extension/upsmodule/openorders/index&user_token=' . $this->session->data[$constants->user_token],
            true
        );
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data['text_create_single_shipments'] = $this->language->get('text_create_single_shipments');
        $data['text_create_batch_shipments'] = $this->language->get('text_create_batch_shipments');
        $data['text_export_all_orders'] = $this->language->get('text_export_all_orders');
        $data['text_export_open_orders'] = $this->language->get('text_export_open_orders');
        $data[$constants->text_open_orders] = $this->language->get($constants->text_open_orders);
        $data['text_shipments'] = $this->language->get('text_shipments');
        $data['text_archived_orders'] = $this->language->get('text_archived_orders');
        $data['text_id_order'] = $this->language->get('text_id_order');
        $data['text_create_date'] = $this->language->get('text_create_date');
        $data['text_create_time'] = $this->language->get('text_create_time');
        $data['text_product'] = $this->language->get('text_product');
        $data['text_delivery_address'] = $this->language->get('text_delivery_address');
        $data['text_shipping_service'] = $this->language->get('text_shipping_service');
        $data['text_cod'] = $this->language->get('text_cod');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_archiving_orders'] = $this->language->get('text_archiving_orders');
        $data['text_archiving_orders_modal_content'] = $this->language->get('text_archiving_orders_modal_content');
        $data['button_cancel'] = $this->language->get('button_cancel');
        $data['button_ok'] = $this->language->get('button_ok');
        $data['text_OpenOrder'] = $this->language->get('text_OpenOrder');
        $data['text_ArcCustomer'] = $this->language->get('text_ArcCustomer');
        $data['text_ArcProduct'] = $this->language->get('text_ArcProduct');
        $data['text_ArcAddress'] = $this->language->get('text_ArcAddress');
        $data['text_OpenPhoneNumber'] = $this->language->get('text_OpenPhoneNumber');
        $data['text_OpenEmail'] = $this->language->get('text_OpenEmail');
        $data['text_ArcShippingService'] = $this->language->get('text_ArcShippingService');
        $data['text_OpenAccessPoint'] = $this->language->get('text_OpenAccessPoint');
        $data['text_ArcAccessorialService'] = $this->language->get('text_ArcAccessorialService');
        $data['text_ArcOrderValue'] = $this->language->get('text_ArcOrderValue');
        $data['text_ArcPaymentStatus'] = $this->language->get('text_ArcPaymentStatus');
        $data['text_order_exist'] = $this->language->get('text_order_exist');
        $data[$constants->ups_access_point] = $this->language->get($constants->ups_access_point);
        $data[$constants->ups_home] = $this->language->get($constants->ups_home);
        //Create batch shipment
        $data['text_home'] = $this->language->get('text_home');
        $data['text_OpenProcessShipment'] = $this->language->get('text_OpenProcessShipment');
        $data['text_OpenAccountNumber'] = $this->language->get('text_OpenAccountNumber');
        $data['text_OpenOrderProcessed'] = $this->language->get('text_OpenOrderProcessed');
        $data['text_OpenCreateShipment'] = $this->language->get('text_OpenCreateShipment');
        $data['openorders'] = $this->linkOpenOrder();
        $data['shipments'] = $this->linkShipment();
        $data['archivedorders'] = $this->linkArchived();
        $data['exportCSV'] = $this->linkExportCsv();
        $data['updateStatusArchivedOrders'] = $this->linkUpdateArchived();
        $data['urlCreateBatch'] = $this->linkCreateBatch();
        $data['urldetailorder'] = $this->linkDetailOrder();
        $data['text_Action'] = $this->language->get('text_Action');
        //end
        //region data sort
        $url = '';
        if (isset($this->request->get[$constants->order_data]) &&
            (strtolower($this->request->get[$constants->order_data]) != 'asc' &&
            strtolower($this->request->get[$constants->order_data]) != 'desc')
        ) {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        if ($request[$constants->order_data] == 'ASC') {
            $url .= '&order=ASC';
            $data[$constants->sort_ar_orders] = 'asc';
        } else {
            $url .= '&order=DESC';
            $data[$constants->sort_ar_orders] = 'desc';
        }
        //end
        //region page
        $total = $this->model_extension_upsmodule_openorders->getTotalNews();
        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get($constants->config_admin);
        $check_sort = $request['sort'];
        switch ($check_sort) {
            case 'id':
                $pagination->url = $this->url->link(
                    $constants->link_openorders,
                    $constants->token .
                    $constants->sort_id_orders . $url . $constants->sort_page_orders,
                    true
                );
                $data[$constants->curren_page_orders] = $page;
                break;
            case 'datesort':
                $pagination->url = $this->url->link(
                    $constants->link_openorders,
                    $constants->token .
                    $constants->sort_date_orders . $url . $constants->sort_page_orders,
                    true
                );
                $data[$constants->curren_page_orders] = $page;
                break;
            case 'timesort':
                $pagination->url = $this->url->link(
                    $constants->link_openorders,
                    $constants->token .
                    $constants->sort_time_orders . $url . $constants->sort_page_orders,
                    true
                );
                $data[$constants->curren_page_orders] = $page;
                break;
            case $constants->sort_address_orders:
                $pagination->url = $this->url->link(
                    $constants->link_openorders,
                    $constants->token .
                    $constants->sort_ap_name . $url . $constants->sort_page_orders,
                    true
                );
                $data[$constants->curren_page_orders] = $page;
                break;
            case $constants->sort_service_orders:
                $pagination->url = $this->url->link(
                    $constants->link_openorders,
                    $constants->token .
                    $constants->sort_service_name_orders . $url . $constants->sort_page_orders,
                    true
                );
                $data[$constants->curren_page_orders] = $page;
                break;
            case $constants->payment_code:
                $pagination->url = $this->url->link(
                    $constants->link_openorders,
                    $constants->token .
                    $constants->sort_payment_code . $url . $constants->sort_page_orders,
                    true
                );
                $data[$constants->curren_page_orders] = $page;
                break;
            default:
                $pagination->url = "";
                $data[$constants->curren_page_orders] = '';
                break;
        }
        $url = '';
        if ($request[$constants->order_data] == 'ASC') {
            $url .= '&order=DESC';
            $data[$constants->sort_ar_orders] = 'asc';
        } else {
            $url .= '&order=ASC';
            $data[$constants->sort_ar_orders] = 'desc';
        }
        $data['sort_id'] = $this->url->link(
            $constants->link_openorders,
            $constants->token . $constants->sort_id_orders . $url,
            true
        );
        $data['sort_date'] = $this->url->link(
            $constants->link_openorders,
            $constants->token . $constants->sort_date_orders . $url,
            true
        );
        $data['sort_time'] = $this->url->link(
            $constants->link_openorders,
            $constants->token . $constants->sort_time_orders . $url,
            true
        );
        $data['sort_address'] = $this->url->link(
            $constants->link_openorders,
            $constants->token . $constants->sort_ap_name . $url,
            true
        );
        $data['sort_service'] = $this->url->link(
            $constants->link_openorders,
            $constants->token . $constants->sort_service_name_orders . $url,
            true
        );
        $data['sort_cod'] = $this->url->link(
            $constants->link_openorders,
            $constants->token . $constants->sort_payment_code . $url,
            true
        );
        $data['sort'] = $request['sort'];
        $data[$constants->order_data] = $request[$constants->order_data];
        if (isset($data[$constants->curren_page_orders])) {
            $data['sort_id'] = $this->url->link(
                $constants->link_openorders,
                $constants->token . $constants->sort_id_orders . $url .
                $constants->page_data . $data[$constants->curren_page_orders],
                true
            );
            $data['sort_date'] = $this->url->link(
                $constants->link_openorders,
                $constants->token . $constants->sort_date_orders . $url .
                $constants->page_data . $data[$constants->curren_page_orders],
                true
            );
            $data['sort_time'] = $this->url->link(
                $constants->link_openorders,
                $constants->token . $constants->sort_time_orders . $url .
                $constants->page_data . $data[$constants->curren_page_orders],
                true
            );
            $data['sort_address'] = $this->url->link(
                $constants->link_openorders,
                $constants->token . $constants->sort_ap_name . $url .
                $constants->page_data . $data[$constants->curren_page_orders],
                true
            );
            $data['sort_service'] = $this->url->link(
                $constants->link_openorders,
                $constants->token . $constants->sort_service_name_orders . $url .
                $constants->page_data . $data[$constants->curren_page_orders],
                true
            );
            $data['sort_cod'] = $this->url->link(
                $constants->link_openorders,
                $constants->token . $constants->sort_payment_code . $url .
                $constants->page_data . $data[$constants->curren_page_orders],
                true
            );
        }
        $total_page_1 = 0;
        $total_page_2 = 0;
        if ($total) {
            $total_page_1 = (($page - 1) * $this->config->get($constants->config_admin)) + 1;
        }
        if ((($page - 1) * $this->config->get($constants->config_admin)) > ($total - $this->config->get($constants->config_admin))
        ) {
            $total_page_2 = $total;
        } else {
            $total_page_2 = (($page - 1) * $this->config->get($constants->config_admin)) +
            $this->config->get($constants->config_admin);
        }
        $data['pagination'] = $pagination->render();
        $data['results'] = sprintf(
            $this->language->get('text_pagination'),
            $total_page_1,
            $total_page_2,
            $total,
            ceil($total / $this->config->get($constants->config_admin))
        );
        //end
        $data[$constants->user_token] = $this->session->data[$constants->user_token];
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['createbatch'] = $this->load->view('extension/upsmodule/createbatchshipment', $data);
        //region Modal Create Single Shipment
        /*
         * @author: UPS <<EMAIL>>
         */
        $modal_data['txt_popup_title'] = $this->language->get('txt_popup_title');
        $modal_data['txt_popup_account_number'] = $this->language->get('txt_popup_account_number');
        $modal_data['txt_popup_ship_from'] = $this->language->get('txt_popup_ship_from');
        $modal_data['txt_popup_ship_to'] = $this->language->get('txt_popup_ship_to');
        $modal_data['txt_popup_edit_ship_to_name'] = $this->language->get('txt_popup_edit_ship_to_name');
        $modal_data['txt_popup_edit_ship_to_address'] = $this->language->get('txt_popup_edit_ship_to_address');
        $modal_data['txt_popup_edit_ship_to_postal_code'] = $this->language->get('txt_popup_edit_ship_to_postal_code');
        $modal_data['txt_popup_edit_ship_to_city'] = $this->language->get('txt_popup_edit_ship_to_city');
        $modal_data['txt_popup_edit_ship_to_state'] = $this->language->get('txt_popup_edit_ship_to_state');
        $modal_data['txt_popup_edit_ship_to_country'] = $this->language->get('txt_popup_edit_ship_to_country');
        $modal_data['txt_popup_edit_ship_to_phone_number']
            = $this->language->get('txt_popup_edit_ship_to_phone_number');
        $modal_data['txt_popup_edit_ship_to_email'] = $this->language->get('txt_popup_edit_ship_to_email');
        $modal_data['txt_popup_edit_ship_to_note'] = $this->language->get('txt_popup_edit_ship_to_note');
        $modal_data['txt_popup_shipping_service'] = $this->language->get('txt_popup_shipping_service');
        $modal_data['txt_popup_accessorial_service'] = $this->language->get('txt_popup_accessorial_service');
        $modal_data['txt_popup_package'] = $this->language->get('txt_popup_package');
        $modal_data['txt_popup_package_label'] = $this->language->get('txt_popup_package_label');
        $modal_data['txt_popup_customer_package'] = $this->language->get('txt_popup_customer_package');
        $modal_data['txt_popup_customer_package_weight'] = $this->language->get('txt_popup_customer_package_weight');
        $modal_data['txt_popup_customer_package_length'] = $this->language->get('txt_popup_customer_package_length');
        $modal_data['txt_popup_customer_package_width'] = $this->language->get('txt_popup_customer_package_width');
        $modal_data['txt_popup_customer_package_height'] = $this->language->get('txt_popup_customer_package_height');
        $modal_data['btn_popup_add_package'] = $this->language->get('btn_popup_add_package');
        $modal_data['txt_popup_view_estimated_shipping_fee']
            = $this->language->get('txt_popup_view_estimated_shipping_fee');
        $modal_data[$constants->btn_cancel_edit] = $this->language->get($constants->btn_cancel_edit);
        $modal_data['btn_popup_edit'] = $this->language->get('btn_popup_edit');
        $modal_data['btn_popup_create_shipment'] = $this->language->get('btn_popup_create_shipment');
        $modal_data['ship_to_service_ap'] = $this->language->get($constants->ups_access_point);
        $modal_data['ship_to_service_add'] = $this->language->get($constants->ups_home);

        //change request for us
        $country_data = $this->model_extension_upsmodule_country->getCountryCode();
        $data['ups_footer'] = '';
        $modal_data['txt_country_code'] = '';
        if (!empty($country_data)) {
            $modal_data['txt_country_code'] = $country_data[$constants->value];
            if (strtolower($country_data[$constants->value]) == "us") {
                $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
                $modal_data['txt_accessorial_service_detail'] = $this->language->get('txt_popup_accessorial_service_detail');
                $modal_data['txt_additional_handling_detail'] = $this->language->get('txt_popup_additional_handling_detail');
                $modal_data['txt_residential_address_detail'] = $this->language->get('txt_popup_residential_address_detail');
                $modal_data['txt_to_home_cod_detail'] = $this->language->get('txt_popup_to_home_cod_detail');
                $modal_data['txt_popup_accessorial_service'] = $this->language->get('txt_popup_accessorial_service_US');
            }
        }

        $modal_data['urldetailorder'] = $this->url->link(
            'extension/upsmodule/openorders/detailOrder&user_token=' .
            $this->session->data[$constants->user_token],
            true
        );
        $modal_data['create_shipment_url'] = $this->url->link(
            'extension/upsmodule/shipments/createShipment&user_token='
            . $this->session->data[$constants->user_token],
            true
        );
        $modal_data['api_rating_url'] = $this->url->link(
            'extension/upsmodule/shipments/rateShipment&user_token=' .
            $this->session->data[$constants->user_token],
            true
        );
        //get list account to create
        $modal_data['list_state'] = json_encode($this->model_extension_upsmodule_opencartsetting->getState());
        $modal_data['list_country'] = $this->model_extension_upsmodule_opencartsetting->getTotalCountrys();
        $modal_data['listCountry'] = json_encode($this->model_extension_upsmodule_opencartsetting->getTotalCountrys());
        $modal_data['services_to_ap'] = $this->checkServiceAp();
        $modal_data['services_to_add'] = $this->checkServiceAdd();
        $accessorials = $this->model_extension_upsmodule_accessorial->getListAllAccessorial();
        $arr_accessorials = [];
        foreach ($accessorials as $key => $val) {
            $val['translate_pl'] = $this->language->get($val['accessorial_key']);
            $arr_accessorials[$key] = $val;
        }
        $list_account = $this->model_extension_upsmodule_account->getListAccount();
        if (!empty($list_account)) {
            foreach ($list_account as $key => $value) {
                $account_data = (object) $value;
                $country_data
                    = $this->model_extension_upsmodule_opencartsetting->getCountryByCountryCode($account_data->country);
                $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByStateCode(
                    $account_data->state_province_code,
                    $account_data->country
                );
                $list_account[$key]['country_name'] = (!empty($country_data)) ? $country_data['name'] : '';
                $list_account[$key]['state_name'] = (!empty($state_data)) ? $state_data['name'] : '';
            }
        }
        $modal_data['list_account'] = $list_account;
        $account_default['ap'] = $this->checkAccountDefaultAp();
        $account_default['add'] = $this->checkAccountDefaultAdd();
        $modal_data['account_default'] = $account_default;
        $modal_data['listAccessorial'] = json_encode($arr_accessorials);
        $modal_data['arrAccessorials'] = $arr_accessorials;
        $modal_data['modal_loading'] = $this->load->view('extension/upsmodule/modalloading');
        //get list package
        $modal_data['list_package'] = [];

        $package_list = $this->model_extension_upsmodule_packagedimension->listProductDimension();
        foreach ($package_list as $package) {
            $packageObj = new stdClass();
            $packageObj->length = $package['length'];
            $packageObj->width = $package['width'];
            $packageObj->height = $package['height'];
            $packageObj->unit_dimension = $package['unit_dimension'];
            $packageObj->weight = $package['weight'];
            $packageObj->unit_weight = $package['unit_weight'];
            $package['json_data'] = $packageObj;
            $modal_data['list_package'][] = $package;
        }
        $data['modal_create_single_shipment'] = $this->load->view(
            'extension/upsmodule/modalcreatesingleshipment',
            $modal_data
        );
        //end
        $data['open_order'] = $this->url->link(
            'extension/upsmodule/openorders&user_token=' .
            $this->session->data[$constants->user_token] . $constants->sort_id_orders .
            $constants->link_sort_page
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $this->response->setOutput($this->load->view($constants->link_openorders, $data));
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders checkAccountDefaultAP
     *
     * @return $check_default_ap
     */
    public function checkAccountDefaultAp()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //account default set up in shipping service screen.
        $list_account_default_id = $this->model_extension_upsmodule_opencartsetting->getAccountDefault();
        $account_default = [];
        //account default set up in account screen.
        $check_default_ap = '';
        $config_account_default = $this->model_extension_upsmodule_account->getAccountDefault();
        $config_account_default_id = (!empty($config_account_default)) ? $config_account_default['account_id'] : 1;
        if (!empty($list_account_default_id)) {
            foreach ($list_account_default_id as $item) {
                if ($item['key'] == 'ups_shipping_choose_account_number_ap' && $item[$constants->value_data]) {
                    $account
                        = $this->model_extension_upsmodule_account->getInfoAccount($item[$constants->value_data]);
                    if (!empty($account)) {
                        $check_default_ap = $item[$constants->value_data];
                        continue;
                    }
                    $check_default_ap = $config_account_default_id;
                }
            }
        } else {
            $check_default_ap = $config_account_default_id;
        }
        return $check_default_ap;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders checkAccountDefaultADD
     *
     * @return $check_default_add
     */
    public function checkAccountDefaultAdd()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //account default set up in shipping service screen.
        $list_account_default_id = $this->model_extension_upsmodule_opencartsetting->getAccountDefault();
        $account_default = [];
        //account default set up in account screen.
        $check_default_add = '';
        $config_account_default = $this->model_extension_upsmodule_account->getAccountDefault();
        $config_account_default_id = (!empty($config_account_default)) ? $config_account_default['account_id'] : 1;
        if (!empty($list_account_default_id)) {
            foreach ($list_account_default_id as $item) {
                if ($item['key'] == 'ups_shipping_choose_account_number_add' && $item[$constants->value_data]) {
                    $account
                        = $this->model_extension_upsmodule_account->getInfoAccount($item[$constants->value_data]);
                    if (!empty($account)) {
                        $check_default_add = $item[$constants->value_data];
                        continue;
                    }
                    $check_default_add = $config_account_default_id;
                }
            }
        } else {
            $check_default_add = $config_account_default_id;
        }
        return $check_default_add;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders checkServiceAP
     *
     * @return $check_ap
     */
    public function checkServiceAp()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $list_country_code = $this->model_extension_upsmodule_opencartsetting->getCountryCode();
        if ($list_country_code) {
            $check_ap = $this->model_extension_upsmodule_shippingservice->listShippingServices(
                $list_country_code[$constants->value_data],
                'AP'
            );
        }
        return $check_ap;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders checkServiceADD
     *
     * @return $check_add
     */
    public function checkServiceAdd()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $list_country_code = $this->model_extension_upsmodule_opencartsetting->getCountryCode();
        if ($list_country_code) {
            $check_add = $this->model_extension_upsmodule_shippingservice->listShippingServices(
                $list_country_code[$constants->value_data],
                'ADD'
            );
        }
        return $check_add;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders checkListIDCSV
     *
     * @return $list_orders_id
     */
    public function checkListIdCsv()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_openorders);
        $this->load->model($constants->link_opencartsetting);
        $this->load->language($constants->link_translate);
        $request = $this->request->post;
        $order_ids = html_entity_decode($request[$constants->order_id]);
        $order_ids = str_replace('"', '', substr($order_ids, 1, -1));
        $order_by = $request['OrderBy'];
        if ($request[$constants->order_id] != '') {
            $list_orders_id = $this->model_extension_upsmodule_openorders->getExportOrderData($order_ids, $order_by);
        } else {
            $list_orders_id = $this->model_extension_upsmodule_openorders->getExportAllData($order_by);
        }
        return $list_orders_id;
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders exportCSV
     *
     * @return null
     */
    public function exportCsv()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $list_open_orders = $this->checkListIdCsv();
        $data_export = [
            ['Order ID', 'Order Date', 'Order Time', 'COD', 'COD Amount', 'COD Currency', 'Current State',
            'Total Paid', 'Total Products', 'Shipping Service', 'Accessorials Service', 'Product Name',
            'Merchant UPSaccount Number', 'Customer Last Name', 'Customer First Name', 'Customer Address line 1',
            'Customer Address line 2', 'Customer Address line 3', 'Customer PostalCode', 'Customer Phone',
            'Customer City', 'Customer StateOrProvince', 'Customer Country', 'Customer Email',
            'AlternateDeliveryAddressIndicator', 'UPSAccessPointID', 'Access Point Address line 1',
            'Access Point Address line 2', 'Access Point Address line 3', 'Access Point City',
            'Access Point StateOrProvince', 'Access Point PostalCode', 'Access Point Country']
        ];
        foreach ($list_open_orders as $key => $open_order) {
            if ($open_order['decimal_place'] != '') {
                $decimal_value = $open_order['decimal_place'];
            } else {
                $decimal_value = 0;
            }
            $cod = '';
            $cod_amount = '';
            $cod_currency = '';
            if ($open_order[$constants->payment_code] == 'cod') {
                $cod = $this->language->get('text_Yes');
                $cod_amount = number_format($open_order[$constants->total_all], $decimal_value);
                $cod_currency = $open_order['currency_code'];
            } else {
                $cod = $this->language->get('text_No');
                $cod_amount = '';
                $cod_currency = '';
            }
            $access_cod = $this->language->get($constants->ups_access_point);
            $home_cod = $this->language->get($constants->ups_home);
            $accessorial_array = [];
            $list_accessorial = [];
            if (!empty($open_order[$constants->access_service])) {
                $accessorial_array = json_decode($open_order[$constants->access_service], true);
                foreach ($accessorial_array as $key1 => $value) {
                    $accessorial_translate = $this->language->get($key1);
                    $accessorial_array[$key1] = $accessorial_translate;
                }
            }
            $list_accessorial = $accessorial_array;
            $accessorial_shipment = [];
            $accessorial_value = '';
            foreach ($list_accessorial as $key => $value) {
                $accessorial_shipment[] = $value;
            }
            if ($open_order[$constants->service_type] == 'AP' && $open_order[$constants->payment_code] == 'cod') {
                $accessorial_shipment[] = $access_cod;
            } elseif ($open_order[$constants->payment_code] == 'cod') {
                $accessorial_shipment[] = $home_cod;
            } else {
                $accessorial_value = '';
            }
            if ($open_order[$constants->service_type] == 'AP') {
                $alternaet_delivery = 1;
            } else {
                $alternaet_delivery = 0;
            }
            if ($open_order[$constants->service_type] == 'AP' && $open_order[$constants->service_type] != '') {
                $country_ap = $open_order[$constants->shipping_country];
            } else {
                $country_ap = '';
            }
            $service_name = str_replace("&reg;", "", $open_order[$constants->service_name]);
            $ups_accesspoint_id = '';
            $address_line_1 = '';
            $address_line_2 = '';
            $address_line_3 = '';
            $access_city = '';
            $access_state = '';
            $access_postal_code = '';
            if (($open_order[$constants->access_point_id] != '' || $open_order[$constants->access_point_id] != null)
            && $open_order[$constants->service_type] == 'AP') {
                $ups_accesspoint_id = $open_order[$constants->access_point_id];
            }
            if (($open_order[$constants->data_ap_name] != '' || $open_order[$constants->data_ap_name] != null)
            && $open_order[$constants->service_type] == 'AP') {
                $address_line_1 = $open_order[$constants->data_ap_name];
            }
            if (($open_order[$constants->ap_address1] != '' || $open_order[$constants->ap_address1] != null)
            && $open_order[$constants->service_type] == 'AP') {
                $address_line_2 = html_entity_decode($open_order[$constants->ap_address1]);
            }
            if (($open_order[$constants->ap_address2] != '' || $open_order[$constants->ap_address2] != null)
            && $open_order[$constants->service_type] == 'AP') {
                $address_line_3 = html_entity_decode($open_order[$constants->ap_address2]);
            }
            if (($open_order[$constants->ap_city] != '' || $open_order[$constants->ap_city] != null)
            && $open_order[$constants->service_type] == 'AP') {
                $access_city = $open_order[$constants->ap_city];
            }
            if (($open_order[$constants->ap_state_orders] != '' || $open_order[$constants->ap_state_orders] != null)
            && $open_order[$constants->service_type] == 'AP') {
                $access_state = $open_order[$constants->shipping_zone_orders];
            }
            if (($open_order[$constants->ap_post_code] != '' || $open_order[$constants->ap_post_code] != null)
            && $open_order[$constants->service_type] == 'AP') {
                $access_postal_code = $open_order[$constants->ap_post_code];
            }
            $row_export = [
                'order_id' => $open_order['order_id'],
                'order_date' => date('Y/m/d', strtotime($open_order['datesort'])),
                'order_time' => $open_order['timesort'],
                'cod' => $cod,
                'cod_amount' => '"' . $cod_amount . '"',
                'cod_currency' => $cod_currency,
                'currency_state' => '"' . $open_order['name'] . '"',
                'total_paid' => '"' . number_format($open_order[$constants->total_all], $decimal_value) . '"',
                'total_product' => '"' . number_format($open_order['totalProduct'], $decimal_value) . '"',
                'shipping_service' => '"' . $service_name . '"',
                $constants->access_service => '"' . implode(', ', $accessorial_shipment) . '"',
                $constants->product_name => '"' . str_replace(',', ', ', $open_order[$constants->list_product_name]) . '"',
                'merchant_ups_account_number' => '',
                'customer_last_name' => $open_order['shipping_lastname'],
                'customer_first_name' => $open_order['shipping_firstname'],
                'customer_address_1' => '"' . str_replace(
                    $constants->char_special,
                    '',
                    html_entity_decode($open_order[$constants->shipping_address_1_orders])
                ) . '"',
                'customer_address_2' => '"' . str_replace(
                    $constants->char_special,
                    '',
                    html_entity_decode($open_order[$constants->shipping_address_2_orders])
                ) . '"',
                'customer_address_3' => '',
                'customer_postal_code' => ' ' .  $open_order[$constants->shipping_post_code],
                'customer_phone' => $open_order['telephone'],
                'customer_city' => $open_order[$constants->shipping_city],
                'customer_state_or_province' => $open_order[$constants->shipping_zone_orders],
                'customer_country' => $open_order[$constants->shipping_country],
                'customer_email' => $open_order['email'],
                'alternaet_delivery_address_indicator' => $alternaet_delivery,
                'UPSAcessPointID' => $ups_accesspoint_id,
                'Access_Point_Address_line_1' => '"' . $address_line_1 . '"',
                'Access_Point_Address_line_2' => '"' . $address_line_2 . '"',
                'Access_Point_Address_line_3' => '"' .$address_line_3 . '"',
                'Access_Point_City' => $access_city,
                'Access_Point_StateOrProvince' => $access_state,
                'Access_Point_PostalCode' => $access_postal_code,
                'Access_Point_Country' => $country_ap,
            ];
            array_push($data_export, $row_export);
        }
        $file_name = "orders_data_" . date("dmy") . ".csv";
        header('Content-type: text/csv');
        header('Content-Type: application/force-download; charset=UTF-8');
        header('Cache-Control: no-store, no-cache');
        header("Content-Disposition: attachment; filename=\"$file_name\"");
        $this->exportCsvFile($data_export);
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders ExportCSVFile
     *
     * @param string $records //The records
     *
     * @return null
     */
    function exportCsvFile($records)
    {
        $fh = fopen('php://output', 'w');
        fprintf($fh, chr(0xEF) . chr(0xBB) . chr(0xBF));
        $heading = false;
        if (!empty($records)) {
            foreach ($records as $row) {
                if (!$heading) {
                    fwrite($fh, implode(",", $row));
                    $heading = true;
                } else {
                    fwrite($fh, "\n" . implode(",", $row));
                }
            }
            fclose($fh);
        }
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders updateStatusArchivedOrders
     *
     * @return null
     */
    public function updateStatusArchivedOrders()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_openorders);
        $request = $this->request->post;
        $order_ids = html_entity_decode($request[$constants->order_id]);
        $order_ids = str_replace('"', '', substr($order_ids, 1, -1));
        if ($request[$constants->order_id] != '') {
            $list_open_orders = $this->model_extension_upsmodule_openorders->updateStatusArchivedOrders($order_ids);
            $update_date_archived = $this->model_extension_upsmodule_openorders->updateDateArchivedOrders($order_ids);
        }
        $this->response->redirect(
            $this->url->link(
                $constants->link_openorders,
                $constants->token .
                $constants->sort_id_orders . $constants->link_sort_page,
                true
            )
        );
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders detailOrder
     *
     * @return null
     */
    public function detailOrder()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_openorders);
        $this->load->model($constants->link_opencartsetting);
        $this->load->language($constants->link_translate);
        $request = $this->request->post;
        $order_id = $request['list_id_orders'];
        //region check oder
        $check_shipment = $this->model_extension_upsmodule_openorders->checkOderShipment($order_id);
        //end
        if (!empty($check_shipment)) {
            $detail_order[$constants->check_shipment] = $check_shipment;
            echo json_encode($detail_order);
        } else {
            if (!is_numeric($order_id)) {
                $detail_order = $this->model_extension_upsmodule_openorders->getMultiDetailOrder($order_id);
                $order_value = 0;
                foreach ($detail_order as $key => $value) {
                    $product_name = [];
                    $product_name = explode(',', $value[$constants->list_product_name]);
                    $detail_order[$key][$constants->product_name] = implode($constants->drop_down, $product_name);
                    // handling service name
                    if ($detail_order[$key][$constants->service_symbol_orders] == '&trade;') {
                        $detail_order[$key][$constants->service_name_info_orders] = 'UPS Access Point&trade; Economy';
                    } else {
                        $detail_order[$key][$constants->service_name_info_orders]
                            = $detail_order[$key][$constants->service_name] .
                            $detail_order[$key][$constants->service_symbol_orders];
                    }
                    $detail_order[$key][$constants->date_added_orders]
                        = date('M d, Y, H:i:s', strtotime($detail_order[$key][$constants->date_added_orders]));
                    $order_value = $order_value + (double)filter_var(
                        $detail_order[$key][$constants->total_all],
                        FILTER_SANITIZE_NUMBER_FLOAT,
                        FILTER_FLAG_ALLOW_FRACTION
                    );
                    $address_format_1 = [
                        $detail_order[$key][$constants->shipping_address_1_orders],
                        $detail_order[$key][$constants->shipping_address_2_orders],
                        $detail_order[$key][$constants->shipping_city],
                        $detail_order[$key][$constants->shipping_zone_orders],
                        $detail_order[$key][$constants->shipping_post_code],
                        $detail_order[$key][$constants->shipping_country]
                    ];
                    $address_format = $this->addArray($address_format_1);
                    $detail_order[$key]['address'] = implode($constants->drop_down, $address_format);
                    $ap_address_all = $this->addArray(
                        [$detail_order[$key][$constants->ap_address1],
                        $detail_order[$key][$constants->ap_address2],
                        $detail_order[$key][$constants->ap_address3]]
                    );
                    $detail_order[$key]['ap_address_all'] = implode($constants->drop_down, $ap_address_all);
                    if ($detail_order[$key][$constants->ap_address1] != null) {
                        $detail_order[$key][$constants->ap_address1] = str_replace(
                            $constants->char_special,
                            '',
                            $detail_order[$key][$constants->ap_address1]
                        );
                    } else {
                        $detail_order[$key][$constants->ap_address1] = '';
                    }
                    if ($detail_order[$key][$constants->ap_address2] != null) {
                        $detail_order[$key][$constants->ap_address2] = str_replace(
                            $constants->char_special,
                            '',
                            $detail_order[$key][$constants->ap_address2]
                        );
                    } else {
                        $detail_order[$key][$constants->ap_address2] = '';
                    }
                    if ($detail_order[$key][$constants->ap_address3] != null) {
                        $detail_order[$key][$constants->ap_address3] = str_replace(
                            $constants->char_special,
                            '',
                            $detail_order[$key][$constants->ap_address3]
                        );
                    } else {
                        $detail_order[$key][$constants->ap_address3] = '';
                    }
                    $detail_order[$key][$constants->state_data] = '';
                    $detail_order[$key][$constants->state_name_orders] = '';
                    if ($detail_order[$key][$constants->service_type] == 'AP'
                        && !empty($detail_order[$key][$constants->ap_country_orders])
                    ) {
                        $country_data = $this->model_extension_upsmodule_opencartsetting->getCountryByCountryCode(
                            $detail_order[$key][$constants->ap_country_orders]
                        );
                        if (!empty($country_data)) {
                            $detail_order[$key]['ap_country_name'] = $country_data['name'];
                        }
                    } elseif ($detail_order[$key][$constants->service_type] == 'AP'
                        && !empty($detail_order[$key][$constants->ap_state_orders])
                    ) {
                        $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByStateCode(
                            $detail_order[$key][$constants->ap_state_orders],
                            $detail_order[$key][$constants->ap_country_orders]
                        );
                        if (!empty($state_data)) {
                            $detail_order[$key][$constants->state_data] = $state_data['code'];
                            $detail_order[$key][$constants->state_name_orders] = $state_data['name'];
                        }
                    } else {
                        $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByZoneId(
                            $detail_order[$key]['shipping_zone_id']
                        );
                        if (!empty($state_data)) {
                            $detail_order[$key][$constants->state_data] = $state_data['code'];
                        }
                        $detail_order[$key][$constants->state_name_orders]
                            = $detail_order[$key][$constants->shipping_zone_orders];
                    }
                    $add_address_all = $this->addArray(
                        [$detail_order[$key][$constants->shipping_address_1_orders],
                        $detail_order[$key][$constants->shipping_address_2_orders]]
                    );
                    $detail_order[$key]['add_address_all'] = implode($constants->drop_down, $add_address_all);
                    $accessorial_array = [];
                    if (!empty($detail_order[$key][$constants->access_service])) {
                        $accessorial_array = json_decode($detail_order[$key][$constants->access_service], true);
                        foreach ($accessorial_array as $key1 => $value) {
                            $accessorial_translate = $this->language->get($key1);
                            $accessorial_array[$key1] = $accessorial_translate;
                        }
                    }
                    $detail_order[$key]['accessorial_array'] = $accessorial_array;
                }
                $detail_order['order_value'] = number_format($order_value, 2);
                $detail_order[$constants->check_shipment] = '';
            } else {
                $detail_order = $this->model_extension_upsmodule_openorders->getDetailOrderData($order_id);
                $detail_order['total_all_decimal']
                    = $this->currency->format($detail_order['total'], $detail_order['currency_code'], '', true);
                $product_name = explode(',', $detail_order[$constants->list_product_name]);
                $detail_order[$constants->product_name] = implode($constants->drop_down, $product_name);
                // handling service name
                if ($detail_order[$constants->service_symbol_orders] == '&trade;') {
                    $detail_order[$constants->service_name_info_orders] = 'UPS Access Point&trade; Economy';
                } else {
                    $detail_order[$constants->service_name_info_orders] = $detail_order[$constants->service_name] .
                        $detail_order[$constants->service_symbol_orders];
                }
                $detail_order[$constants->date_added_orders]
                    = date('M d, Y, H:i:s', strtotime($detail_order[$constants->date_added_orders]));
                $detail_order['order_value'] = $detail_order[$constants->total_all];
                $detail_order[$constants->check_shipment] = '';
                $address_format_1 = [
                    $detail_order[$constants->shipping_address_1_orders],
                    $detail_order[$constants->shipping_address_2_orders],
                    $detail_order[$constants->shipping_city],
                    $detail_order[$constants->shipping_zone_orders],
                    $detail_order[$constants->shipping_post_code],
                    $detail_order[$constants->shipping_country]
                ];
                $address_format = $this->addArray($address_format_1);
                $detail_order['address'] = implode($constants->drop_down, $address_format);
                $ap_address_all = $this->addArray(
                    [$detail_order[$constants->ap_address1],
                    $detail_order[$constants->ap_address2],
                    $detail_order[$constants->ap_address3]]
                );
                $detail_order['ap_address_all'] = implode($constants->drop_down, $ap_address_all);
                $address_format_ap = [];
                if (!empty($detail_order[$constants->data_ap_name])) {
                    $address_format_ap[] = $detail_order[$constants->data_ap_name];
                } else {
                    $address_format_ap[] = null;
                }
                if ($detail_order[$constants->ap_address1] != null) {
                    $detail_order[$constants->ap_address1] = str_replace(
                        $constants->char_special,
                        '',
                        $detail_order[$constants->ap_address1]
                    );
                    if (!empty($detail_order[$constants->ap_address1])) {
                        $address_format_ap[] = $detail_order[$constants->ap_address1];
                    }
                } else {
                    $detail_order[$constants->ap_address1] = '';
                }
                if ($detail_order[$constants->ap_address2] != null) {
                    $detail_order[$constants->ap_address2] = str_replace(
                        $constants->char_special,
                        '',
                        $detail_order[$constants->ap_address2]
                    );
                    if (!empty($detail_order[$constants->ap_address2])) {
                        $address_format_ap[] = $detail_order[$constants->ap_address2];
                    }
                } else {
                    $detail_order[$constants->ap_address2] = '';
                }
                if ($detail_order[$constants->ap_address3] != null) {
                    $detail_order[$constants->ap_address3] = str_replace(
                        $constants->char_special,
                        '',
                        $detail_order[$constants->ap_address3]
                    );
                    if (!empty($detail_order[$constants->ap_address3])) {
                        $address_format_ap[] =  $detail_order[$constants->ap_address3];
                    }
                } else {
                    $detail_order[$constants->ap_address3] = '';
                }
                if (!empty($detail_order[$constants->ap_city])) {
                    $address_format_ap[] = $detail_order[$constants->ap_city];
                }
                if (!empty($detail_order[$constants->ap_state_orders])) {
                    $address_format_ap[] = $detail_order[$constants->shipping_zone_orders];
                }
                if (!empty($detail_order[$constants->ap_post_code])) {
                    $address_format_ap[] = $detail_order[$constants->ap_post_code];
                }
                $detail_order[$constants->state_data] = '';
                $detail_order[$constants->state_name_orders] = '';
                if ($detail_order[$constants->service_type] == 'AP'
                    && !empty($detail_order[$constants->ap_country_orders])
                ) {
                    $country_data = $this->model_extension_upsmodule_opencartsetting->getCountryByCountryCode(
                        $detail_order[$constants->ap_country_orders]
                    );
                    if (!empty($country_data)) {
                        $address_format_ap[] = $country_data['name'];
                        $detail_order['ap_country_name'] = $country_data['name'];
                    }
                } elseif ($detail_order[$constants->service_type] == 'AP'
                    && !empty($detail_order[$constants->ap_state_orders])
                ) {
                    $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByStateCode(
                        $detail_order[$constants->ap_state_orders],
                        $detail_order[$constants->ap_country_orders]
                    );
                    if (!empty($state_data)) {
                        $detail_order[$constants->state_data] = $state_data['code'];
                        $detail_order[$constants->state_name_orders] = $state_data['name'];
                    }
                } else {
                    $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByZoneId(
                        $detail_order['shipping_zone_id']
                    );
                    if (!empty($state_data)) {
                        $detail_order[$constants->state_data] = $state_data['code'];
                    }
                    $detail_order[$constants->state_name_orders] = $detail_order[$constants->shipping_zone_orders];
                }
                $detail_order['address_ap'] = implode($constants->drop_down, $address_format_ap);
                $add_address_all = $this->addArray(
                    [$detail_order[$constants->shipping_address_1_orders],
                    $detail_order[$constants->shipping_address_2_orders]]
                );
                $detail_order['add_address_all'] = implode("<br />", $add_address_all);
                $accessorial_array = [];
                if (!empty($detail_order[$constants->access_service])) {
                    $accessorial_array = json_decode($detail_order[$constants->access_service], true);
                    foreach ($accessorial_array as $key => $value) {
                        $accessorial_translate = $this->language->get($key);
                        $accessorial_array[$key] = $accessorial_translate;
                    }
                }
                $detail_order['accessorial_array'] = $accessorial_array;
            }
            echo json_encode($detail_order);
        }
    }

    /**
     * ControllerExtensionUpsmoduleOpenorders addArray
     *
     * @param string $array //The array
     *
     * @return $array_return
     */
    function addArray($array)
    {
        $array_return = [];
        foreach ($array as $key => $value) {
            if (!empty($value)) {
                $array_return[] = $value;
            }
        }
        return $array_return;
    }
}
