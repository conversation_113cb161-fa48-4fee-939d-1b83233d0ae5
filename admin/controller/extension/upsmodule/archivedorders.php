<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleArchivedorders file
 *
 * @category Archivedorders_Controller
 */

class ControllerExtensionUpsmoduleArchivedorders extends Controller
{
    private $_base_model = 'extension/upsmodule/base';
    private $_country_model = 'extension/upsmodule/country';
    /**
     * ControllerExtensionUpsmoduleArchivedorders index
     *
     * @return null
     */
    public function index()
    {
        //load model base
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "archivedorders",
            "extension/upsmodule/archivedorders"
        );
        
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load mode link_translate
        $this->load->language($constants->link_translate);
        //load model setTitle
        $this->document->setTitle($this->language->get($constants->text_archived_orders));
        //load model link_archivedorders
        $this->load->model($constants->link_archivedorders);
        //checkDate
        $check_date = $this->model_extension_upsmodule_archivedorders->checkDateArchivedOrders();
        //checkDate
        foreach ($check_date as $date) {
            $order_id = '';
            $date_create = '';
            $date_update = '';
            $date_check_update = date($constants->ymd);
            //$date['archive_orders']
            if (isset($date['archive_orders'])) {
                $date_create = date($constants->ymd, strtotime($date['archive_orders']));
                $date_update = date($constants->ymd, strtotime(date("Y-m-d", strtotime($date_create))." +90 days"));
                //check dateUpdate and dateCheckUpdate
                if (strtotime($date_update) <= strtotime($date_check_update)) {
                    $order_id = $date['id'];
                    $this->model_extension_upsmodule_archivedorders->checkDeleteDateArchivedOrders($order_id);
                }
            }
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders getProductName
     *
     * @return $listOpenOrderArray
     */
    protected function getProductName()
    {
        //load model base
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $url = '';
        //request
        $request = $this->request->get;
        //check sort page
        $sort = 'id';
        $order = 'DESC';
        $page = 1;
        //check request
        foreach ($request as $key => $val) {
            if ($key != "page" && $key != $constants->sort_order && $key != "sort") {
                $url .= '&' .  $key . '=' . $val;
            }
        }
        //check isset get['page']
        if (isset($this->request->get['page']) && ctype_digit($this->request->get['page'])) {
            $url .= $this->orderPage . $this->request->get['page'];
            $page = $this->request->get['page'];
        } else {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        $url_order = 'DESC';
        if ($request[$constants->sort_order] == 'ASC') {
            $url_order = 'ASC';
        }
        //filter_data
        $filter_data = [
            'sort' => $request['sort'],
            $constants->sort_order => $url_order,
            'page' => $page,
            'limit' => $this->config->get($constants->config_limit_admin),
            'start' => $this->config->get($constants->config_limit_admin) * ($page - 1)
        ];
        //listOpenOrders
        $list_open_orders = $this->model_extension_upsmodule_archivedorders->getListSetting($filter_data);
        //listOpenOrderArray
        $list_open_order_array = [];
        foreach ($list_open_orders as $key => $value) {
            $listProduct = [];
            $listProduct = explode(',', $value[$constants->list_product]);
            $str = "";
            //check count($listProduct)
            if (count($listProduct) > 3) {
                $str .= trim($listProduct[0]) . $constants->br_dropdown . trim($listProduct[1]) .
                    $constants->br_dropdown . trim($listProduct[2]) . '<br /> ...';
            } else {
                $str .= implode($constants->br_dropdown, $listProduct);
            }
            $value[$constants->list_product] = $str;
            $list_open_order_array[$key] = $value;
        }
        return $list_open_order_array;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders linkOpenOrder
     *
     * @return $linkOrder
     */
    protected function linkOpenOrder()
    {
        //load model base
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //linkOrder
        $linkOrder = $this->url->link(
            'extension/upsmodule/openorders',
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . $constants->sort_id . $constants->list_sort_page,
            true
        );
        return $linkOrder;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders linkShipment
     *
     * @return $linkShipment
     */
    protected function linkShipment()
    {
        //load model base
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //linkShipment
        $link_shipment = $this->url->link(
            'extension/upsmodule/shipments',
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . '&sort=shipment_number&order=DESC&page=1',
            true
        );
        return $link_shipment;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders linkArchived
     *
     * @return $linkArchived
     */
    protected function linkArchived()
    {
        //load model base
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //linkArchived
        $link_archived = $this->url->link(
            $constants->link_archivedorders,
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . $constants->sort_id . $constants->list_sort_page,
            true
        );
        return $link_archived;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders linkArchivedDetail
     *
     * @return $linkArchivedDetail
     */
    protected function linkArchivedDetail()
    {
        //load model base
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //linkArchivedDetail
        $link_archived_detail = $this->url->link(
            $constants->link_archivedorders . '/detailOrder&user_token=' .
            $this->session->data[$constants->user_token],
            true
        );
        return $link_archived_detail;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders linkArchivedDetail
     *
     * @return null
     */
    protected function getForm()
    {
        //load model base
        $this->load->model($this->_base_model);
        $this->load->model($this->_country_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $url = '';
        //load request
        $request = $this->request->get;
        $page = 1;
        //check isset get['page']
        if (isset($this->request->get['page']) && ctype_digit($this->request->get['page'])) {
            $url .= $this->orderPage . $this->request->get['page'];
            $page = $this->request->get['page'];
        } else {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        //load getProductName()
        $data['listOpenOrders'] = $this->getProductName();
        $data['url_unarchived_orders'] = $this->url->link(
            'extension/upsmodule/archivedorders/unArchivedOrders&user_token=' .
            $this->session->data[$constants->user_token] . $constants->sort_id .
            $constants->link_sort_page
        );
        //translate
        $data['text_home'] = $this->language->get('text_home');
        $data['text_open_orders'] = $this->language->get('text_open_orders');
        $data['text_shipments'] = $this->language->get('text_shipments');
        $data['text_archived_orders'] = $this->language->get($constants->text_archived_orders);
        $data['text_un_archived_orders'] = $this->language->get("text_un_archived_orders");
        $data['text_id_order'] = $this->language->get('text_id_order');
        $data['text_create_date'] = $this->language->get('text_create_date');
        $data['text_create_time'] = $this->language->get('text_create_time');
        $data['text_product'] = $this->language->get('text_product');
        $data['text_delivery_address'] = $this->language->get('text_delivery_address');
        $data['text_shipping_service'] = $this->language->get('text_shipping_service');
        $data['text_cod'] = $this->language->get('text_cod');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_OpenOrder'] = $this->language->get('text_OpenOrder');
        $data['text_ArcCustomer'] = $this->language->get('text_ArcCustomer');
        $data['text_ArcProduct'] = $this->language->get('text_ArcProduct');
        $data['text_ArcAddress'] = $this->language->get('text_ArcAddress');
        $data['text_OpenPhoneNumber'] = $this->language->get('text_OpenPhoneNumber');
        $data['text_OpenEmail'] = $this->language->get('text_OpenEmail');
        $data['text_ArcShippingService'] = $this->language->get('text_ArcShippingService');
        $data['text_OpenAccessPoint'] = $this->language->get('text_OpenAccessPoint');
        $data['text_ArcAccessorialService'] = $this->language->get('text_ArcAccessorialService');
        $data['text_ArcOrderValue'] = $this->language->get('text_ArcOrderValue');
        $data['text_ArcPaymentStatus'] = $this->language->get('text_ArcPaymentStatus');
        $data['UPS_ACSRL_ACCESS_POINT_COD'] = $this->language->get('UPS_ACSRL_ACCESS_POINT_COD');
        $data['UPS_ACSRL_TO_HOME_COD'] = $this->language->get('UPS_ACSRL_TO_HOME_COD');
        $data['button_ok'] = $this->language->get('button_ok');
        $data['openorders'] = $this->linkOpenOrder();
        $data['shipments'] = $this->linkShipment();
        $data['archivedorders'] = $this->linkArchived();
        $data['urldetailorder'] = $this->linkArchivedDetail();
        $data['modal_loading'] = $this->load->view('extension/upsmodule/modalloading');
        $data['text_unarchived_orders'] = $this->language->get('text_unarchived_orders');
        $data['text_popup_unarchived_orders'] = $this->language->get('text_popup_unarchived_orders');
        $data['text_popup_confirm_unarchived_orders'] = $this->language->get('text_popup_confirm_unarchived_orders');
        $data['button_ok'] = $this->language->get('button_ok');
        $data['button_cancel'] = $this->language->get('button_cancel');
        //region data sort
        $url = '';
        if (isset($this->request->get[$constants->order_data]) &&
            (strtolower($this->request->get[$constants->order_data]) != 'asc' &&
            strtolower($this->request->get[$constants->order_data]) != 'desc')
        ) {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        if ($request[$constants->sort_order] == 'ASC') {
            $url .= '&order=ASC';
            $data[$constants->sort_ar] = 'asc';
        } else {
            $url .= '&order=DESC';
            $data[$constants->sort_ar] = 'desc';
        }
        //end
        //total
        $total = $this->model_extension_upsmodule_archivedorders->getTotalNews();
        //pagination
        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get($constants->config_limit_admin);
        //$request['sort']
        $checkSort = $request['sort'];
        switch ($checkSort) {
            case 'id':
                //id
                $pagination->url = $this->url->link(
                    $constants->link_archivedorders,
                    $constants->user_token_ .
                    $this->session->data[$constants->user_token] . $constants->sort_id . $url .
                    $constants->sort_page,
                    true
                );
                $data[$constants->curren_page] = $page;
                break;
            case 'datesort':
                //datesort
                $pagination->url = $this->url->link(
                    $constants->link_archivedorders,
                    $constants->user_token_ .
                    $this->session->data[$constants->user_token] . $constants->sort_date . $url .
                    $constants->sort_page,
                    true
                );
                $data[$constants->curren_page] = $page;
                break;
            case 'timesort':
                //timesort
                $pagination->url = $this->url->link(
                    $constants->link_archivedorders,
                    $constants->user_token_ .
                    $this->session->data[$constants->user_token] . $constants->sort_time . $url .
                    $constants->sort_page,
                    true
                );
                $data[$constants->curren_page] = $page;
                break;
            case $constants->sort_address:
                //sort_address
                $pagination->url = $this->url->link(
                    $constants->link_archivedorders,
                    $constants->user_token_ .
                    $this->session->data[$constants->user_token] . $constants->sort_name . $url .
                    $constants->sort_page,
                    true
                );
                $data[$constants->curren_page] = $page;
                break;
            case $constants->sort_service:
                //sort_service
                $pagination->url = $this->url->link(
                    $constants->link_archivedorders,
                    $constants->user_token_ .
                    $this->session->data[$constants->user_token] . $constants->sort_service_name . $url .
                    $constants->sort_page,
                    true
                );
                $data[$constants->curren_page] = $page;
                break;
            case 'payment_code':
                //payment_code
                $pagination->url = $this->url->link(
                    $constants->link_archivedorders,
                    $constants->user_token_ .
                    $this->session->data[$constants->user_token] . $constants->sort_payment . $url .
                    $constants->sort_page,
                    true
                );
                $data[$constants->curren_page] = $page;
                break;
            default:
                //pagination
                $pagination->url = "";
                $data[$constants->curren_page] = '';
                break;
        }
        //check link
        $url = '';
        if ($request[$constants->sort_order] == 'ASC') {
            $url .= '&order=DESC';
            $data[$constants->sort_ar] = 'asc';
        } else {
            $url .= '&order=ASC';
            $data[$constants->sort_ar] = 'desc';
        }
        //sort_id
        $data['sort_id'] = $this->url->link(
            $constants->link_archivedorders,
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . $constants->sort_id . $url,
            true
        );
            //sort_date
        $data['sort_date'] = $this->url->link(
            $constants->link_archivedorders,
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . $constants->sort_date . $url,
            true
        );
            //sort_time
        $data['sort_time'] = $this->url->link(
            $constants->link_archivedorders,
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . $constants->sort_time . $url,
            true
        );
            //sort_address
        $data['sort_address'] = $this->url->link(
            $constants->link_archivedorders,
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . $constants->sort_name . $url,
            true
        );
            //sort_service
        $data['sort_service'] = $this->url->link(
            $constants->link_archivedorders,
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . $constants->sort_service_name . $url,
            true
        );
            //sort_cod
        $data['sort_cod'] = $this->url->link(
            $constants->link_archivedorders,
            $constants->user_token_ .
            $this->session->data[$constants->user_token] . $constants->sort_payment . $url,
            true
        );
            //sort
        $data['sort'] = $request['sort'];
        $data[$constants->sort_order] = $request[$constants->sort_order];
        //curren_page
        if (isset($data[$constants->curren_page])) {
            //sort_id
            $data['sort_id'] = $this->url->link(
                $constants->link_archivedorders,
                $constants->user_token_ .
                $this->session->data[$constants->user_token] . $constants->sort_id . $url .
                $constants->order_page . $data[$constants->curren_page],
                true
            );
            //sort_date
            $data['sort_date'] = $this->url->link(
                $constants->link_archivedorders,
                $constants->user_token_ .
                $this->session->data[$constants->user_token] . $constants->sort_date . $url .
                $constants->order_page . $data[$constants->curren_page],
                true
            );
            //sort_time
            $data['sort_time'] = $this->url->link(
                $constants->link_archivedorders,
                $constants->user_token_ .
                $this->session->data[$constants->user_token] . $constants->sort_time . $url .
                $constants->order_page . $data[$constants->curren_page],
                true
            );
            //sort_address
            $data['sort_address'] = $this->url->link(
                $constants->link_archivedorders,
                $constants->user_token_ .
                $this->session->data[$constants->user_token] . $constants->sort_name . $url .
                $constants->order_page . $data[$constants->curren_page],
                true
            );
            //sort_service
            $data['sort_service'] = $this->url->link(
                $constants->link_archivedorders,
                $constants->user_token_ .
                $this->session->data[$constants->user_token] . $constants->sort_service_name . $url .
                $constants->order_page . $data[$constants->curren_page],
                true
            );
            //sort_cod
            $data['sort_cod'] = $this->url->link(
                $constants->link_archivedorders,
                $constants->user_token_ .
                $this->session->data[$constants->user_token] . $constants->sort_payment . $url .
                $constants->order_page . $data[$constants->curren_page],
                true
            );
        }
        //$data['pagination']
        $data['pagination'] = $pagination->render();
        //$data['results']
        $page_total_1 = 0;
        $page_total_2 = '';
        if (($total)) {
            $page_total_1 = (($page - 1) * $this->config->get($constants->config_limit_admin)) + 1;
        }
        if ((($page - 1) * $this->config->get($constants->config_limit_admin))
        > ($total - $this->config->get($constants->config_limit_admin))) {
            $page_total_2 = $total;
        } else {
            $page_total_2 = ((($page - 1) * $this->config->get($constants->config_limit_admin))
            + $this->config->get($constants->config_limit_admin));
        }
        $data['results'] = sprintf(
            $this->language->get('text_pagination'),
            $page_total_1,
            $page_total_2,
            $total,
            ceil($total / $this->config->get($constants->config_limit_admin))
        );
        //end
        $data[$constants->user_token] = $this->session->data[$constants->user_token];
        //load model localisation/language
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['archived_order'] = $this->url->link(
            'extension/upsmodule/archivedorders&user_token=' .
            $this->session->data[$constants->user_token] . $constants->sort_id .
            $constants->link_sort_page
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        //add ups footer for us
        $country_data = (object) $this->model_extension_upsmodule_country->getCountryCode();
        $data['ups_footer'] = "";
        if (!empty($country_data) && strtolower($country_data->value) == "us") {
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        }
        $this->response->setOutput($this->load->view($constants->link_archivedorders, $data));
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders detailAccess
     *
     * @return $accessorial_array
     */
    protected function detailAccess()
    {
        //load model base
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load model link_archivedorders
        $this->load->model($constants->link_archivedorders);
        //load model link_opencartsetting
        $this->load->model($constants->link_opencartsetting);
        //load mode link_translate
        $this->load->language($constants->link_translate);
        //load request->post
        $request = $this->request->post;
        //load list_id_orders
        $order_id = $request[$constants->list_id_orders];
        //load model getDetailOrderData
        $detail_order = $this->model_extension_upsmodule_archivedorders->getDetailOrderData($order_id);
        //ap_address_1
        if (!empty($detail_order[$constants->ap_address_1])) {
            $state_data = $this->model_extension_upsmodule_opencartsetting
                ->getStateByZoneId($detail_order['shipping_zone_id']);
                //state_data
            if (!empty($state_data)) {
                //state_code
                $detail_order[$constants->state_code] = $state_data['code'];
            }
            $detail_order[$constants->state_name] = $detail_order[$constants->shipping_zone];
        } else {
            //load model getStateByStateCode
            $state_data = $this->model_extension_upsmodule_opencartsetting
                ->getStateByStateCode($detail_order[$constants->ap_state], $detail_order[$constants->ap_country]);
            $detail_order[$constants->state_code] = $detail_order[$constants->ap_state];
            //state_data
            if (!empty($state_data)) {
                $detail_order[$constants->state_name] = $state_data['name'];
            }
        }
        //accessorial_array
        $accessorial_array = [];
        if (!empty($detail_order[$constants->accessorial_service])) {
            $accessorial_array = json_decode($detail_order[$constants->accessorial_service], true);
            //check accessorial_array
            foreach ($accessorial_array as $key => $value) {
                //accessorial_array
                $accessorial_translate = $this->language->get($key);
                $accessorial_array[$key] = $accessorial_translate;
            }
        }
        return $accessorial_array;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders detailAddressAP
     *
     * @return $addressAP
     */
    protected function detailAddressAp()
    {
        //load model base
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load model link_archivedorders
        $this->load->model($constants->link_archivedorders);
        //load model link_opencartsetting
        $this->load->model($constants->link_opencartsetting);
        //load mode link_translate
        $this->load->language($constants->link_translate);
        //load request->post
        $request = $this->request->post;
        //load list_id_orders
        $order_id = $request[$constants->list_id_orders];
        //load model getDetailOrderData
        $detail_order = $this->model_extension_upsmodule_archivedorders->getDetailOrderData($order_id);
        //address_format_ap
        $address_format_ap = [];
        //check ap_city
        if (!empty($detail_order['ap_city'])) {
            $address_format_ap[] = $detail_order['ap_city'];
        }
        //check ap_state
        if (!empty($detail_order[$constants->ap_state])) {
            $address_format_ap[] = $detail_order['shipping_zone'];
        }
        //check ap_postcode
        if (!empty($detail_order[$constants->ap_postcode])) {
            $address_format_ap[] = $detail_order[$constants->ap_postcode];
        }
        //check service_type
        if ($detail_order['service_type'] == 'AP') {
            //check ap_country
            if (!empty($detail_order[$constants->ap_country])) {
                $country_data = $this->model_extension_upsmodule_opencartsetting
                    ->getCountryByCountryCode($detail_order[$constants->ap_country]);
                //check country_data
                if (!empty($country_data)) {
                    $address_format_ap[] = $country_data['name'];
                }
            } else {
                $country_data = '';
            }
        }
        //addressAP
        $addressAP = implode($constants->br_dropdown, $address_format_ap);
        return $addressAP;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders detailAddress
     *
     * @return $address_array
     */
    protected function detailAddress()
    {
        //load model base
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load model link_archivedorders
        $this->load->model($constants->link_archivedorders);
        //load model link_opencartsetting
        $this->load->model($constants->link_opencartsetting);
        //load mode link_translate
        $this->load->language($constants->link_translate);
        //load request->post
        $request = $this->request->post;
        //load list_id_orders
        $order_id = $request[$constants->list_id_orders];
        //load model getDetailOrderData
        $detail_order = $this->model_extension_upsmodule_archivedorders->getDetailOrderData($order_id);
        //address_format_ap
        $address_format_ap = [];
        if (!empty($detail_order[$constants->ap_name])) {
            $address_format_ap[] = $detail_order[$constants->ap_name];
        }
        $ap_address_all = $this->addArray(
            [$detail_order[$constants->ap_address_1],
            $detail_order[$constants->ap_address_2], $detail_order[$constants->ap_address_3]]
        );
        $detail_order['ap_address_all'] = implode(", ", $ap_address_all);
        //check ap_address_1
        if ($detail_order[$constants->ap_address_1] != null) {
            $detail_order[$constants->ap_address_1] = str_replace(
                $constants->special_char,
                '',
                ($detail_order[$constants->ap_address_1])
            );
            //check empty ap_address_1
            if (!empty($detail_order[$constants->ap_address_1])) {
                $address_format_ap[] = $detail_order[$constants->ap_address_1];
            }
        } else {
            $detail_order[$constants->ap_address_1] = '';
        }
        //check ap_address_2
        if ($detail_order[$constants->ap_address_2] != null) {
            $detail_order[$constants->ap_address_2] = str_replace(
                $constants->special_char,
                '',
                ($detail_order[$constants->ap_address_2])
            );
            //check empty ap_address_2
            if (!empty($detail_order[$constants->ap_address_2])) {
                $address_format_ap[] = $detail_order[$constants->ap_address_2];
            }
        } else {
            $detail_order[$constants->ap_address_2] = '';
        }
        //check ap_address_3
        if ($detail_order[$constants->ap_address_3] != null) {
            $detail_order[$constants->ap_address_3] = str_replace(
                $constants->special_char,
                '',
                ($detail_order[$constants->ap_address_3])
            );
            //check empty ap_address_3
            if (!empty($detail_order[$constants->ap_address_3]) != "") {
                $address_format_ap[] = $detail_order[$constants->ap_address_3];
            }
        } else {
            $detail_order[$constants->ap_address_3] = '';
        }
        //load detailAddressAP
        $addressAP = $this->detailAddressAp();
        //load address_array
        $address_array = implode($constants->br_dropdown, $address_format_ap) . $constants->br_dropdown . $addressAP;
        return $address_array;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders detailOrder
     *
     * @return null
     */
    public function detailOrder()
    {
        //load model base
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load model link_archivedorders
        $this->load->model($constants->link_archivedorders);
        //load model link_opencartsetting
        $this->load->model($constants->link_opencartsetting);
        //load mode link_translate
        $this->load->language($constants->link_translate);
        //load request->post
        $request = $this->request->post;
        //load list_id_orders
        $order_id = $request[$constants->list_id_orders];
        //load model getDetailOrderData
        $detail_order = $this->model_extension_upsmodule_archivedorders->getDetailOrderData($order_id);
        $detail_order['total_all_decimal']
            = $this->currency->format($detail_order['total'], $detail_order['currency_code'], '', true);
        //handling currency symbol
        $currency_code = $detail_order['currency_code'];
        //get currency symbol with currency code
        $product_name = explode(',', $detail_order[$constants->list_product]);
        $detail_order['product_name'] = implode($constants->br_dropdown, $product_name);
        // handling service name
        if ($detail_order[$constants->service_symbol] == '&trade;') {
            $detail_order[$constants->service_name_info] = 'UPS Access Point&trade; Economy';
        } else {
            $detail_order[$constants->service_name_info] = $detail_order[$constants->service_name] .
                $detail_order[$constants->service_symbol];
        }
        //load format date_added
        $detail_order[$constants->date_added] = date('M d, Y, H:i:s', strtotime($detail_order[$constants->date_added]));
        //load format total
        $detail_order[$constants->total] = number_format($detail_order[$constants->total], 2);
        //load total
        $detail_order['order_value'] = $detail_order[$constants->total];
        //load address_format_1
        $address_format_1 = [
            $detail_order[$constants->shipping_address_1],
            $detail_order[$constants->shipping_address_2],
            $detail_order['shipping_city'],
            $detail_order[$constants->shipping_zone],
            $detail_order[$constants->shipping_postcode],
            $detail_order['shipping_country']
        ];
        //load address_format
        $address_format = $this->addArray($address_format_1);
        $detail_order['address'] = implode($constants->br_dropdown, $address_format);
        //load detailAddress
        $address_array =  $this->detailAddress();
        $detail_order['address_ap'] = $address_array;
        //load add_address_all
        $add_address_all = $this->addArray(
            [$detail_order[$constants->shipping_address_1], $detail_order[$constants->shipping_address_2]]
        );

        $detail_order['add_address_all'] = implode(", ", $add_address_all);
        //load accessorial_array
        $accessorial_array = $this->detailAccess();
        $detail_order['accessorial_array'] = $accessorial_array;
        echo json_encode($detail_order);
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders addArray
     *
     * @param string $array //The array
     *
     * @return $array_returnl
     */
    function addArray($array)
    {
        //array_return
        $array_return = [];
        //check array
        foreach ($array as $key => $value) {
            //check value
            if (!empty($value)) {
                $array_return[] = $value;
            }
        }
        return $array_return;
    }

    /**
     * ControllerExtensionUpsmoduleArchivedorders unArchivedOrders
     *
     * @param string $array //The array
     *
     * @return $array_returnl
     */
    function unArchivedOrders()
    {
        $this->load->model($this->_base_model);
        $this->load->model("extension/upsmodule/archivedorders");
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        $data = $this->request->post;
        $list_archived_orders = $data['unarchived_order_data'];
        $list_unarchived = "";
        if (is_array($list_archived_orders) && !empty($list_archived_orders)) {
            $list_unarchived = implode(",", $list_archived_orders);
        } else {
            $list_unarchived = $list_archived_orders;
        }
        $this->model_extension_upsmodule_archivedorders->setUnArchivedOrders($list_unarchived);
        echo json_encode("");
    }
}
