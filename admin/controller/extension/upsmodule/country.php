<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleCountry file
 *
 * @category Country_Controller
 */

class ControllerExtensionUpsmoduleCountry extends Controller
{
    private $_error = [];
    private $_base_model = 'extension/upsmodule/base';

    /**
     * ControllerExtensionUpsmoduleCountry index
     *
     * @return null
     */
    public function index()
    {
        //Load Model
        $this->load->model($this->_base_model);

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        //load language
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->text_country));
        //load model link
        $this->load->model($constants->link_country);
        //get checkValueCountry()
        $checkValue = $this->model_extension_upsmodule_country->checkValueCountry();
        //get $value
        foreach ($checkValue as $value) {
            //check $value['value']
            if ($value['value'] == 1) {
                $this->response->redirect($this->url->link($constants->link_account, $token, true));
            } else {
                $this->url->link($constants->link_country, $token, true);
            }
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleCountry getForm
     *
     * @return null
     */
    protected function getForm()
    {
        //Load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        //link
        $data['countryCode'] = $this->url->link('extension/upsmodule/country/add', $token, true);
        $data['action'] = $this->url->link('extension/upsmodule/country/add', $token, true);
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data['list_country_code'] = $constants->list_country_code;
        //text
        $lang_country = '';
        if (!isset($this->request->get['id'])) {
            $lang_country = $this->language->get('text_country_setting');
        } else {
            $lang_country = $this->language->get('country_setting_default');
        }
        $data['text_home'] = $this->language->get('text_home');
        $data['text_form'] = $lang_country;
        $data['text_enabled'] = $this->language->get('text_enabled');
        $data['text_disabled'] = $this->language->get('text_disabled');
        $data['text_none'] = $this->language->get('text_none');
        //help
        $data['help_category'] = $this->language->get('help_category');
        $data['help_intro_text'] = $this->language->get('help_intro_text');
        //Column
        $data['column_title'] = $this->language->get('column_title');
        $data['column_description'] = $this->language->get('column_description');
        $data['column_status'] = $this->language->get('Country');
        $data['column_action'] = $this->language->get('column_action');
        //Button
        $data['button_save'] = $this->language->get('button_save');
        $data['button_cancel'] = $this->language->get('button_cancel');
        $data['button_remove'] = $this->language->get('button_remove');
        $data['tab_general'] = $this->language->get('tab_general');
        $data['button_continue'] = $this->language->get('button_continue');
        $data['text_select_country'] = $this->language->get('text_select_country');
        if (isset($this->_error['warning'])) {
            $data['error_warning'] = $this->_error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        //region country
        $totals = $this->model_extension_upsmodule_country->getListCountry();
        //end
        //region isCountryCode
        $isCountry = $this->model_extension_upsmodule_country->getCountryCode();
        //end
        if (isset($this->_error['name'])) {
            $data['error_name'] = $this->_error['name'];
        } else {
            $data['error_name'] = [];
        }
        if (isset($this->_error['meta_title'])) {
            $data['error_meta_title'] = $this->_error['meta_title'];
        } else {
            $data['error_meta_title'] = [];
        }
        //region URL
        $request = $this->request->get;
        $url = '';
        foreach ($request as $key => $val) {
            $url .= '&' .  $key . '=' . urlencode(html_entity_decode($val, ENT_QUOTES, 'UTF-8'));
        }
        //end
        $data['user_token'] = $this->session->data[$constants->user_token];
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        $data['totals'] = $totals;
        $data['isCountry'] = $isCountry['value'];
        //end
        $data['country'] = $this->url->link(
            'extension/upsmodule/country&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $this->response->setOutput($this->load->view($constants->link_country, $data));
    }

    /**
     * ControllerExtensionUpsmoduleCountry add
     *
     * @return null
     */
    function add()
    {
        //Load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        $url = '';
        $this->load->model($constants->link_country);
        $request = $this->request->post;
        $this->model_extension_upsmodule_country->updateCountry($request['Country']);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "country",
            "extension/upsmodule/country"
        );

        //redirect
        $this->response->redirect($this->url->link($constants->link_termcondition, $token . $url, true));
    }
}
