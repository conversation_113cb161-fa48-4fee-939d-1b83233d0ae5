<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleAccount file
 *
 * @category Account_Controller
 */

class ControllerExtensionUpsmoduleAccount extends Controller
{
    private $_validate_data = [];
    private $_base_model = 'extension/upsmodule/base';
    private $_api_model = 'extension/upsmodule/apiModel';
    private $_user_model = 'user/user_group';
    private $_acclicnumber = '';
    /**
     * ControllerExtensionUpsmoduleAccount index
     *
     * @return null
     */
    public function index()
    {
        //Load model
        $this->load->model($this->_base_model);
        $this->load->model($this->_user_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "account",
            "extension/upsmodule/account"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        $user_group_id = $this->user->getGroupId();
        //load link language
        $this->load->language($constants->link_translate);
        //load link account
        $this->load->model($constants->link_account);
        //load link opencartsetting
        $this->load->model($constants->link_opencartsetting);
        //load set title
        $this->document->setTitle($this->language->get($constants->text_account));
        //load account_default_check
        $account_default_check = $this->model_extension_upsmodule_account->getAccountDefault();
        //check account_default_check
        if ($account_default_check) {
            //get link account success
            $this->response->redirect($this->url->link($constants->link_accountsuccess, $token, true));
        }
        //load getSetting()
        $setting = $this->model_extension_upsmodule_base->getSetting();
        //region bo phan quyen menu
        //check ups_shipping_menu_account
        if (!empty($setting->ups_shipping_menu_account)) {
            $this->model_user_user_group->removePermission($user_group_id, 'access', $constants->link_termcondition);
            $this->model_user_user_group->removePermission($user_group_id, 'modify', $constants->link_termcondition);
            $this->model_user_user_group->removePermission($user_group_id, 'access', $constants->link_country);
            $this->model_user_user_group->removePermission($user_group_id, 'modify', $constants->link_country);
        }
        //delete cache
        $this->cache->delete('menu_left');
        //end
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleAccount getForm
     *
     * @return null
     */
    protected function getForm()
    {
        //Load model
        $this->load->model($this->_base_model);
        $this->load->model($this->_user_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //constantsCurrency
        $constantsCurrency = $this->model_extension_upsmodule_base->listCurrency();
        //constantsTitleAccount
        $constantsTitleAccount = $this->model_extension_upsmodule_base->listTitleAccount();
        //constanst token
        $token = $constants->token;
        $userid = $this->user->getId();
        //load getListCheckTermcondition()
        $listCheckTermcondition = $this->model_extension_upsmodule_account->getListCheckTermcondition();
        //check listCheckTermcondition
        foreach ($listCheckTermcondition as $row) {
            //check value
            if ($row[$constants->value] == 1) {
                $this->url->link($constants->link_account, $token, true);
            } else {
                $this->response->redirect($this->url->link($constants->link_termcondition, $token, true));
            }
        }
        //list_currencies
        $list_currencies = $constantsCurrency->list_currency;
        $list_title = $constantsTitleAccount->list_title_account;
        $data['list_currencies'] = $list_currencies;
        //check and get list_title
        $arrTitle = [];
        foreach ($list_title as $key => $val) {
            $val['translate_pl'] = $this->language->get($val['title_key']);
            $arrTitle[$key] = $val;
        }
        $data['list_title'] = $arrTitle;
        //translate
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data['text_account'] = $this->language->get($constants->text_account);
        $data['text_error'] = $this->language->get($constants->text_error);
        //action url
        $data['action'] = $this->url->link('extension/upsmodule/account/saveAccount&' . $token, true);
        $data['account_success_url'] = $this->url->link('extension/upsmodule/accountsuccess&' . $token, true);
        //get country code
        $get_country_code = $this->model_extension_upsmodule_opencartsetting->getCountryCode();
        $totals = $this->model_extension_upsmodule_country->getListCountry();
        if (!empty($totals) && is_array($totals)) {
            foreach ($totals as $key => $value) {
                if ($key == $get_country_code[$constants->value]) {
                    $data[$constants->country_code] = $key;
                    $data[$constants->country_name] = $value;
                }
            }
        }
        $data['ups_footer'] = "";
        if (!empty($get_country_code) && strtolower($get_country_code[$constants->value]) == "us") {
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        }
        //Infor account
        $data['text_home'] = $this->language->get('text_home');

        $ups_link = $this->model_extension_upsmodule_base->createUpsReferenceLink("account");
        $link = '<a target="_blank" href="' . $ups_link . '">';
        $data['account_link'] = sprintf($this->language->get('account_link'), $link, '</a>');

        $data['account_title'] = $this->language->get('account_title');
        $data['Mr'] = $this->language->get('account_Mr');
        $data['Miss'] = $this->language->get('account_Miss');
        $data['Mrs'] = $this->language->get('account_Mrs');
        $data['Ms'] = $this->language->get('account_Ms');
        $data['account_address_type'] = $this->language->get('account_address_type');
        $data['account_placeholder_address_type'] = $this->language->get('account_placeholder_address_type');
        $data['account_tooltip_address_type'] = $this->language->get('account_tooltip_address_type');
        $data['account_full'] = $this->language->get('account_full');
        $data['account_address'] = $this->language->get('account_address');
        $data['account_placeholder_address1'] = $this->language->get('account_placeholder_address1');
        $data['account_placeholder_address2'] = $this->language->get('account_placeholder_address2');
        $data['account_placeholder_address3'] = $this->language->get('account_placeholder_address3');
        $data['account_company'] = $this->language->get('account_company');
        $data['account_email'] = $this->language->get('account_email');
        $data['account_phone_number'] = $this->language->get('account_phone_number');
        $data['account_postal_code'] = $this->language->get('account_postal_code');
        $data['account_tooltip_postal_code'] = $this->language->get('account_tooltip_postal_code');
        $data['account_city'] = $this->language->get('account_city');
        $data['account_state'] = $this->language->get('account_state');
        $data['account_country'] = $this->language->get('account_country');
        $data['account_account_name'] = $this->language->get('account_account_name');
        $data['account_tooltip_account_name'] = $this->language->get('account_tooltip_account_name');
        $data['account_account_number'] = $this->language->get('account_account_number');
        $data['account_tooltip_account_number'] = $this->language->get('account_tooltip_account_number');
        $data['account_invoice_number'] = $this->language->get('account_invoice_number');
        $data['account_account_user'] = $this->language->get('account_account_user');
        $data['account_account_pass'] = $this->language->get('account_account_pass');
        $data['account_with_user_pass'] = $this->language->get('account_with_user_pass');
        $data['account_tooltip_account_user'] = $this->language->get('account_tooltip_account_user');
        $data['account_tooltip_account_pass'] = $this->language->get('account_tooltip_account_pass');
        $data['account_tooltip_invoice_number'] = $this->language->get('account_tooltip_invoice_number');
        $data['account_invoice_amount'] = $this->language->get('account_invoice_amount');
        $data['account_tooltip_invoice_amount'] = $this->language->get('account_tooltip_invoice_amount');
        $data['account_currency'] = $this->language->get('account_currency');
        $data['account_invoice_date'] = $this->language->get('account_invoice_date');
        $data['account_tooltip_invoice_date'] = $this->language->get('account_tooltip_invoice_date');
        $data['account_title_form'] = $this->language->get('account_title_form');
        $data['account_title_form_note'] = $this->language->get('account_title_form_note');
        $data['account_number_with_90'] = $this->language->get('account_number_with_90');
        $data['account_number_without_90'] = $this->language->get('account_number_without_90');
        $data['account_title_with_90'] = $this->language->get('account_title_with_90');
        $data['account_not_number'] = $this->language->get('account_not_number');
        $data['account_invoice_control_id'] = $this->language->get('account_invoice_control_id');
        if (strtolower($data[$constants->country_code]) == "us") {
            $data['account_not_number'] = $this->language->get('account_not_number_US');
            $data['ups_term_of_service'] = $this->language->get('ups_term_of_service');
            $data['account_title_with_90'] = $this->language->get('account_title_with_90_US');
        }
        $data['account_vat_detail'] = $this->language->get('account_vat_detail');
        $data['account_vat_number'] = $this->language->get('account_vat_number');
        $data['account_promo_code'] = $this->language->get('account_promo_code');
        $data['account_please_note_shipper'] = $this->language->get('account_please_note_shipper');
        $data['account_link_more_information'] = $this->language->get('account_link_more_information');

        $data['list_state'] = $this->model_extension_upsmodule_country->getListStateByCountryCode(
            $data[$constants->country_code]
        );

        $dangerous_good_link = $this->model_extension_upsmodule_base->createUpsReferenceLink("dangerous_good");
        $link = '<a target="_blank" href="' . $dangerous_good_link . '">' . $dangerous_good_link . '</a>';
        $data['account_link_ups'] = $link;

        $data['your_account_support'] = $this->language->get('your_account_support');
        $data['modal_loading'] = $this->load->view('extension/upsmodule/modalloading');
        //Button
        $data['button_get_started'] = $this->language->get('button_get_started');
        $data['user_token'] = $this->session->data[$constants->user_token];
        //load model language
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        //load link
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['account'] = $this->url->link(
            'extension/upsmodule/account&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $this->response->setOutput($this->load->view($constants->link_account, $data));
    }

    /**
     * ControllerExtensionUpsmoduleAccount saveAccount
     *
     * @return null
     */
    public function saveAccount()
    {
        //Load model
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //load user model
        $this->load->model($this->_user_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //get userid
        $userid = $this->user->getId();
        //load link
        $this->load->language($constants->link_translate);
        $this->load->model($constants->link_account);
        $this->load->model($constants->link_api_model);
        //get license
        $license = $this->model_extension_upsmodule_apiModel->getLicenseDefault();
        $error = true;
        $error_message = '';
        //load data post
        $session = $this->session->data;
        $data = $this->request->post;
        //check decode phone
        $check_phone_number = $data[$constants->phone_number_option3];
        $phone_number = preg_replace($constants->special_phone_acc, '', $check_phone_number);
        //get message error
        $error_message = $this->language->get($constants->text_error);
        //checkValidate
        if ($this->checkValidate()) {
            //checkValidateOption
            if (!$this->checkValidateOption()) {
                echo json_encode([$constants->error => $error, $constants->message => $error_message]);
            } else {
                //get data_account
                $error = false;
                $state_province_code = "";
                if (!empty($data[$constants->province_code_acc])) {
                    $state_province_code = $data[$constants->province_code_acc];
                }
                $data_account = [
                    $constants->title_account => $data[$constants->title_acc],
                    $constants->customer_name => $data[$constants->customer_name_option3],
                    $constants->company => $data[$constants->company_name_option3],
                    $constants->email => $data[$constants->address_email_option3],
                    $constants->phone => $phone_number,
                    'address_type' => $data['AddressType'],
                    $constants->address_1 => $data[$constants->address_line_1],
                    $constants->address_2 => $data['AddressLine2'],
                    $constants->address_3 => $data['AddressLine3'],
                    $constants->post_code => trim($data[$constants->postal_code]),
                    'city' => $data[$constants->account_city_option3],
                    $constants->state_province_code => $state_province_code,
                    $constants->country_code => $data[$constants->country_code_option3],
                    $constants->account_type => $data[$constants->optradio],
                    'device_identity' => $data['ioBlackBox'],
                    'account_default' => 1
                    // 'AccessLicenseNumber' => '6D54DCFD5EBA7608'
                ];
                $option = $this->model_extension_upsmodule_base->getSetting();
                if ($data[$constants->optradio] == 4) {
                    $data_account['myUpsID'] = $data['AccountUser'];
                    $data_account['myUpsPassword'] = $data['AccountPass'];
                    $data_account['myUpsAccess'] = $data['AccountAccess'];
                } elseif (isset($option->ups_shipping_pre_registered_plugin_token)) {
                    $data_account['myUpsID'] = $this->callUpsApi($constants->MyUpsID, '', '');
                }
                //get optradio = 1
                if ($data[$constants->optradio] == 1) {
                    $date = date('Y-m-d', strtotime(str_replace('-', '/', $data['InvoiceDate'])));
                    $checkAccountName = htmlentities($data[$constants->account_name]);
                    $data[$constants->account_name] = $checkAccountName;
                    $data_option = [
                        $constants->ups_account_name => $data[$constants->account_name],
                        $constants->ups_account_number => $data[$constants->account_number_acc],
                        'ups_invoice_number' => $data['InvoiceNumber'],
                        'ups_invoice_amount' => $data['InvoiceAmount'],
                        'ups_currency' => $data['Currency'],
                        'ups_invoice_date' => $date,
                    ];
                    if (isset($data['ControlID'])) {
                        $data_option['control_id'] = $data['ControlID'];
                    }
                    $data_account = array_merge($data_account, $data_option);
                    //saveAccountDefault
                    $data['display_msg'] = $this->saveAccountDefault($data_account);
                    
                } elseif ($data[$constants->optradio] == 2) {    //option 2
                    $checkAccountName1 = htmlentities($data[$constants->account_name1]);
                    $data[$constants->account_name1] = $checkAccountName1;
                    $data_option = [
                        $constants->ups_account_name => $data[$constants->account_name1],
                        $constants->ups_account_number => $data['AccountNumber1'],
                    ];
                    $data_account = array_merge($data_account, $data_option);
                    //saveAccountDefault
                    $data['display_msg'] = $this->saveAccountDefault($data_account);
                } elseif ($data[$constants->optradio] == 4) {    //option 4
                    $checkAccountName4 = htmlentities($data['AccountName4']);
                    $data['AccountName4'] = $checkAccountName4;
                    $data_option = [
                        $constants->ups_account_name => $data['AccountName4'],
                        $constants->ups_account_number => $data['AccountNumber4'],
                    ];
                    $data_account = array_merge($data_account, $data_option);
                    //saveAccountDefault
                    $data['display_msg'] = $this->saveAccountDefault($data_account);
                } else {    //option 3
                    $account_promocode = "";
                    if (strtolower($data_account[$constants->country_code]) == 'us') {
                        $data['AccountPromoCode'] = $constants->const_promo_code;
                    }
                    if (isset($data['AccountPromoCode'])) {
                        $account_promocode = $data['AccountPromoCode'];
                    }
                    $data_option = [
                        'ups_account_vatnumber' => $data[$constants->account_vat_number],
                        $constants->ups_account_promocode => $account_promocode
                    ];
                    $data_account = array_merge($data_account, $data_option);
                    $data_promo_discount_agreement = $this->apiPromoDiscountAgreeMent();
                    $data_account = array_merge($data_account, $data_promo_discount_agreement);
                    //saveAccountDefault
                    $this->saveAccountOption3($data_account);
                }
            }
        } else {
            echo json_encode([$constants->error => $error, $constants->message => $error_message]);
        }
    }

    /**
     * ControllerExtensionUpsmoduleAccount saveAccountOption3
     *
     * @param array $data_account //The data_account
     * @return null
     */
    public function saveAccountOption3($data_account)
    {
        //Load model
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load model link
        $this->load->model($constants->link_account);
        $this->load->model($constants->link_api_model);
        $this->load->language($constants->link_translate);
        //get api registration
        $response02 = $this->callUpsApi($constants->Registration, $data_account);
        //get message error
        $getMessage = $response02['message'];
        //check response02
        if ($response02['check'] == 1) {
            //get data apiOpenAccount()
            $getOpenAccount = $this->apiOpenAccount($response02[$constants->UserName]);
            $data_account = array_merge($data_account, $getOpenAccount);
            //get api response
            $response = $this->callUpsApi($constants->OpenAccount, $data_account);
            if (isset($response->OpenAccountResponse->ShipperNumber)) {
                $data_account[$constants->ups_account_name] = "";
                $data_account[$constants->ups_account_number] = $response->OpenAccountResponse->ShipperNumber;
                $data_account[$constants->account_number_acc] = $response->OpenAccountResponse->ShipperNumber;
                $data_account['AccountNotNeededIndicator'] = $response->OpenAccountResponse->ShipperNumber;
            }
            //checkError
            if (isset($response->Fault->detail->Errors->ErrorDetail)) {
                $checkError = $response->Fault->detail->Errors->ErrorDetail;
            }
            $error = false;
            //check isset and empty checkError Severity
            if (!empty($checkError->Severity)) {
                $error = true;
                $error_message = $checkError->PrimaryErrorCode->Description;
                //check isset($error_message
                if (isset($error_message)) {
                    echo json_encode([$constants->error => $error, $constants->message => $error_message]);
                }
            } else {
                $error = false;
                if (isset($response->OpenAccountResponse->PickupAddressCandidate)) {
                    $error = true;
                    $error_message = $this->language->get('message_postalcode_failed01');
                    $error_code = $response->OpenAccountResponse->Response->Alert->Code;
                    if (is_array($response->OpenAccountResponse->PickupAddressCandidate)) {
                        $getPostalCountry = $response->OpenAccountResponse->PickupAddressCandidate;
                        foreach ($getPostalCountry as $item) {
                            $error_message .= '"' . $item->City . ', ' . $item->PostalCode . '";';
                        }
                        $error_message = rtrim($error_message, ";");
                        $error_message .= '. ';
                    } else {
                        $item = $response->OpenAccountResponse->PickupAddressCandidate;
                        $error_message .= '"' . $item->City . ', ' . $item->PostalCode . '";';
                        $error_message = rtrim($error_message, ";");
                        $error_message .= '. ';
                    }
                    $error_message .= $this->language->get('message_postalcode_failed02');
                    $data['message_postalcode_failed'] = $error_message;
                    echo json_encode([$constants->error => $error, $constants->message => $error_message, 'errorCode' => $error_code]);
                } else {
                    $error = false;
                    //get api license
                    $check_license = $this->updateAccessLicenseNumber($data_account);
                    if ($check_license->check) {
                        $error = false;
                        //save and update
                        $data_option = [
                            $constants->ups_account_number => $data_account[$constants->account_number_acc],
                        ];
                        $data_account = array_merge($data_account, $data_option);
                        $this->model_extension_upsmodule_account->saveAccount($data_account);
                        $this->model_extension_upsmodule_account->updateCheckAccount();
                        $this->model_extension_upsmodule_apiModel->updateLicense(
                            $response02[$constants->UserName],
                            $response02['password']
                        );
                        $this->getTokenAndBingMapKey();
                        if (!empty($data_account[$constants->ups_account_promocode])) {
                            //get api Promo Discount AgreeMent
                            $data_account[$constants->languageCode_acc] = $this->language->get("code");
                            $license = $this->model_extension_upsmodule_apiModel->getLicensePromoCode(); // En PRO
                            $apiPromoDiscount
                                = $this->callUpsApi($constants->PromoDiscount, $data_account, $license, 1);
                            $apiPromoDiscount = json_decode($apiPromoDiscount);
                            if (isset($apiPromoDiscount->PromoDiscountAgreementResponse->Response->ResponseStatus->Code)
                                && $apiPromoDiscount->PromoDiscountAgreementResponse->Response->ResponseStatus->Code == '1'
                            ) {
                                $checkAcceptanceCode
                                    = $apiPromoDiscount->PromoDiscountAgreementResponse->PromoAgreement->AcceptanceCode;
                                $data_account['checkAcceptanceCode'] = $checkAcceptanceCode;
                                $license = $this->model_extension_upsmodule_apiModel->getLicensePromoCode(); // En PRO
                                $apiPromo = $this->callUpsApi($constants->PromoDiscount, $data_account, $license, 2);
                                $apiPromo = json_decode($apiPromo);
                                if (!empty($apiPromo) && isset($apiPromo->PromoDiscountResponse->Response)) {
                                    $message = "";
                                    $promo_response = $apiPromo->PromoDiscountResponse->Response;
                                    if (isset($promo_response->ResponseStatus->Description)) {
                                        $message = $promo_response->ResponseStatus->Description;
                                    }
                                    $this->session->data[$constants->success] = 1;
                                    $this->session->data[$constants->message] = "Promo Discount " . $message;
                                } else {
                                    $message = "";
                                    if (isset($apiPromo->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode)) {
                                        $error_code = $apiPromo->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode;
                                        if (isset($error_code->Description)) {
                                            $message = $error_code->Description;
                                        }
                                    }
                                    $this->session->data[$constants->error] = 1;
                                    $this->session->data[$constants->message] = $message;
                                }
                            } else {
                                $errorPromoDiscount
                                    = $apiPromoDiscount->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode->Description;
                                $error_message = $errorPromoDiscount;
                                $this->session->data[$constants->error] = 1;
                                $this->session->data[$constants->message] = $error_message;
                            }
                        }
                        echo json_encode([$constants->error => $error, $constants->message => '']);
                    } else {
                        $error = true;
                        //error_message
                        $error_message = $check_license->message;
                        echo json_encode([$constants->error => $error, $constants->message => $error_message]);
                    }
                }
            }
        } else {
            $error = true;
            echo json_encode([$constants->error => $error, $constants->message => $getMessage]);
        }
    }

    /**
     * ControllerExtensionUpsmoduleAccount saveAccountDefault
     *
     * @param string $data_account //The data_account
     * @return null
     */
    public function saveAccountDefault($data_account)
    {
        //Load model
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //get link
        include_once "$constants->link_api_ups";
        $this->load->model($constants->link_account);
        $this->load->model($constants->link_api_model);
        //get api registration
        if (isset($data_account['account_type']) && $data_account['account_type'] == 4) {
            $response = [];
            $response['check'] = true;
            $response[$constants->UserName] = isset($data_account['myUpsID']) ? $data_account['myUpsID'] : '';
            $response['password'] = isset($data_account['myUpsPassword']) ? $data_account['myUpsPassword'] : '';
            
        } else {
            $response = $this->callUpsApi($constants->Registration, $data_account);
        }
        
        $error = true;
        
        $support = $this->get_country_support();
       
        if ($response['check']) {
            $error = false;
            //get api license
            if(isset($data_account['account_type']) && $data_account['account_type'] != 4){
                $check_license = $this->updateAccessLicenseNumber($data_account);
            }
            if(isset($data_account['account_type']) && $data_account['account_type'] == 4){
                
                $accessLicenseNumber = !empty($data_account['myUpsAccess']) ? $data_account['myUpsAccess'] : $this->_acclicnumber;
                $check_license = new stdClass();
                $check_license->check = true;
                if(!empty($accessLicenseNumber)){
                    $this->model_extension_upsmodule_apiModel->updateLicenseAccess2($accessLicenseNumber);
                    
                }
                
                
            }
            
            
            if ($check_license->check) {
                //save
                $this->model_extension_upsmodule_account->saveAccount($data_account);
                $this->model_extension_upsmodule_account->updateCheckAccount();
                $this->model_extension_upsmodule_apiModel->updateLicense(
                    $response[$constants->UserName],
                    $response['password']
                );
                $this->getTokenAndBingMapKey();
                echo json_encode([$constants->error => $error, $constants->message => '','support'=>$support]);
            } else {
                $error = true;
                //error_message
                $error_message = $check_license->message;
                echo json_encode([$constants->error => $error, $constants->message => $error_message,'support'=>$support]);
            }
        } else {
            $error = true;
            //error_message
            $error_message = $response[$constants->message];
            echo json_encode([$constants->error => $error, $constants->message => $error_message,'support'=>$support]);
        }
        // $data['display_msg'] = $error_message;
    }

    public function get_country_support(){
        $support = array();
        $support['GB'] = array('name' => 'United Kingdom','number'=>'+44 808 258 0323');
        $support['BE'] = array('name' => 'Belgium','number'=>'+32 78 48 49 16');
        $support['FR'] = array('name' => 'France','number'=>'+33 805 11 96 92');
        $support['DE'] = array('name' => 'Germany','number'=>'+49 32 *********');
        $support['IS'] = array('name' => 'Iceland','number'=>'+49 32 *********');
        $support['IT'] = array('name' => 'Italy','number'=>'+39 800 725 920');
        $support['NL'] = array('name' => 'Netherlands','number'=>'+31 85 107 0232');
        $support['PL'] = array('name' => 'Poland','number'=>'+48 22 103 24 55');
        $support['AT'] = array('name' => 'austria','number'=>'+49 32 *********');
        $support['BG'] = array('name' => 'bulgaria','number'=>'+49 32 *********');
        $support['HR'] = array('name' => 'croatia','number'=>'+49 32 *********');
        $support['CY'] = array('name' => 'cyprus','number'=>'+49 32 *********');
        $support['CZ'] = array('name' => 'czechrepublic','number'=>'+49 32 *********');
        $support['DK'] = array('name' => 'denmark','number'=>'+49 32 *********');
        $support['EE'] = array('name' => 'estonia','number'=>'+49 32 *********');
        $support['FI'] = array('name' => 'finland','number'=>'+49 32 *********');
        $support['GR'] = array('name' => 'greece','number'=>'+49 32 *********');
        $support['HU'] = array('name' => 'hungary','number'=>'+49 32 *********');
        $support['IE'] = array('name' => 'ireland','number'=>'+49 32 *********');
        $support['LV'] = array('name' => 'latvia','number'=>'+49 32 *********');
        $support['LT'] = array('name' => 'lithuania','number'=>'+49 32 *********');
        $support['LU'] = array('name' => 'luxembourg','number'=>'+49 32 *********');
        $support['MT'] = array('name' => 'malta','number'=>'+49 32 *********');
        $support['PT'] = array('name' => 'portugal','number'=>'+49 32 *********');
        $support['RO'] = array('name' => 'romania','number'=>'+49 32 *********');
        $support['SI'] = array('name' => 'slovakia','number'=>'+49 32 *********');
        $support['SK'] = array('name' => 'slovenia','number'=>'+49 32 *********');
        $support['SE'] = array('name' => 'sweden','number'=>'+49 32 *********');
        $support['CH'] = array('name' => 'Switzerland','number'=>'+49 32 *********');
        $support['RS'] = array('name' => 'Serbia','number'=>'+49 32 *********');
        $support['NO'] = array('name' => 'Norway','number'=>'+49 32 *********');
        $support['TR'] = array('name' => 'Turkey','number'=>'+49 32 *********');
        $support['JE'] = array('name' => 'Jersey','number'=>'+49 32 *********');
        return $support;
    }

    /**
     * ControllerExtensionUpsmoduleAccount updateAccessLicenseNumber
     *
     * @param string $account_data //The account_data
     * @return null
     */
    public function updateAccessLicenseNumber($account_data)
    {
        //Load model
        $this->load->model($this->_base_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $option = $this->model_extension_upsmodule_base->getSetting();
        //load model link
        $this->load->model($constants->link_api_model);
        //load getLicense()
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        //get data
        $data = [];
        $data[$constants->account_type] = $account_data[$constants->account_type];
        $data[$constants->company] = $account_data[$constants->company];
        $data[$constants->customer_name] = $account_data[$constants->customer_name];
        $data[$constants->title_account] = $account_data[$constants->title_account];
        $data[$constants->address_1] = $account_data[$constants->address_1];
        $data[$constants->address_2] = $account_data[$constants->address_2];
        $data[$constants->address_3] = $account_data[$constants->address_3];
        $data['city'] = $account_data['city'];
        $data[$constants->post_code] = $account_data[$constants->post_code];
        $data[$constants->country_code] = $account_data[$constants->country_code];
        $data[$constants->state_province_code] = "XX";
        if (!empty($account_data[$constants->state_province_code])) {
            $data[$constants->state_province_code] = $account_data[$constants->state_province_code];
        }
        //check license
        if (!empty($license)) {
            $data['language_code'] = isset($license[$constants->languageCode_acc]) ? $license[$constants->languageCode_acc] : 'EN';
        } else {
            $data['language_code'] = 'EN';
        }
        $data[$constants->phone] = $account_data[$constants->phone];
        $data[$constants->email] = $account_data[$constants->email];
        $data['fax'] = '';
        $data[$constants->ups_account_name] = $account_data[$constants->ups_account_name];
        $data[$constants->ups_account_number] = $account_data[$constants->ups_account_number];
        $data['version'] = VERSION;//Version Flatform
        $data['accessLicenseText'] =  $license['AccessLicenseText'];
        //get api license
        $response = $this->callUpsApi($constants->License, $data, $license);
        if(isset($account_data['get_accesss']) && $account_data['get_accesss'] == true){
            return $response;
        }

        $result = new \stdClass();
        $result->check = false;
        $result->message = '';
        //check AccessLicenseNumber
        $accessLicenseNumber = 'nokey';
        if (isset($response->AccessLicenseResponse->AccessLicenseNumber)) {
            $accessLicenseNumber = $response->AccessLicenseResponse->AccessLicenseNumber;
            $result->check = true;
        } else {    
            $result->check = false;
            if (isset($response->Fault)) {
                $result->message = $response->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode;
            }
        }
        //update
        $this->model_extension_upsmodule_apiModel->updateLicenseAccess2($accessLicenseNumber);
        //get token of registered plugin
        return $result;
    }

    /**
     * ControllerExtensionUpsmoduleAccount checkValidate
     *
     * @return null
     */
    public function checkValidate()
    {
        //load constantsValidate
        $constantsValidate = $this->model_extension_upsmodule_base->listRegexValidate();
        //load constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //45
        $data = $this->request->post;
        //46
        $regex = $constantsValidate->regex_validate;
        //47
        $this->_validate_data = [];
        $this->_validate_data = [
            !empty($data[$constants->customer_name_option3]),
            filter_var(strtolower($data[$constants->address_email_option3]), FILTER_VALIDATE_EMAIL),
            !empty($data[$constants->postal_code_option3]),
            !empty($data[$constants->company_name_option3]),
            !empty($data[$constants->address_line_1]),
            $this->validateAccount($data[$constants->account_city_option3], $regex['validateNull'])
        ];
        //48
        if (!in_array(false, $this->_validate_data)) {
            return true;
        }
        return false;
    }

    /**
     * ControllerExtensionUpsmoduleAccount checkValidateOption
     *
     * @return null
     */
    public function checkValidateOption()
    {
        //Load model
        $this->load->model($this->_base_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $constantsValidate = $this->model_extension_upsmodule_base->listRegexValidate();
        //validateNull
        $validateNull = 'validateNull';
        //get data post
        $data = $this->request->post;
        //get regex_validate
        $regex = $constantsValidate->regex_validate;
        //check data optradio
        if ($data[$constants->optradio] == 2) {
            //check optradio 2
            $this->_validate_data = [];
            $this->_validate_data = [
                $this->validateAccount($data['AccountNumber1'], $regex['accountNumber']),
                !empty($data[$constants->account_name1])
            ];
        } elseif ($data[$constants->optradio] == 1) {
            //check optradio 1
            $this->_validate_data = [];
            $this->_validate_data = [
                $this->validateAccount($data[$constants->account_number_acc], $regex['accountNumber']),
                !empty($data[$constants->account_name]),
                $this->validateAccount($data['InvoiceNumber'], $regex['invoiceNumber']),
                $this->validateAccount($data['InvoiceAmount'], $regex['invoiceAmount']),
                $this->validateAccount($data['InvoiceDate'], $regex['date'])
            ];
            if (isset($data['ControlID'])) {
                $this->_validate_data[] = $this->validateAccount($data['ControlID'], $regex['alphanum']);
            }
        } elseif ($data[$constants->optradio] == 4) {
            //check optradio 4
            $this->_validate_data = [];
            $this->_validate_data = [
                $this->validateAccount($data['AccountNumber4'], $regex['accountNumber']),
                !empty($data['AccountName4'])
            ];
            
            // get access key if access field is empty
            
            if(!isset($data['AccountAccess']) || empty($data['AccountAccess'])){
                $state_province_code = "";
                if (!empty($data[$constants->province_code_acc])) {
                    $state_province_code = $data[$constants->province_code_acc];
                }
                $check_phone_number = $data[$constants->phone_number_option3];
                $phone_number = preg_replace($constants->special_phone_acc, '', $check_phone_number);
                $data_account = [
                    $constants->title_account => $data[$constants->title_acc],
                    $constants->customer_name => $data[$constants->customer_name_option3],
                    $constants->company => $data[$constants->company_name_option3],
                    $constants->email => $data[$constants->address_email_option3],
                    $constants->phone => $phone_number,
                    'address_type' => $data['AddressType'],
                    $constants->address_1 => $data[$constants->address_line_1],
                    $constants->address_2 => $data['AddressLine2'],
                    $constants->address_3 => $data['AddressLine3'],
                    $constants->post_code => trim($data[$constants->postal_code]),
                    'city' => $data[$constants->account_city_option3],
                    $constants->state_province_code => $state_province_code,
                    $constants->country_code => $data[$constants->country_code_option3],
                    $constants->account_type => $data[$constants->optradio],
                    'device_identity' => $data['ioBlackBox'],
                    'account_default' => 1
                    // 'AccessLicenseNumber' => '6D54DCFD5EBA7608'
                ];
                $data_option = [
                    $constants->ups_account_name => $data['AccountName4'],
                    $constants->ups_account_number => $data['AccountNumber4'],
                ];
                $data_account = array_merge($data_account, $data_option);

                    $data_account['myUpsID'] = $data['AccountUser'];
                    $data_account['myUpsPassword'] = $data['AccountPass'];
                    $data_account['get_accesss'] = true;
                $response = $this->updateAccessLicenseNumber($data_account);
                $accessLicenseNumber = '';
                if (isset($response->AccessLicenseResponse->AccessLicenseNumber)) {
                    $accessLicenseNumber = $response->AccessLicenseResponse->AccessLicenseNumber;
                }
                $data['AccountAccess'] = $accessLicenseNumber;
                $this->_acclicnumber = $accessLicenseNumber;

            }
        $tracking_number = '';
        //LINK_API
        $this->load->model($constants->link_api_model);
        //license
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        //trackingResponse
        $tracking_response = $this->callUpsApi('Track', $data, $license);
        $tracking_response = json_decode($tracking_response,true);
        if(isset($tracking_response['Fault'])){
            $errdetail = isset($tracking_response['Fault']['detail']['Errors']['ErrorDetail']) ? $tracking_response['Fault']['detail']['Errors']['ErrorDetail'] : '';
            $err_code = isset($errdetail['PrimaryErrorCode']['Code']) ? $errdetail['PrimaryErrorCode']['Code'] : '';
            $err_msg = isset($errdetail['PrimaryErrorCode']['Description']) ? $errdetail['PrimaryErrorCode']['Description'] : '';
            if($err_code == '' || $err_code == '250002' || $err_code == '250003'){
                
                echo json_encode($tracking_response);
                die();
            }else{
                // save credentials
            }
        }
       
        } else {
            //option 0
        }
        //validate_data
        if (!in_array(false, $this->_validate_data)) {
            return true;
        }
        return false;
    }

    /**
     * ControllerExtensionUpsmoduleAccount validateAccount
     *
     * @param string $input_account //The input_account
     * @param string $validate      //The validate
     *
     * @return null
     */
    public function validateAccount($input_account, $validate)
    {
        //validateAccount
        if (preg_match($validate, $input_account)) {
            return true;
        }
        return false;
    }

    /**
     * ControllerExtensionUpsmoduleAccount apiOpenAccount
     *
     * @param string $username //The username
     *
     * @return array $dataOpenAccount
     */
    public function apiOpenAccount($username)
    {
        //Load model
        $this->load->model($this->_base_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //get request
        $locate = '';
        //data post
        $data = $this->request->post;
        //get address_line_1
        $address = $data[$constants->address_line_1];
        //get address_line_2
        if ($data[$constants->address_line_2] != '') {
            $address = $address . ', ' . $data[$constants->address_line_2];
        }
        //get address_line_3
        if ($data[$constants->address_line_3] != '') {
            $address = $address . ', ' . $data[$constants->address_line_3];
        }
        //checkLanguageCountry
        $check_language_country = $this->model_extension_upsmodule_account->getLanguageCountry();
        if (isset($check_language_country[$constants->value])) {
            $locate = explode('-', $check_language_country[$constants->value]);
        }
        //decode phone
        $check_phone_number = html_entity_decode($data[$constants->phone_number_option3]);
        $phone_number = preg_replace($constants->special_phone_acc, '', $check_phone_number);
        $postal_code = html_entity_decode(trim($data[$constants->postal_code_option3]));
        //languageCountry
        $language_country = $locate[0] . '_' . $data[$constants->country_code_option3];
        $vat_tax_id = '';
        if ((!empty($data[$constants->account_vat_number]))) {
            $vat_tax_id = $data[$constants->account_vat_number];
        }
        //state province code
        $state_province_code = "XX";
        if (!empty($data[$constants->province_code_acc])) {
            $state_province_code = $data[$constants->province_code_acc];
        }
        //dataOpenAccount
        $data_open_account = [
            //Locale
            "Locale" => $this->model_extension_upsmodule_base->languageCodeForCountry($language_country),
            //EndUserInformation
            "EndUserInformation" => [
                "Username" => $username, //Username
                "VatTaxID" => $vat_tax_id, //VatTaxID
                "DeviceIdentity" => $data['ioBlackBox'] //DeviceIdentity
            ],
            //BillingAddress
            "BillingAddress" => [
                "ContactName" => $data[$constants->customer_name_option3], //ContactName
                $constants->company_name_acc => $data[$constants->company_name_option3], //CompanyName
                "StreetAddress" => $address, //StreetAddress
                "City" => $data[$constants->account_city_option3], //City
                $constants->country_code_acc => $data[$constants->country_code_option3], //CountryCode
                $constants->province_code_acc => $state_province_code, //StateProvinceCode
                $constants->postal_code_acc => $postal_code, //PostalCode
                "Phone" => [
                    "Number" => $phone_number //Number
                ]
            ],
            //PickupAddress
            "PickupAddress" => [
                "ContactName" => $data[$constants->customer_name_option3], //ContactName
                $constants->company_name_acc => $data[$constants->company_name_option3], //CompanyName
                "StreetAddress" => $address, //StreetAddress
                "City" => $data[$constants->account_city_option3], //City
                $constants->country_code_acc => $data[$constants->country_code_option3], //CountryCode
                $constants->province_code_acc => $state_province_code, //StateProvinceCode
                $constants->postal_code_acc => $postal_code, //PostalCode
                "Phone" => [
                    "Number" => $phone_number //Number
                ],
                "EmailAddress" => $data[$constants->address_email_option3] //EmailAddress
            ]
        ];
        return $data_open_account;
    }

    /**
     * ControllerExtensionUpsmoduleAccount apiPromoDiscountAgreeMent
     *
     * @return array $data_promo_discount_agreement
     */
    public function apiPromoDiscountAgreeMent()
    {
        //Load model
        $this->load->model($this->_base_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //data post
        $data = $this->request->post;
        //checkLanguageCountry
        $check_data_license = $this->model_extension_upsmodule_account->checkLicense();
        //dataPromoDiscountAgreeMent
        $account_promocode = "";
        if (isset($data['AccountPromoCode'])) {
            $account_promocode = $data['AccountPromoCode'];
        }
        if (strtolower($data[$constants->country_code_option3]) == 'us') {
            $account_promocode = $constants->const_promo_code;
        }
        $data_promo_discount_agreement = [
            //PromoCode
            "PromoCode" => $account_promocode,
            "Locale" => [
                "LanguageCode" => $check_data_license[$constants->languageCode_acc], //LanguageCode
                $constants->country_code_acc => $data[$constants->country_code_option3] //CountryCode
            ]
        ];
        return $data_promo_discount_agreement;
    }

    /**
     * ControllerExtensionUpsmoduleAccount apiPromoDiscountAgreeMent
     *
     * @return array $data_promo_discount_agreement
     */
    public function getTokenAndBingMapKey()
    {
        //Load model
        $this->load->model($this->_api_model);
        $this->load->model($this->_base_model);
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        $this->model_extension_upsmodule_base->doRegisteredPluginToken($license);
        $this->model_extension_upsmodule_base->getBingMapCredential();
    }

    /**
     * ControllerExtensionUpsmoduleAccount callUpsApi
     *
     * @param string $method  //The method
     * @param string $data    //The data
     * @param string $license //The license
     * @param string $option  //The option
     * @param string $count  //The count
     * @return $response
     */
    public function callUpsApi($method, $data = null, $license = [], $option = 0, $count = 1)
    {
        //Load model
        $this->load->model($this->_base_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $setting = $this->model_extension_upsmodule_base->getSetting();
        $commonInfo = $this->model_extension_upsmodule_base->getCommonInfo();
        include_once"$constants->link_api_ups";
        include_once"$constants->link_api_manager";
        //get api
        $Api = new Ups();
        $Api->setCommonApiInfo($commonInfo);
        $api_manager = new Manage();
        $api_manager->setCommonApiInfo($commonInfo);
        $response = null;
        $check = false;
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($method);
        switch ($method) {
            case $constants->Registration:
                if (isset($setting->ups_shipping_pre_registered_plugin_token)) {
                    $data["upsmodule_token"] = $setting->ups_shipping_pre_registered_plugin_token;
                }
                $response = $api_manager->registration($data);
                break;
            case $constants->MyUpsID:
                if (isset($setting->ups_shipping_pre_registered_plugin_token)) {
                    $data = $setting->ups_shipping_pre_registered_plugin_token;
                }
                $response = $api_manager->getUpsID($data);
                break;
            case $constants->OpenAccount:
                if (isset($setting->ups_shipping_pre_registered_plugin_token)) {
                    $data["upsmodule_token"] = $setting->ups_shipping_pre_registered_plugin_token;
                }
                $response = json_decode($api_manager->openAccount($data, $license));
                break;
            case $constants->PromoDiscount:
                if ($option == 1) {
                    $response = $Api->apiPromoDiscountAgreeMent($data, $license);
                } else {
                    $response = $Api->apiPromo($data, $license);
                }
                $check = true;
                break;
            case $constants->License:
                if (isset($setting->ups_shipping_pre_registered_plugin_token)) {
                    $data["upsmodule_token"] = $setting->ups_shipping_pre_registered_plugin_token;
                }
                $response = json_decode($api_manager->access2($data));
                break;
                case $constants->Track:
                    if (isset($setting->ups_shipping_pre_registered_plugin_token)) {
                        $check = true;
                        $data['is_acclink'] = true;
                        $data['InquiryNumber'] = '';
                        $response = $Api->statusShipment($data,$license);
                       
                    }
                    break;
                case 'access':
                    $response = json_decode($api_manager->access2($data));
                    break;
            default:
                break;
        }
        if ($check) {
            $data_api = $Api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        } else {
            $data_api = $api_manager->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        }
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);
        if (isset($response->error->errorCode) && (intval($response->error->errorCode) == 401) && $count <= 3) {
            $count += 1;
            $this->model_extension_upsmodule_base->getPreRegisteredPluginToken();
            $response = $this->callUpsApi($method, $data, $license, $option, $count);
        }
        return $response;
    }
}
