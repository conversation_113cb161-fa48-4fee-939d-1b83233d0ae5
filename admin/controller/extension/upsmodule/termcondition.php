<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleTermcondition file
 *
 * @category Termcondition_Controller
 */

class ControllerExtensionUpsmoduleTermcondition extends Controller
{
    private $_base_model = 'extension/upsmodule/base';
    private $_module_model = 'extension/module/upsmodule';
    private $_user_model = 'user/user_group';
    private $_link_module_ups = 'extension/upsmodule/';
    private $_access = 'access';
    private $_modify = 'modify';
    private $_codename = 'upsmodule';
    private $_api_manage = '../system/library/upsmodule/API/Manage.php';

    /**
     * ControllerExtensionUpsmoduleTermcondition index
     *
     * @return null
     */
    public function index()
    {
        //Load model
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "termcondition",
            "extension/upsmodule/termcondition"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        //load language
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->text_termcondition));
        //load link
        $this->load->model($constants->link_termcondition);
        //get checkValue
        $check_value = $this->model_extension_upsmodule_termcondition->checkValueCountry();
        //checkValue
        foreach ($check_value as $value) {
            //check $value['value']
            if ($value['value'] == 1) {
                $this->response->redirect($this->url->link('extension/upsmodule/account', $token, true));
            } else {
                $this->url->link('extension/upsmodule/termcondition', $token, true);
            }
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleTermcondition updateTermcondition
     *
     * @return null
     */
    public function updateTermcondition()
    {
        //Load Model
        $this->load->model($this->_user_model);
        $this->load->model($this->_base_model);
        $user_group_id = $this->user->getGroupId();
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        //load language
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->text_termcondition));
        //load link
        $this->load->model($constants->link_termcondition);
        //List Module
        $list_module_before = $this->model_extension_upsmodule_base->listModuleBefore();
        foreach ($list_module_before as $item) {
            $module = $this->_link_module_ups . $item;
            //remove Permission
            $this->model_user_user_group->removePermission($user_group_id, $this->_access, $module);
            $this->model_user_user_group->removePermission($user_group_id, $this->_modify, $module);
        }
        $list_term_condition = $this->model_extension_upsmodule_termcondition->getListSetting();
        foreach ($list_term_condition as $row) {
            if ($row['key'] == 'ups_shipping_show_term_condition'
                || $row['key'] == 'ups_shipping_accept_term_condition'
            ) {
                $this->model_extension_upsmodule_termcondition->updateTermcondition($row['key']);
            }
        }
        //region Phan quyen Menu Account
        //UPSMODULE
        $this->model_user_user_group->addPermission($user_group_id, $this->_access, 'extension/'.$this->_codename);
        $this->model_user_user_group->addPermission($user_group_id, $this->_modify, 'extension/'.$this->_codename);
        //List Module
        $list_module = $this->model_extension_upsmodule_base->listModuleAfter();
        foreach ($list_module as $item) {
            $module = $this->_link_module_ups . $item;
            //remove Permission
            $this->model_user_user_group->addPermission($user_group_id, $this->_access, $module);
            $this->model_user_user_group->addPermission($user_group_id, $this->_modify, $module);
        }
        $this->cache->delete('menu_left');
        //end
        $this->response->redirect($this->url->link('extension/upsmodule/account', $token, true));
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleTermcondition updateTermcondition
     *
     * @return null
     */
    protected function getForm()
    {
        //Include lib API
        include_once"$this->_api_manage";

        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        //load language
        $this->load->language($constants->link_translate);
        $language_code = $this->language->get('code');
        //listCheckCountry
        $list_check_country = $this->model_extension_upsmodule_termcondition->getListCheckCountry();
        foreach ($list_check_country as $row) {
            if ($row[$constants->value] == 1) {
                $this->url->link($constants->link_termcondition, $token, true);
            } else {
                $this->response->redirect($this->url->link('extension/upsmodule/country', $token, true));
            }
        }
        //listCountry curency
        $list_country = $this->model_extension_upsmodule_termcondition->getListCountry();
        $country_code = '';
        foreach ($list_country as $country) {
            if ($country[$constants->value] == 'PL') {
                $country_code = 'PL';
            } elseif ($country[$constants->value] == 'GB') {
                $country_code = 'GB';
            } else {
                $country_code = $country[$constants->value];
            }
        }
        //S translate
        $data['text_home'] = $this->language->get('text_home');
        $data['action'] = $this->url->link('extension/upsmodule/termcondition/updateTermcondition', $token, true);
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data['text_termcondition'] = $this->language->get($constants->text_termcondition);
        $data['text_connect_error'] = $this->language->get('text_connect_error');
        if (strtolower($country_code) != 'pl') {
            $data['text_read_agree'] = $this->language->get('text_read_agree');
        } else {
            $data['text_read_agree'] = $this->language->get('text_read_agree_PL');
        }
        $data['link_ups_term'] = $this->language->get('link_ups_term');
        //End language
        //API
        $this->load->model('extension/upsmodule/apiModel');
        $option = $this->model_extension_upsmodule_base->getSetting();

        $request_data = new \stdClass();
        $request_data->country_code = $country_code;
        $request_data->language_code = $language_code;
        if (isset($option->ups_shipping_pre_registered_plugin_token)) {
            $request_data->upsmodule_token = $option->ups_shipping_pre_registered_plugin_token;
        } else {
            $request_data->upsmodule_token = "";
        }

        $check_language_code = $this->model_extension_upsmodule_apiModel->checkLanguageCodeLicense();
        foreach ($check_language_code as $language) {
            if (isset($language)) {
                $this->model_extension_upsmodule_apiModel->updateLanguageCodeLicense($request_data->language_code);
            }
        }
        //call api
        $license = $this->callUpsApi($constants->License, $request_data);
        $text_api = "";
        if (is_string($license)) {
            $text_api = preg_replace("/(&#xD;)/", '<br>', $license);
        }
        
        $country_check_license = ["de", "it", "nl", "be"];
        //check data api
        if ($text_api != '') {
            if (in_array(strtolower($country_code), $country_check_license)) {
                $data[$constants->license] = iconv("UTF-8", "ISO-8859-1//TRANSLIT", $text_api);
            } else {
                $data[$constants->license] = $text_api;
            }
            $data['showButton'] = true;
        } else {
            $data[$constants->license] = "";
            $data['showButton'] = false;
        }
        $this->model_extension_upsmodule_apiModel->saveLicense($text_api, $language_code);
        $data['country_code'] = strtolower($country_code);
        //Button
        $data['button_continue'] = $this->language->get('button_continue');
        $data['button_print'] = $this->language->get('button_print');
        $data['button_ok'] = $this->language->get('button_ok');
        $data['user_token'] = $this->session->data[$constants->user_token];
        //language
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['term_condition'] = $this->url->link(
            'extension/upsmodule/termcondition&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        //add ups footer for us
        $data['ups_footer'] = '';
        if (strtolower($country_code) == 'us') {
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        }
        $this->response->setOutput($this->load->view($constants->link_termcondition, $data));
    }

    /**
     * ControllerExtensionUpsmoduleAccount callUpsApi
     *
     * @param string $method  //The method
     * @param string $data    //The data
     * @param string $license //The license
     * @param string $option  //The option
     *
     * @return $response
     */
    public function callUpsApi($method, $data, $license = [], $option = 1)
    {
        //Load model
        $this->load->model($this->_base_model);
        $setting = $this->model_extension_upsmodule_base->getSetting();
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $commonInfo = $this->model_extension_upsmodule_base->getCommonInfo();
        include_once"$constants->link_api_manager";
        //get api
        $api_manager = new Manage();
        $response = null;
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($method);
        switch ($method) {
            case $constants->License:
                $api_manager->setCommonApiInfo($commonInfo);
                $response = $api_manager->termcondition($data);
                break;
            default:
                break;
        }
        $data_api = $api_manager->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);
        if (isset($response->error->errorCode) && (intval($response->error->errorCode) == 401) && $option <= 3) {
            $option += 1;
            $this->model_extension_upsmodule_base->getPreRegisteredPluginToken();
            if (isset($setting->ups_shipping_pre_registered_plugin_token)) {
                $data->upsmodule_token = $setting->ups_shipping_pre_registered_plugin_token;
            } else {
                $data->upsmodule_token = "";
            }
            $response = $this->callUpsApi($method, $data, $license, $option);
        }
        return $response;
    }
}
