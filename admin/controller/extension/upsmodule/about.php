<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleAbout file
 *
 * @category About_Controller
 */

class ControllerExtensionUpsmoduleAbout extends Controller
{
    private $_base_model = 'extension/upsmodule/base';
    private $_about_model = 'extension/upsmodule/about';
    private $_country_model = 'extension/upsmodule/country';
    /**
     * ControllerExtensionUpsmoduleAbout index
     *
     * @return null
     */
    public function index()
    {
        //Load base model
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //check language country
        $country_setting = $this->model_extension_upsmodule_base->getSettingByKey("ups_shipping_menu_country");
        if (!empty($country_setting) && intval($country_setting) != 0) {
            //setup default language to english
            $this->load->model("extension/upsmodule/opencartsetting");
            $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
                "about",
                "extension/upsmodule/about"
            );
        }
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->about));
        //load model link
        $this->load->model($constants->link_about);
        $this->getForm();

        if (isset($this->request->post['ups_dwnld_api_log'])) {
            $sql = "SELECT * FROM " .DB_PREFIX . "upsmodule_shipping_logs_api ORDER BY `id` desc limit 0, 100";
            $query = $this->db->query($sql);
            $log_file_url = 'ups_api_log.csv';
            if (isset($query->rows) && !empty($query->rows)) {
                $fp = fopen($log_file_url, 'w');
                fputcsv($fp, array('id', 'method', 'full_uri', 'request', 'response', 'time_request', 'time_response'));
                foreach ($query->rows as $log) {
                    if ( isset($log['id']) && isset($log['method']) && isset($log['full_uri']) && isset($log['request']) && isset($log['response']) && isset($log['time_request']) && isset($log['time_response']) ) {
                        fputcsv($fp, $log);
                    }
                }
                
                fclose($fp);

                $log_file_name = basename($log_file_url);
                $log_file_info = pathinfo($log_file_name);

                if ($log_file_info["extension"] == "csv") {
                    header("Content-Description: File Transfer");
                    header("Content-Type: application/octet-stream");
                    header("Content-Disposition: attachment; filename=" . $log_file_name . "");

                    readfile($log_file_url);

                }
                unlink($log_file_url);
                exit();
            }
            // echo"<pre>";print_r($query->rows);die();
        }
    }
    /**
     * ControllerExtensionUpsmoduleBillingpreference getForm
     *
     * @return null
     */
    protected function getForm()
    {
        //Load base model
        $this->load->model($this->_base_model);
        $this->load->model($this->_about_model);
        $this->load->model($this->_country_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $data['link'] = $this->url->link(
            'extension/upsmodule/about/logApiDetail&user_token=' .
            $this->session->data[$constants->user_token],
            true
        );
        $data['text_home'] = $this->language->get('text_home');
        $data['text_module_version'] = $this->language->get('text_module_version');
        //S Translate
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data['text_About_UPS'] = $this->language->get('text_About_UPS');
        $data['text_About_Version'] = $this->language->get('text_About_Version');
        $data['text_Version'] = $constants->txt_extension_version;
        $data['text_Release'] = $this->language->get('text_Release');
        $data['text_About_Changelog'] = $this->language->get('text_About_Changelog');
        $data['text_Changelog'] = $this->language->get('text_Changelog');
        $data['text_About_Ver_Platform'] = $this->language->get('text_About_Ver_Platform');
        $data['text_Ver_Platform'] = $this->language->get('text_Ver_Platform');
        $data['text_About_Blacklink'] = $this->language->get('text_About_Blacklink');

        //translate release date.
        $language_code = $this->language->get("code");
        $release_date = [
            'day' => '18',
            'month' => 'oct',
            'year' => '2021'
        ];
        $data['text_date_release'] = $this->language->get($release_date['month']) . ' ' .
                                    $release_date['day'] . ', ' . $release_date['year'];

        //create about link
        $list_country_name = $constants->list_country_name;
        $list_link_data = $constants->about_link;

        $data_link = [];
        foreach ($list_country_name as $key => $value) {
            $ups_link = $list_link_data[$key]["en"];
            if (isset($list_link_data[$key][strtolower($language_code)])) {
                $ups_link = $list_link_data[$key][strtolower($language_code)];
            }
            $data_link[] = '<a target="__blank" href="' . $ups_link . '">' . $value . '</a>';
        }
        $link = implode(", ", $data_link);
        $data['text_Blacklink'] = sprintf($this->language->get('text_Blacklink'), $link);

        $data['text_About_Support'] = $this->language->get('text_About_Support');

        //create list phone support
        $list_phone_support = implode("", $constants->list_phone_support);
        $list_phone_support = '<ul>' . $list_phone_support . '</ul>';
        $data_config = $this->model_extension_upsmodule_base->getSetting();
        $merchant_key = "";
        if (!empty($data_config)) {
            $merchant_key = $data_config->ups_shipping_merchant_key;
        }
        $data['text_Docs'] = $this->language->get('text_Docs');
        $data['text_Docs_link'] = $this->language->get('text_Docs_link');
        $support_link = "<a href='https://ecommerce.help/product/ups-plugins' target='_blank'>".$this->language->get('text_here')."</a>";
        $data['text_non_tech_Support'] = sprintf($this->language->get('text_non_tech_Support'), $support_link);
        $data['text_Support'] = sprintf($this->language->get('text_Support'), $list_phone_support, $merchant_key);
        //table label
        $data['text_id_log'] = 'ID';
        $data['text_method_log'] = 'Method';
        $data['text_uri_log'] = 'Full URL';
        $data['text_request_log'] = 'Request';
        $data['text_response_log'] = 'Response';
        $data['text_time_request'] = 'Time Request';
        $data['text_time_response'] = 'Time Response';
        //Load model language
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        $country_data = (object) $this->model_extension_upsmodule_country->getCountryCode();
        $data['ups_footer'] = "";
        if (!empty($country_data) && strtolower($country_data->value) == "us") {
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        }
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        
        $this->response->setOutput($this->load->view($constants->link_about, $data));
    }

    /**
     * ControllerExtensionUpsmoduleBillingpreference logApiDetail
     *
     * @return content
     */
    public function logApiDetail()
    {
        $request = $this->request->post;
        $id_log_api = $request["id"];
        $this->load->model($this->_about_model);
        $log_api = $this->model_extension_upsmodule_about->getListLogApiById($id_log_api);
        echo json_encode(["content" => $log_api]);
    }
}
