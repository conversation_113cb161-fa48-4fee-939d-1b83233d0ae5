<?php

/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleShipments file
 *
 * @category Shipments_Controller
 */

class ControllerExtensionUpsmoduleShipments extends Controller
{
    private $_base_model = 'extension/upsmodule/base';
    private $_country_model = 'extension/upsmodule/country';
    private $_plugin_manage_model = 'extension/upsmodule/pluginmanage';

    /**
     * ControllerExtensionUpsmoduleShipments index
     *
     * @return null
     */
    public function index()
    {
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "shipments",
            "extension/upsmodule/shipments"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->language($constants->link_translate);
        $this->document->setTitle($this->language->get($constants->text_shipments));
        $this->load->model($constants->link_shipments);
        $check_date = $this->model_extension_upsmodule_shipments->checkDateShipments();
        foreach ($check_date as $date) {
            $order_id = '';
            $date_check_update = date($constants->ymd_orders);
            $date_create = date($constants->ymd_orders, strtotime($date['create_date']));
            $date_update = date($constants->ymd_orders, strtotime(date("Y-m-d", strtotime($date_create)) . " +90 days"));
            if (strtotime($date_update) <= strtotime($date_check_update)) {
                $order_id = $date['id'];
                $this->model_extension_upsmodule_shipments->checkDeleteDateShipments($order_id);
            }
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension getForm
     *
     * @return null
     */
    protected function getForm()
    {
        $this->load->model($this->_base_model);
        $this->load->model($this->_country_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $url = '';
        $request = $this->request->get;
        if (
            isset($this->request->get[$constants->order_shipment]) &&
            (strtolower($this->request->get[$constants->order_shipment]) != 'asc' &&
                strtolower($this->request->get[$constants->order_shipment]) != 'desc')
        ) {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        $status_id = null;
        $filter_title = null;
        $sort = $constants->shipment_number;
        $order = 'DESC';
        $page = 1;
        foreach ($request as $key => $val) {
            if ($key != "page" && $key != $constants->order_shipment && $key != "sort") {
                $url .= '&' .  $key . '=' . $val;
            }
            if ($key == "title") {
                $filter_title = $val;
            }
            if ($key == "statusid") {
                $status_id = $val;
            }
            if ($key == "sort") {
                $sort = $val;
            }
            if ($key == $constants->order_shipment && $val == 'ASC') {
                $order = 'ASC';
            }
            if ($key == "page") {
                if (isset($this->request->get['page']) && ctype_digit($this->request->get['page'])) {
                    $url .= $this->orderPage . $this->request->get['page'];
                    $page = $this->request->get['page'];
                } else {
                    http_response_code(403);
                    $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
                }
            }
        }
        $token_value = '';
        $token_name = '';
        if (isset($this->request->get['page']) && ctype_digit($this->request->get['page'])) {
            $url .= $this->orderPage . $this->request->get['page'];
            $page = $this->request->get['page'];
        } else {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
        $filter_data = [
            'filter_title' => $filter_title,
            'filter_statusid' => $status_id,
            'sort' => $sort,
            'order' => $order,
            'page' => $page,
            'limit' => $this->config->get($constants->config_limit),
            'start' => $this->config->get($constants->config_limit) * ($page - 1),
        ];
        //region data sort
        $url = '';
        if ($order == 'ASC') {
            $url .= '&order=ASC';
            $data[$constants->sort_ar_shipment] = 'asc';
        } else {
            $url .= '&order=DESC';
            $data[$constants->sort_ar_shipment] = 'desc';
        }
        //end
        $total = $this->model_extension_upsmodule_shipments->getTotalShipments();
        $list_shipments = $this->model_extension_upsmodule_shipments->getListShipment($filter_data);
        $address = [];
        if (!empty($list_shipments)) {
            foreach ($list_shipments as $key => $value) {
                $address = [
                    $list_shipments[$key][$constants->address1],
                    $list_shipments[$key][$constants->address2],
                    $list_shipments[$key][$constants->address3],
                    $list_shipments[$key]['city']
                ];
                $address = $this->addArray($address);
                $list_shipments[$key]['shipment_address'] = html_entity_decode(implode('<br />', $address));
            }
        }
        $pagination = new Pagination();
        $pagination->total = $total;
        $pagination->page = $page;
        $pagination->limit = $this->config->get($constants->config_limit);
        if ($sort == $constants->order_id_shipment) {
            $pagination->url = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_order_id . $url . $constants->page_page,
                true
            );
            $data[$constants->curren_page_shipment] = $page;
        } elseif ($sort == 'datesort') {
            $pagination->url = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_date_sort . $url . $constants->page_page,
                true
            );
            $data[$constants->curren_page_shipment] = $page;
        } elseif ($sort == 'timesort') {
            $pagination->url = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_time_sort . $url . $constants->page_page,
                true
            );
            $data[$constants->curren_page_shipment] = $page;
        } elseif ($sort == $constants->address1) {
            $pagination->url = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_address1 . $url . $constants->page_page,
                true
            );
            $data[$constants->curren_page_shipment] = $page;
        } elseif ($sort == $constants->shipping_fee) {
            $pagination->url = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_shipping_fee . $url . $constants->page_page,
                true
            );
            $data[$constants->curren_page_shipment] = $page;
        } elseif ($sort == $constants->shipment_number) {
            $pagination->url = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_shipment_number . $url . $constants->page_page,
                true
            );
            $data[$constants->curren_page_shipment] = $page;
        } elseif ($sort == $constants->tracking_number) {
            $pagination->url = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_tracking_number . $url . $constants->page_page,
                true
            );
            $data[$constants->curren_page_shipment] = $page;
        } else {
            $pagination->url = '';
            $data[$constants->curren_page_shipment] = '';
        }
        $url = '';
        if ($order == 'ASC') {
            $url .= '&order=DESC';
            $data[$constants->sort_ar_shipment] = 'asc';
        } else {
            $url .= '&order=ASC';
            $data[$constants->sort_ar_shipment] = 'desc';
        }
        $data[$constants->order_id_shipment] = $this->url->link(
            $constants->link_shipments,
            $constants->token . $constants->sort_order_id . $url,
            true
        );
        $data['sort_date'] = $this->url->link(
            $constants->link_shipments,
            $constants->token . $constants->sort_date_sort . $url,
            true
        );
        $data['sort_time'] = $this->url->link(
            $constants->link_shipments,
            $constants->token . $constants->sort_time_sort . $url,
            true
        );
        $data[$constants->address1] = $this->url->link(
            $constants->link_shipments,
            $constants->token . $constants->sort_address1 . $url,
            true
        );
        $data[$constants->shipping_fee] = $this->url->link(
            $constants->link_shipments,
            $constants->token . $constants->sort_shipping_fee . $url,
            true
        );
        $data[$constants->shipment_number] = $this->url->link(
            $constants->link_shipments,
            $constants->token . $constants->sort_shipment_number . $url,
            true
        );
        $data[$constants->tracking_number] = $this->url->link(
            $constants->link_shipments,
            $constants->token . $constants->sort_tracking_number . $url,
            true
        );
        $data['sort'] = $sort;
        $data['order'] = $order;
        if (isset($data[$constants->curren_page_shipment])) {
            $data[$constants->order_id_shipment] = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_order_id . $url . $constants->page_shipment .
                    $data[$constants->curren_page_shipment],
                true
            );
            $data['sort_date'] = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_date_sort . $url . $constants->page_shipment .
                    $data[$constants->curren_page_shipment],
                true
            );
            $data['sort_time'] = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_time_sort . $url . $constants->page_shipment .
                    $data[$constants->curren_page_shipment],
                true
            );
            $data[$constants->address1] = $this->url->link(
                $constants->link_shipments,
                $constants->token .
                    $constants->sort_address1 . $url . $constants->page_shipment .
                    $data[$constants->curren_page_shipment],
                true
            );
            $data[$constants->shipping_fee] = $this->url->link(
                $constants->link_shipments,
                $constants->token . $constants->sort_shipping_fee . $url . $constants->page_shipment .
                    $data[$constants->curren_page_shipment],
                true
            );
            $data[$constants->shipment_number] = $this->url->link(
                $constants->link_shipments,
                $constants->token . $constants->sort_shipment_number . $url . $constants->page_shipment .
                    $data[$constants->curren_page_shipment],
                true
            );
            $data[$constants->tracking_number] = $this->url->link(
                $constants->link_shipments,
                $constants->token . $constants->sort_tracking_number . $url . $constants->page_shipment .
                    $data[$constants->curren_page_shipment],
                true
            );
        }
        $pagination->url = $this->url->link(
            $constants->link_shipments,
            $constants->token . $constants->page_page,
            true
        );
        $data['pagination'] = $pagination->render();
        $page_total_1 = 0;
        $page_total_2 = '';
        if ($total) {
            $page_total_1 = (($page - 1) * $this->config->get($constants->config_limit)) + 1;
        }
        if ((($page - 1) * $this->config->get($constants->config_limit))
            > ($total - $this->config->get($constants->config_limit))
        ) {
            $page_total_2 = $total;
        } else {
            $page_total_2 = ((($page - 1) * $this->config->get($constants->config_limit)) + $this->config->get($constants->config_limit));
        }
        $data['results'] = sprintf(
            $this->language->get('text_pagination'),
            $page_total_1,
            $page_total_2,
            $total,
            ceil($total / $this->config->get($constants->config_limit))
        );
        //end
        $data['listShipments'] = $list_shipments;
        $data['urlCancelShipment'] = $this->url->link(
            'extension/upsmodule/shipments/cancelShipment&user_token=' .
                $this->session->data[$constants->user_token],
            true
        );
        $data['exportCSV'] = $this->url->link(
            'extension/upsmodule/shipments/exportCSV',
            $constants->token . $url,
            true
        );
        $data['urldetailshipment'] = $this->url->link(
            'extension/upsmodule/shipments/detailShipment&user_token=' .
                $this->session->data[$constants->user_token],
            true
        );
        $data['printLabel'] = $this->url->link(
            'extension/upsmodule/shipments/printLabel&user_token=' .
                $this->session->data[$constants->user_token],
            true
        );
        $data['ups_footer'] = "";
        $country_data = (object) $this->model_extension_upsmodule_country->getCountryCode();
        if (!empty($country_data) && strtolower($country_data->value) == "us") {
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        }
        $data['text_home'] = $this->language->get('text_home');
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data['text_export_all_orders'] = $this->language->get('text_export_all_orders');
        $data[$constants->text_shipments] = $this->language->get($constants->text_shipments);
        $data['text_archived_orders'] = $this->language->get('text_archived_orders');
        $data['text_id_order'] = $this->language->get('text_id_order');
        $data['text_product'] = $this->language->get('text_product');
        $data['text_delivery_address'] = $this->language->get('text_delivery_address');
        $data['text_shipping_service'] = $this->language->get('text_shipping_service');
        $data['text_cod'] = $this->language->get('text_cod');
        $data['text_no_results'] = $this->language->get('text_no_results');
        $data['text_print_label_pdf'] = $this->language->get('text_print_label_pdf');
        $data['text_print_label_zpl'] = $this->language->get('text_print_label_zpl');
        $data['text_export_shipments'] = $this->language->get('text_export_shipments');
        $data['text_cancel_shipments'] = $this->language->get('text_cancel_shipments');
        $data['text_id_shipment'] = $this->language->get('text_id_shipment');
        $data['text_tracking_number'] = $this->language->get('text_tracking_number');
        $data['text_order_id'] = $this->language->get('text_order_id');
        $data['text_date'] = $this->language->get('text_date');
        $data['text_time'] = $this->language->get('text_time');
        $data['text_estimated_fee'] = $this->language->get('text_estimated_fee');
        $data['text_estimated_fee02'] = $this->language->get('text_estimated_fee02');
        $data['text_are_you_sure_you_want_to_cancel_selected_shipments']
            = $this->language->get('text_are_you_sure_you_want_to_cancel_selected_shipments');
        $data['account_phone_number'] = $this->language->get('account_phone_number');
        $data['text_ArcAddress'] = $this->language->get('text_ArcAddress');
        $data['text_ArcProduct'] = $this->language->get('text_ArcProduct');
        $data['text_ShipmentsTracking'] = $this->language->get('text_ShipmentsTracking');
        $data['text_ShipmentsOrderID'] = $this->language->get('text_ShipmentsOrderID');
        $data[$constants->text_shipments] = $this->language->get($constants->text_shipments);
        $data['text_OpenEmail'] = $this->language->get('text_OpenEmail');
        $data['text_ArcShippingService'] = $this->language->get('text_ArcShippingService');
        $data['text_OpenEstimatedShippingFee'] = $this->language->get('text_OpenEstimatedShippingFee');
        $data['text_ArcOrderValue'] = $this->language->get('text_ArcOrderValue');
        $data['text_ShipmentsPackageDetails'] = $this->language->get('text_ShipmentsPackageDetails');
        $data['text_OpenAccessPoint'] = $this->language->get('text_OpenAccessPoint');
        $data['button_cancel'] = $this->language->get('button_cancel');
        $data['ups_tracking_terms_and_conditions'] = $this->language->get('ups_tracking_terms_and_conditions');
        $data['text_notice_terms_and_conditions'] = $this->language->get('text_notice_terms_and_conditions');
        $data['by_select_text_terms_and_conditions'] = $this->language->get('by_select_text_terms_and_conditions');
        $data['text_SecondTandC'] = $this->language->get('text_SecondTandC');
        $data['text_cancel_shipments_popup'] = $this->language->get('text_cancel_shipments_popup');
        $data['text_order_exist'] = $this->language->get('text_order_exist');
        $data['shipment_canceled'] = $this->language->get('shipment_canceled');
        $data[$constants->ups_access_point_cod] = $this->language->get($constants->ups_access_point_cod);
        $data[$constants->ups_home_cod] = $this->language->get($constants->ups_home_cod);
        $data['UPS_ACSRL_ADDITIONAL_HADING'] = $this->language->get('UPS_ACSRL_ADDITIONAL_HADING');
        $data['UPS_ACSRL_QV_SHIP_NOTIF'] = $this->language->get('UPS_ACSRL_QV_SHIP_NOTIF');
        $data['UPS_ACSRL_QV_DLV_NOTIF'] = $this->language->get('UPS_ACSRL_QV_DLV_NOTIF');
        $data['UPS_ACSRL_RESIDENTIAL_ADDRESS'] = $this->language->get('UPS_ACSRL_RESIDENTIAL_ADDRESS');
        $data['UPS_ACSRL_STATURDAY_DELIVERY'] = $this->language->get('UPS_ACSRL_STATURDAY_DELIVERY');
        $data['UPS_ACSRL_CARBON_NEUTRAL'] = $this->language->get('UPS_ACSRL_CARBON_NEUTRAL');
        $data['UPS_ACSRL_DIRECT_DELIVERY_ONLY'] = $this->language->get('UPS_ACSRL_DIRECT_DELIVERY_ONLY');
        $data['UPS_ACSRL_DECLARED_VALUE'] = $this->language->get('UPS_ACSRL_DECLARED_VALUE');
        $data['UPS_ACSRL_SIGNATURE_REQUIRED'] = $this->language->get('UPS_ACSRL_SIGNATURE_REQUIRED');
        $data['UPS_ACSRL_ADULT_SIG_REQUIRED'] = $this->language->get('UPS_ACSRL_ADULT_SIG_REQUIRED');
        $data['estimated_currency_code'] = $this->model_extension_upsmodule_base->getSettingByKey(
            "ups_shipping_fee_currency_code"
        );
        $data['text_Action'] = $this->language->get('text_Action');
        $data['cancel_shipment'] = $this->language->get('cancel_shipment');
        $data['modal_loading'] = $this->load->view('extension/upsmodule/modalloading');
        $data['openorders'] = $this->url->link(
            $constants->link_openorders,
            $constants->token . '&sort=id&order=DESC&page=1',
            true
        );
        $data['shipments'] = $this->url->link(
            $constants->link_shipments,
            $constants->token . '&sort=shipment_number&order=DESC&page=1',
            true
        );
        $data['archivedorders'] = $this->url->link(
            'extension/upsmodule/archivedorders',
            $constants->token . '&sort=id&order=DESC&page=1',
            true
        );
        //end
        $data[$constants->user_token] = $this->session->data[$constants->user_token];
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['shipment'] = $this->url->link(
            'extension/upsmodule/shipments&user_token=' .
                $this->session->data[$constants->user_token] . $constants->sort_shipment_number .
                $constants->link_sort_page
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
                $this->session->data[$constants->user_token]
        );
        $this->response->setOutput($this->load->view($constants->link_shipments, $data));
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension cancelShipment
     *
     * @return null
     */
    public function cancelShipment()
    {
        $this->load->model($this->_base_model);
        $this->load->model($this->_plugin_manage_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_shipments);
        $this->load->language($constants->link_translate);
        $request = $this->request->post;
        $arr_shipment_number = [];
        $arr_order = [];
        $cancel_status = true;
        $message = '';
        $cancel_shipment_list = $request['cancel_shipment_data'];
        //Lấy dữ liệu trước khi xóa
        //Danh sách shipment
        //Shipment Array
        $shipment = [];
        foreach ($cancel_shipment_list as $item) {
            $number_shipment = $item;
            if (!in_array($number_shipment, $arr_shipment_number)) {
                $arr_shipment_number[] = $number_shipment;
                //get list tracking number of shipment number
                $list_tracking
                    = $this->model_extension_upsmodule_shipments->getListTrackingNumberByShipmentNumber(
                        $number_shipment
                    );
                if (!empty($list_tracking)) {
                    $checkVoidShipment = $this->model_extension_upsmodule_shipments->voidShipmentByShipmentNumber(
                        $number_shipment
                    );
                    //update status open order.
                    if ($checkVoidShipment[$constants->check]) {
                        $uniqueTracking = [];
                        foreach ($list_tracking as $items) {
                            if (!in_array($items[$constants->order_id_shipment], $arr_order)) {
                                $arr_order[] = $items[$constants->order_id_shipment];
                                $this->model_extension_upsmodule_shipments->updateStatusCancelShipment(
                                    $items[$constants->order_id_shipment]
                                );
                                $this->model_extension_upsmodule_shipments->updateStatusOrder($items['order_id_cart']);
                                $this->model_extension_upsmodule_shipments->deleteShipmentid(
                                    $items['order_id_cart']
                                );
                            }
                            if (!in_array($items[$constants->tracking_number], $uniqueTracking)) {
                                $shipment_data = new \stdClass();
                                $shipment_data->tracking_number = $items[$constants->tracking_number];
                                $shipment_data->shipment_status = 'processing_in_progress';
                                $shipment[] = $shipment_data;
                                $uniqueTracking[] = $items[$constants->tracking_number];
                            }
                        }
                        $this->model_extension_upsmodule_shipments->deleteTracking($number_shipment);
                        $this->model_extension_upsmodule_shipments->deleteRowShipment($number_shipment);
                    } else {
                        $cancel_status = false;
                        $messageFail = $checkVoidShipment[$constants->message];
                        if ($messageFail == 'The Shipment was not voided') {
                            $message = $this->language->get('text_message_voided');
                        } else {
                            $message = $checkVoidShipment[$constants->message];
                        }
                    }
                }
            }
        }
        if (!empty($shipment)) {
            $shipment = array_unique($shipment, SORT_REGULAR);
            //save data Update status
            $this->model_extension_upsmodule_pluginmanage->updateShipmentStatus($shipment);
        }
        echo json_encode([$constants->check => $cancel_status, $constants->message => $message]);
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension exportCSV
     *
     * @return null
     */
    public function exportCsv()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->language($constants->link_translate);
        $this->load->model($constants->link_opencartsetting);
        $this->load->model($constants->link_shipments);
        $this->load->model($constants->link_country);
        $request = $this->request->post;
        $shipment_tracking_ids = html_entity_decode($request['shipmentTrackingIds']);
        $shipment_tracking_ids = str_replace('"', '', substr($shipment_tracking_ids, 1, -1));
        $orderBy = $request['OrderByShipment'];
        if ($request['shipmentTrackingIds'] != '') {
            $list_shipments
                = $this->model_extension_upsmodule_shipments->getExportShipmentData($shipment_tracking_ids, $orderBy);
        }
        $data_export = [
            [
                'Shipment ID', 'Date', 'Time', 'Tracking number', 'deliveryStatus', 'COD', 'CODAmount', 'CODCurrency',
                'Estimated shipping fee', 'Shipping service', 'Accessorials', 'Order ID', 'Order date', 'Order value',
                'Shipping fee', 'Package details', 'Product details', 'Customer name', 'Customer Address line 1',
                'Customer Address line 2', 'Customer Address line 3', 'Customer PostalCode', 'Customer Phone no',
                'Customer City', 'Customer StateOrProvince', 'Customer Country', 'Customer Email',
                'AlternateDeliveryAddressIndicator', 'UPSAccessPointID', 'Access Point Address line 1',
                'Access Point Address line 2', 'Access Point Address line 3', 'Access Point City',
                'Access Point StateOrProvince', 'Access Point PostalCode', 'Access Point Country'
            ]
        ];
        $arr_country_state_name = $this->model_extension_upsmodule_country->getStateCountryName();
        $array_country_state_name  = [];
        foreach ($arr_country_state_name as $item) {
            $array_country_state_name[$item['countryCode']][$item['stateCode']] = $item['StateName'];
        }
        foreach ($list_shipments as $key => $shipment) {
            if ($shipment[$constants->decimal_place] != '') {
                $decimal_value = $shipment[$constants->decimal_place];
            } else {
                $decimal_value = 0;
            }
            $cod = '';
            $cod_amount = '';
            $cod_currency = '';
            $access_cod = $this->language->get($constants->ups_access_point_cod);
            $home_cod = $this->language->get($constants->ups_home_cod);
            $accessorial_array = [];
            $accessorial_array = [];
            if (!empty($shipment[$constants->accessorial_service_shipment])) {
                $accessorial_array = json_decode($shipment[$constants->accessorial_service_shipment], true);
                foreach ($accessorial_array as $key1 => $value) {
                    $accessorial_translate = $this->language->get($key1);
                    $accessorial_array[$key1] = $accessorial_translate;
                }
            }
            $accessorial_shipment = [];
            $accessorial_value = '';
            $order_value = '';
            foreach ($accessorial_array as $value) {
                $accessorial_shipment[] = $value;
            }
            if ($shipment['cod'] == 1) {
                if ($shipment[$constants->service_type_shipment] == "AP") {
                    $accessorial_shipment[] = $access_cod;
                } elseif ($shipment[$constants->service_type_shipment] == "ADD") {
                    $accessorial_shipment[] = $home_cod;
                } else {
                    $accessorial_value = '';
                }
                $method = $this->language->get('text_yes');
                $order_value = number_format($shipment[$constants->order_value], $decimal_value);
            } else {
                $method = $this->language->get('text_no');
                $order_value = '';
                $shipment[$constants->currency_code] = '';
            }
            $check_name = explode(",", $shipment['listProduct']);
            $cut_name = array_unique($check_name);
            $get_name = implode(", ", $cut_name);
            $service_name = str_replace("&reg;", "", $shipment['service_name']);
            $row_export = [
                'Shipment_ID' => $shipment[$constants->shipment_number],
                'Date' => date($constants->ymd_shipment, strtotime($shipment[$constants->create_date])),
                'Time' => date('H:i:s', strtotime($shipment[$constants->create_date])),
                'Tracking_number' => $shipment[$constants->tracking_number],
                'deliveryStatus' => $shipment['status'],
                'COD' => $method,
                'CODAmount' => '"' . $order_value . '"',
                'CODCurrency' => $shipment[$constants->currency_code],
                'Estimated_shipping_fee' => '"' . number_format(
                    (float)preg_replace($constants->number_format, '', $shipment[$constants->shipping_fee]),
                    2
                ) . '"',
                'Shipping_service' => '"' . $service_name . '"',
                'Accessorials' => '"' . implode(', ', $accessorial_shipment) . '"',
                'Order_ID' => $shipment[$constants->order_id_shipment],
                'Order_date' => date($constants->ymd_shipment, strtotime($shipment['date_added'])),
                'Order_value' => '"' . number_format($shipment[$constants->order_value], $decimal_value) . '"',
                'Shipping_fee' => '"' . number_format($shipment['shippingFeeTotal'], $decimal_value) . '"',
                'Package_details' => '"' . $shipment[$constants->package_detail] . '"',
                'Product_details' => '"' . $get_name . '"',
                'Customer_name' => $shipment['shipping_firstname'] . ' ' . $shipment['shipping_lastname'],
                'Customer_Address_line_1' => '"' . str_replace(
                    $constants->address_replace,
                    '',
                    html_entity_decode($shipment[$constants->shipping_address1])
                ) . '"',
                'Customer_Address_line_2' => '"' . str_replace(
                    $constants->address_replace,
                    '',
                    html_entity_decode($shipment[$constants->shipping_address2])
                ) . '"',
                'Customer_Address_line_3' => '',
                'Customer_PostalCode' => $shipment[$constants->shipping_posttal_code],
                'Customer_Phone' => $shipment[$constants->telephone],
                'Customer_City' => $shipment[$constants->ship_city],
                'Customer_StateOrProvince' => $shipment[$constants->ship_zone],
                'Customer_Country' => $shipment[$constants->shipping_country_shipment],
                'Customer_Email' => html_entity_decode($shipment['customer_email'])
            ];
            if ($shipment[$constants->service_type_shipment] == "AP") {
                $check_state = '';
                if (isset($array_country_state_name[$shipment[$constants->country_shipment]][$shipment[$constants->state_shipment]])) {
                    $check_state = $array_country_state_name[$shipment[$constants->country_shipment]][$shipment[$constants->state_shipment]];
                }
                $row_export['AlternaetDeliveryAddressIndicator'] = 1;
                $row_export['UPSAcessPointID'] = $shipment[$constants->access_id];
                $row_export['Access_Point_Address_line_1'] = '"' . $shipment[$constants->name_value] . '"';
                $row_export['Access_Point_Address_line_2'] = '"' . str_replace(
                    $constants->address_replace,
                    '',
                    html_entity_decode($shipment[$constants->address1])
                ) . '"';
                $row_export['Access_Point_Address_line_3'] = '"' . str_replace(
                    $constants->address_replace,
                    '',
                    html_entity_decode($shipment[$constants->address2])
                ) . '"';
                $row_export['Access_Point_City'] = html_entity_decode($shipment['city']);
                $row_export['Access_Point_StateOrProvince'] = html_entity_decode($check_state);
                $row_export['Access_Point_PostalCode'] = $shipment[$constants->post_code_shipment];
                $row_export['Access_Point_Country'] = $shipment['nameCountry'];
            } else {
                $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByStateCode(
                    $shipment[$constants->state_shipment],
                    $shipment[$constants->country_shipment]
                );
                $country_data = $this->model_extension_upsmodule_opencartsetting->getCountryByCountryCode(
                    $shipment[$constants->country_shipment]
                );
                $state = '';
                $country = '';
                if (!empty($state_data)) {
                    $state = $state_data[$constants->name_value];
                }
                if (!empty($country_data)) {
                    $country = $country_data[$constants->name_value];
                }
                $row_export['Customer_name'] = '"' . $shipment[$constants->name_value] . '"';
                $row_export['Customer_Address_line_1'] = '"' . str_replace(
                    $constants->address_replace,
                    '',
                    html_entity_decode($shipment[$constants->address1])
                ) . '"';
                $row_export['Customer_Address_line_2'] = '"' . str_replace(
                    $constants->address_replace,
                    '',
                    html_entity_decode($shipment[$constants->address2])
                ) . '"';
                $row_export['Customer_Address_line_3'] = '"' . str_replace(
                    $constants->address_replace,
                    '',
                    html_entity_decode($shipment[$constants->address3])
                ) . '"';
                $row_export['Customer_PostalCode'] = $shipment[$constants->post_code_shipment];
                $row_export['Customer_Phone'] = $shipment[$constants->phone_shipment];
                $row_export['Customer_City'] = $shipment['city'];
                $row_export['Customer_StateOrProvince'] = html_entity_decode($state);
                $row_export['Customer_Country'] = $country;
                $row_export['Customer_Email'] = html_entity_decode($shipment[$constants->email_data]);
                $row_export['AlternaetDeliveryAddressIndicator'] = 0;
                $row_export['UPSAcessPointID'] = '';
                $row_export['Access_Point_Address_line_1'] = '';
                $row_export['Access_Point_Address_line_2'] = '';
                $row_export['Access_Point_Address_line_3'] = '';
                $row_export['Access_Point_City'] = '';
                $row_export['Access_Point_StateOrProvince'] = '';
                $row_export['Access_Point_PostalCode'] = '';
                $row_export['Access_Point_Country'] = '';
            }
            array_push($data_export, $row_export);
        }
        $file_name = "shipments_data_" . date("dmy") . ".csv";
        header('Content-type: text/csv');
        header('Content-Type: application/force-download; charset=UTF-8');
        header('Cache-Control: no-store, no-cache');
        header("Content-Disposition: attachment; filename=\"$file_name\"");
        $this->model_extension_upsmodule_shipments->exportCsvFile($data_export);
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension printLabel
     *
     * @return null
     */
    public function printLabel()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_api_model);
        $this->load->model($constants->link_shipments);
        $request = $this->request->post;
        $list_tracking = html_entity_decode($request['listCheckedLabel']);
        $label_option = html_entity_decode($request['labelOption']);
        $file_ext = '.' . strtolower($label_option);
        $list_order = explode('","', substr($list_tracking, 2, -2));
        $list_order = array_unique($list_order);
        $array_list = [];
        foreach ($list_order as $key2 => $value) {
            $data = [];
            $data['TrackingNumber'] = $value;
            $data['LabelFormat'] = $label_option;
            $license = $this->model_extension_upsmodule_apiModel->getLicense();
            $label_recovery = $this->callUpsApi($constants->LBRecovery, $data, $license);
            $label_results_print = [];
            /* check response print label */
            if (
                isset($label_recovery->LabelRecoveryResponse->Response->ResponseStatus->Code) &&
                ($label_recovery->LabelRecoveryResponse->Response->ResponseStatus->Code == 1) &&
                isset($label_recovery->LabelRecoveryResponse->LabelResults)
            ) {
                /* check result data */
                if (is_array($label_recovery->LabelRecoveryResponse->LabelResults)) {
                    foreach ($label_recovery->LabelRecoveryResponse->LabelResults as $item) {
                        if (isset($item->LabelImage->GraphicImage)) {
                            $label_results_print[] = $item;
                        }
                    }
                } elseif (isset($label_recovery->LabelRecoveryResponse->LabelResults->LabelImage->GraphicImage)) {
                    $label_results_print[] = $label_recovery->LabelRecoveryResponse->LabelResults;
                }
            }
            $decoded = "";
            foreach ($label_results_print as $label_result) {
                $decoded .= base64_decode($label_result->LabelImage->GraphicImage);
            }
            $temp_name = tempnam(sys_get_temp_dir(), $constants->label_shipment . $key2);
            rename($temp_name, $temp_name . $file_ext);
            file_put_contents($temp_name, $decoded);
            $array_list[] = [$temp_name, $value];
        }
        $zip = new \ZipArchive;
        $link_zip = tempnam(sys_get_temp_dir(), $constants->label_shipment);
        rename($link_zip, $link_zip .= '.zip');
        if ($zip->open($link_zip, \ZipArchive::CREATE) === true) {
            foreach ($array_list as $key3 => $value) {
                $linkfile = $value[0];
                $zip->addFile($linkfile, $constants->label_shipment . $key3 . '_' . $value[1] . $file_ext);
            }
            $zip->close();
            foreach ($array_list as $value) {
                $linkfile = $value[0];
                $this->model_extension_upsmodule_shipments->unlinkFile($linkfile);
            }
            header("Content-type: application/zip");
            header("Content-Disposition: attachment; filename=" . $constants->label_shipment . ".zip");
            header("Pragma: no-cache");
            header("Expires: 0");
            readfile($link_zip);
            $this->model_extension_upsmodule_shipments->unlinkFile($link_zip);
        }
    }

    public function printLabelForced($trk_data)
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_api_model);
        $this->load->model($constants->link_shipments);
        $file_ext = '.' . strtolower($trk_data['label_format']);
        $array_list = [];
        $trk_no = isset($trk_data['trk_no']) ? $trk_data['trk_no'] : [];
        foreach ($trk_no as $key2 => $value) {
            $data = [];
            $data['TrackingNumber'] = $value;
            $data['LabelFormat'] = $trk_data['label_format'];
            $license = $this->model_extension_upsmodule_apiModel->getLicense();
            $label_recovery = $this->callUpsApi($constants->LBRecovery, $data, $license);
            $label_results_print = [];
            /* check response print label */
            if (
                isset($label_recovery->LabelRecoveryResponse->Response->ResponseStatus->Code) &&
                ($label_recovery->LabelRecoveryResponse->Response->ResponseStatus->Code == 1) &&
                isset($label_recovery->LabelRecoveryResponse->LabelResults)
            ) {
                /* check result data */
                if (is_array($label_recovery->LabelRecoveryResponse->LabelResults)) {
                    foreach ($label_recovery->LabelRecoveryResponse->LabelResults as $item) {
                        if (isset($item->LabelImage->GraphicImage)) {
                            $label_results_print[] = $item;
                        }
                    }
                } elseif (isset($label_recovery->LabelRecoveryResponse->LabelResults->LabelImage->GraphicImage)) {
                    $label_results_print[] = $label_recovery->LabelRecoveryResponse->LabelResults;
                }
            }
            $decoded = "";
            foreach ($label_results_print as $label_result) {
                $decoded .= base64_decode($label_result->LabelImage->GraphicImage);
            }
            $temp_name = tempnam(sys_get_temp_dir(), $constants->label_shipment . $key2);
            rename($temp_name, $temp_name . $file_ext);
            file_put_contents($temp_name, $decoded);
            $array_list[] = [$temp_name, $value];
        }
        $zip = new \ZipArchive;
        $link_zip = tempnam(sys_get_temp_dir(), $constants->label_shipment);
        rename($link_zip, $link_zip .= '.zip');
        if ($zip->open($link_zip, \ZipArchive::CREATE) === true) {
            foreach ($array_list as $key3 => $value) {
                $linkfile = $value[0];
                $zip->addFile($linkfile, $constants->label_shipment . $key3 . '_' . $value[1] . $file_ext);
            }
            $zip->close();
            foreach ($array_list as $value) {
                $linkfile = $value[0];
                $this->model_extension_upsmodule_shipments->unlinkFile($linkfile);
            }
            header("Content-type: application/zip");
            header("Content-Disposition: attachment; filename=" . $constants->label_shipment . ".zip");
            header("Pragma: no-cache");
            header("Expires: 0");
            readfile($link_zip);
            $this->model_extension_upsmodule_shipments->unlinkFile($link_zip);
        }
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension createShipment
     *
     * @return null
     */
    public function createShipment()
    {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            try {
                $this->load->model($this->_base_model);
                //set secure header
                $this->model_extension_upsmodule_base->setHeaderSecure();
                //get constant
                $constants = $this->model_extension_upsmodule_base->listConstanst();
                $this->load->model('localisation/country');
                $this->load->model($constants->link_shipments);
                $this->load->model($constants->link_api_model);
                $this->load->model($constants->link_packagedimension);
                $this->load->model('extension/upsmodule/tracking');
                $this->load->model($constants->link_openorders);
                $this->load->model($constants->link_opencartsetting);
                $license = $this->model_extension_upsmodule_apiModel->getLicense();
                $data_post = $this->request->post;

                $check_name_country = $data_post[$constants->order_selected];
                $get_country_name = $check_name_country[$constants->shipping_country_shipment];
                $get_list_country = $this->model_extension_upsmodule_shipments->getListCountry();
                foreach ($get_list_country as $value) {
                    $code_country = $value['name'];
                    if ($get_country_name == $code_country) {
                        $checkCountry = $value['iso_code_2'];
                    }
                }
                $ship_from = $data_post[$constants->ship_from];
                $check_phone_from = html_entity_decode($ship_from[3]);
                $ship_from[3] = preg_replace($constants->special_phone, '', $check_phone_from);
                $check_postal_from = html_entity_decode($ship_from[8]);
                $ship_from[8]
                    = str_replace('amp', '', preg_replace($constants->special_posttal_code, '', $check_postal_from));
                $ship_to = $data_post['ship_to'];
                $check_phone_to = html_entity_decode($ship_to[2]);
                $ship_to[2] = preg_replace($constants->special_phone, '', $check_phone_to);
                $check_postal_to = html_entity_decode($ship_to[7]);
                $ship_to[7] =
                    str_replace('amp', '', preg_replace($constants->special_posttal_code, '', $check_postal_to));
                $shipping_type = $data_post['shipping_type'];
                $package = $data_post['package'];
                if (!empty($data_post[$constants->accessorial_service_shipment])) {
                    $accessorial_service = $data_post[$constants->accessorial_service_shipment];
                } else {
                    $accessorial_service = [];
                }
                $order_selected = $data_post[$constants->order_selected];
                $list_order = $data_post['idorder'];
                $cod = $data_post['cod'];
                $order_value = (float)filter_var(
                    $data_post[$constants->order_value],
                    FILTER_SANITIZE_NUMBER_FLOAT,
                    FILTER_FLAG_ALLOW_FRACTION
                );
                $check_edit = $data_post['edit_shipment'];
                $shipto_format = [];
                if ($shipping_type['0'] == 'ADD' && $check_edit == 1) {
                    $shipto_format[] = $ship_to[0];
                    $shipto_format[] = $ship_to[0];
                    $shipto_format[] = $ship_to[2];
                    $shipto_format[] = substr($ship_to[3], 0, 35); //address 1
                    $shipto_format[] = substr($ship_to[4], 0, 35); //address 2
                    $shipto_format[] = substr($ship_to[5], 0, 35); //address 3
                    $shipto_format[] = $ship_to[6];
                    $shipto_format[] = $ship_to[1];
                    $shipto_format[] = implode("", explode(" ", $ship_to[7]));
                    $shipto_format[] = $ship_to[8]; //$checkCountry; //country code
                    $shipto_format[] = $ship_to[9];
                } else {
                    $name = $order_selected[$constants->first_name] . ' ' . $order_selected[$constants->last_name];
                    $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByZoneId(
                        $order_selected[$constants->shipping_zone_id]
                    );
                    $country_data = $this->model_localisation_country->getCountry(
                        $order_selected[$constants->shipping_country_id]
                    );
                    $shipto_format[] = $name;
                    $shipto_format[] = $name;
                    $shipto_format[] = $order_selected[$constants->telephone];
                    $shipto_format[] = substr($order_selected[$constants->shipping_address1], 0, 35);
                    $shipto_format[] = substr($order_selected[$constants->shipping_address2], 0, 35);
                    $shipto_format[] = '';
                    $shipto_format[] = $order_selected[$constants->ship_city];
                    $shipto_format[] = (!empty($state_data)) ? $state_data['code'] : '';
                    $shipto_format[] = implode("", explode(" ", $order_selected[$constants->shipping_posttal_code]));
                    $shipto_format[] = (!empty($country_data)) ? $country_data[$constants->iso_code_2_shipment] : 'PL';
                    $shipto_format[] = $order_selected[$constants->email_data];
                    $shipto_format[] = $order_selected[$constants->access_id];
                }
                $data = [];
                $data[$constants->shipping_type] = $shipping_type[0];
                $data[$constants->currency_code_data] = $order_selected[$constants->currency_code];
                $data[$constants->monetary_value] = $order_value;
                $data[$constants->shipper][$constants->name_data] = $ship_from[0];
                $data[$constants->shipper][$constants->attention_name] = $ship_from[0];
                $data[$constants->shipper][$constants->shipper_number] = $ship_from[2];
                $data[$constants->shipper][$constants->phone_data][$constants->number_data] = $ship_from[3];
                $data[$constants->shipper][$constants->address_data][$constants->address_line]
                    = [$ship_from[4], $ship_from[5], $ship_from[6]];
                $data[$constants->shipper][$constants->address_data][$constants->city_data] = $ship_from[7];
                $data[$constants->shipper][$constants->address_data][$constants->province_code_acc] = $ship_from[1];
                $data[$constants->shipper][$constants->address_data][$constants->postal_code_shipment] = $ship_from[8];
                $data[$constants->shipper][$constants->address_data][$constants->country_code_shipment]
                    = $ship_from[9];
                $data[$constants->ship_to_data][$constants->name_data] = $shipto_format[0];
                $data[$constants->ship_to_data][$constants->attention_name] = $shipto_format[1];
                $data[$constants->ship_to_data][$constants->phone_data][$constants->number_data]
                    = $shipto_format[2];
                $data[$constants->ship_to_data][$constants->address_data][$constants->address_line]
                    = [$shipto_format[3], $shipto_format[4], $shipto_format[5]];
                $data[$constants->ship_to_data][$constants->address_data][$constants->city_data]
                    = $shipto_format[6];
                $data[$constants->ship_to_data][$constants->address_data][$constants->province_code_acc]
                    = $shipto_format[7];
                $data[$constants->ship_to_data][$constants->address_data][$constants->postal_code_shipment]
                    = $shipto_format[8];
                $data[$constants->ship_to_data][$constants->address_data][$constants->country_code_shipment]
                    = $shipto_format[9];
                $data[$constants->ship_to_data]["Email"] = $shipto_format[10];
                $data[$constants->ship_from_data][$constants->name_data] = $ship_from[0];
                $data[$constants->ship_from_data][$constants->attention_name] = $ship_from[0];
                $data[$constants->ship_from_data]['company'] = $ship_from[10];
                $data[$constants->ship_from_data][$constants->phone_data][$constants->number_data]
                    = $ship_from[3];
                $data[$constants->ship_from_data][$constants->address_data][$constants->address_line]
                    = [$ship_from[4], $ship_from[5], $ship_from[6]];
                $data[$constants->ship_from_data][$constants->address_data][$constants->city_data]
                    = $ship_from[7];
                $data[$constants->ship_from_data][$constants->address_data][$constants->province_code_acc]
                    = $ship_from[1];
                $data[$constants->ship_from_data][$constants->address_data][$constants->postal_code_shipment]
                    = $ship_from[8];
                $data[$constants->ship_from_data][$constants->address_data][$constants->country_code_shipment]
                    = $ship_from[9];
                $data[$constants->service_data][$constants->code_data] = $shipping_type[1];
                $data[$constants->service_data][$constants->description_data] = $shipping_type[3];
                $data[$constants->payment_infor][$constants->shipment_charge][$constants->type_data] = '01';
                $data[$constants->payment_infor][$constants->shipment_charge][$constants->bill_shipper][$constants->account_number] = $ship_from[2];
                if ($shipping_type['0'] == 'AP') {
                    $address_line = str_replace($constants->address_replace, ' ', $ship_to[3]);
                    $data[$constants->alternate_delivery_address][$constants->name_data]
                        = str_replace($constants->special_remove, '&', $ship_to[0]);
                    $data[$constants->alternate_delivery_address][$constants->attention_name]
                        = str_replace($constants->special_remove, '&', $ship_to[0]);
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->address_line] = substr($address_line, 0, 35);
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->city_data] = $ship_to[6];
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->province_code_acc] = $ship_to[1];
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->postal_code_shipment] = $ship_to[7];
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->country_code_shipment] = $checkCountry;
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->access_id] = $ship_to[10];
                }
                if (is_array($accessorial_service) && !empty($accessorial_service)) {
                    foreach ($accessorial_service as $key4 => $value) {
                        $data[$constants->accessorials_data][$key4] = [];
                    }
                }
                if ($cod == 1) {
                    $data[$constants->alternate_delivery_address]['COD'] = '1';
                    if ($shipping_type['0'] == 'AP') {
                        $data[$constants->accessorials_data][$constants->ups_access_point_cod] = [];
                    } else {
                        $data[$constants->accessorials_data][$constants->ups_home_cod] = [];
                    }
                }
                foreach ($package as $key5 => $value) {
                    if (is_numeric($value)) {
                        $list_package_default
                            = $this->model_extension_upsmodule_packagedimension->getListPackageSelected($value);
                        $package_api[] = $list_package_default;
                    } else {
                        $package_api[] = $value;
                    }
                }
                foreach ($package_api as $key6 => $value) {
                    $check_code_unit_weight = $value[$constants->unit_weight];
                    $check_name_unit_weight = '';
                    if ($check_code_unit_weight == 'kgs') {
                        $check_name_unit_weight = $constants->kilograms;
                    } else {
                        $check_name_unit_weight = $constants->pounds;
                    }
                    $check_code_unit = isset($value[$constants->unit_dimension]) ? $value[$constants->unit_dimension] : '';
                    $check_name_unit = '';
                    if ($check_code_unit == 'cm') {
                        $check_name_unit = $constants->centimeter;
                    } else {
                        $check_code_unit = 'in';
                        $check_name_unit = $constants->inches;
                    }

                    if (isset($value[$constants->length]) && $value[$constants->length] != '') {
                        $data[$constants->package_data][] = [
                            $constants->dimensions => [
                                $constants->unit_of_measurement => [
                                    $constants->code_data => $check_code_unit,
                                    $constants->description_data => $check_name_unit
                                ],
                                $constants->length_data => $value[$constants->length],
                                $constants->width_data => $value[$constants->width],
                                $constants->height_data => $value[$constants->height]
                            ],
                            $constants->package_weight => [
                                $constants->unit_of_measurement => [
                                    $constants->code_data => $check_code_unit_weight,
                                    $constants->description_data => $check_name_unit_weight
                                ],
                                $constants->weight_data => $value[$constants->weight]
                            ],
                            "Packaging" => [
                                $constants->code_data => "02"
                            ],
                            "PackagingType" => [
                                $constants->code_data => "02"
                            ]
                        ];
                    } else {
                        $data[$constants->package_data][] = [

                            $constants->package_weight => [
                                $constants->unit_of_measurement => [
                                    $constants->code_data => $check_code_unit_weight,
                                    $constants->description_data => $check_name_unit_weight
                                ],
                                $constants->weight_data => $value[$constants->weight]
                            ],
                            "Packaging" => [
                                $constants->code_data => "02"
                            ],
                            "PackagingType" => [
                                $constants->code_data => "02"
                            ]
                        ];
                    }
                }
                $data[$constants->ship_rate_option][$constants->ship_negotiated_rate] = "";
                //call api get tracking, shipment number
                $response = $this->callUpsApi($constants->Ship, $data, $license);
                $check = true;
                $message = "";
                $currency_code = "";
                if (
                    isset($response->ShipmentResponse->Response->ResponseStatus->Code)
                    && $response->ShipmentResponse->Response->ResponseStatus->Code == 1
                ) {
                    $ship_data = $response->ShipmentResponse->ShipmentResults;
                    $shipping_fee = $ship_data->ShipmentCharges->TotalCharges->MonetaryValue;
                    $currency_code = $ship_data->ShipmentCharges->TotalCharges->CurrencyCode;
                    if (isset($ship_data->NegotiatedRateCharges)) {
                        $shipping_fee = $ship_data->NegotiatedRateCharges->TotalCharge->MonetaryValue;
                        $currency_code = $ship_data->NegotiatedRateCharges->TotalCharge->CurrencyCode;
                    }
                    $shipment_number = $ship_data->ShipmentIdentificationNumber;
                    if (!empty($accessorial_service)) {
                        $accessorial = json_encode($accessorial_service);
                    } else {
                        $accessorial = json_encode([]);
                    }
                    foreach ($list_order as $key => $value) {
                        foreach ($package_api as $key1 => $value1) {
                            if (isset($ship_data->PackageResults->TrackingNumber)) {
                                $tracking_number = $ship_data->PackageResults->TrackingNumber;
                            } else {
                                $tracking_number = $ship_data->PackageResults[$key1]->TrackingNumber;
                            }
                            if (isset($value1[$constants->length]) && isset($value1[$constants->width]) && isset($value1[$constants->height])) {
                                if ($value1[$constants->unit_dimension] == 'cm') {
                                    $value1[$constants->unit_dimension] = 'cm';
                                } else {
                                    $value1[$constants->unit_dimension] = 'inch';
                                }
                                if ($value1[$constants->unit_weight] == 'kgs') {
                                    $value1[$constants->unit_weight] = 'Kg';
                                } else {
                                    $value1[$constants->unit_weight] = 'Pounds';
                                }
                                $detail_package = $value1[$constants->length] . 'x' . $value1[$constants->width] .
                                    'x' . $value1[$constants->height] . ' ' . $value1[$constants->unit_dimension] . ', ' .
                                    $value1[$constants->weight] . ' ' . $value1[$constants->unit_weight];
                                //save tracking to ups tracking.
                            } else {

                                if ($value1[$constants->unit_weight] == 'kgs') {
                                    $value1[$constants->unit_weight] = 'Kg';
                                } else {
                                    $value1[$constants->unit_weight] = 'Pounds';
                                }
                                $detail_package = '' . 'x' . '' .
                                    'x' . '' . ' ' . '' . ', ' .
                                    $value1[$constants->weight] . ' ' . $value1[$constants->unit_weight];
                            }
                            $this->model_extension_upsmodule_tracking->createTracking(
                                $shipment_number,
                                $tracking_number,
                                $detail_package,
                                $value
                            );
                            $package_api[$key1]['tracking_number'] = $tracking_number;
                        }
                    }
                    $decode_ship_03 = html_entity_decode(html_entity_decode($ship_to[3]));
                    $decode_ship_04 = html_entity_decode(html_entity_decode($ship_to[4]));
                    $decode_ship_05 = html_entity_decode(html_entity_decode($ship_to[5]));
                    if ($shipping_type['0'] == 'ADD' && $check_edit == 1) {
                        //insert shipment to db
                        $shipment_id = $this->model_extension_upsmodule_shipments->createShipment(
                            $shipment_number,
                            $shipping_type[2],
                            $accessorial,
                            $shipping_fee,
                            $cod,
                            $ship_to[0],
                            $ship_to[1],
                            $ship_to[2],
                            $decode_ship_03,
                            $decode_ship_04,
                            $decode_ship_05,
                            $ship_to[6],
                            $ship_to[7],
                            $ship_to[8],
                            $ship_to[9],
                            $order_value
                        );
                    } else {
                        //insert shipment to db
                        $shipment_id = $this->model_extension_upsmodule_shipments->createShipment(
                            $shipment_number,
                            $shipping_type[2],
                            $accessorial,
                            $shipping_fee,
                            $cod,
                            $ship_to[0],
                            $ship_to[1],
                            $ship_to[2],
                            $decode_ship_03,
                            $decode_ship_04,
                            $decode_ship_05,
                            $ship_to[6],
                            $ship_to[7],
                            $checkCountry,
                            $ship_to[9],
                            $order_value,
                            $ship_to[10]
                        );
                    }
                    $this->model_extension_upsmodule_base->saveOptionSetting(
                        "ups_shipping_fee_currency_code",
                        $currency_code
                    );
                    //update status order
                    $order_id_update = implode(',', $list_order);
                    $update_data
                        = $this->model_extension_upsmodule_openorders->updateStatusOrder($order_id_update, $shipment_id);
                    //API Manage
                    $this->transferShipments($ship_from[2], $shipment_number);
                    //E API Manage
                    if (!empty($update_data)) {
                        foreach ($update_data as $items) {
                            //update status order in opencart to shipped (status id is 3)
                            $this->model_extension_upsmodule_opencartsetting->updateOrderOpencartStatus(
                                $items[$constants->order_id_opencart],
                                3
                            );
                            $this->model_extension_upsmodule_opencartsetting->addOrderHistoryOpencart(
                                $items[$constants->order_id_opencart],
                                3
                            );
                        }
                    }
                    $check = true;
                    $message = "";
                } else {
                    $check = false;
                    if (!empty($response)) {
                        $message = $response->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode->Description;
                    } else {
                        $message = '';
                    }
                }
                echo json_encode(['check' => $check, 'message' => $message]);
            } catch (Exception $message) {
                $check = false;
                echo json_encode(['check' => $check, 'message' => $message]);
            }
        } else {
            http_response_code(403);
            $this->response->redirect($this->url->link($constants->link_opc_error, $constants->token));
        }
    }

    public function createShipmentForced($order_data)
    {
        if (!empty($order_data)) {
            $data_post = $this->makeShipInfo($order_data);
            if (!empty($data_post)) {
                $this->load->model($this->_base_model);
                //set secure header
                $this->model_extension_upsmodule_base->setHeaderSecure();
                //get constant
                $constants = $this->model_extension_upsmodule_base->listConstanst();

                $this->load->model('localisation/country');
                $this->load->model($constants->link_shipments);
                $this->load->model($constants->link_api_model);
                $this->load->model($constants->link_packagedimension);
                $this->load->model('extension/upsmodule/tracking');
                $this->load->model($constants->link_openorders);
                $this->load->model($constants->link_opencartsetting);
                $license = $this->model_extension_upsmodule_apiModel->getLicense();

                $check_name_country = $data_post[$constants->order_selected];
                $get_country_name = $check_name_country[$constants->shipping_country_shipment];
                $get_list_country = $this->model_extension_upsmodule_shipments->getListCountry();
                foreach ($get_list_country as $value) {
                    $code_country = $value['name'];
                    if ($get_country_name == $code_country) {
                        $checkCountry = $value['iso_code_2'];
                    }
                }
                $ship_from = $data_post[$constants->ship_from];
                $check_phone_from = html_entity_decode($ship_from[3]);
                $ship_from[3] = preg_replace($constants->special_phone, '', $check_phone_from);
                $check_postal_from = html_entity_decode($ship_from[8]);
                $ship_from[8]
                    = str_replace('amp', '', preg_replace($constants->special_posttal_code, '', $check_postal_from));
                $ship_to = $data_post['ship_to'];
                $check_phone_to = html_entity_decode($ship_to[2]);
                $ship_to[2] = preg_replace($constants->special_phone, '', $check_phone_to);
                $check_postal_to = html_entity_decode($ship_to[7]);
                $ship_to[7] =
                    str_replace('amp', '', preg_replace($constants->special_posttal_code, '', $check_postal_to));
                $shipping_type = $data_post['shipping_type'];
                $package = $data_post['package'];
                if (!empty($data_post[$constants->accessorial_service_shipment])) {
                    $accessorial_service = $data_post[$constants->accessorial_service_shipment];
                } else {
                    $accessorial_service = [];
                }
                $order_selected = $data_post[$constants->order_selected];
                $list_order = $data_post['idorder'];
                $cod = $data_post['cod'];
                $order_value = (float)filter_var(
                    $data_post[$constants->order_value],
                    FILTER_SANITIZE_NUMBER_FLOAT,
                    FILTER_FLAG_ALLOW_FRACTION
                );
                $check_edit = $data_post['edit_shipment'];
                $shipto_format = [];
                if ($shipping_type['0'] == 'ADD' && $check_edit == 1) {
                    $shipto_format[] = $ship_to[0];
                    $shipto_format[] = $ship_to[0];
                    $shipto_format[] = $ship_to[2];
                    $shipto_format[] = substr($ship_to[3], 0, 35); //address 1
                    $shipto_format[] = substr($ship_to[4], 0, 35); //address 2
                    $shipto_format[] = substr($ship_to[5], 0, 35); //address 3
                    $shipto_format[] = $ship_to[6];
                    $shipto_format[] = $ship_to[1];
                    $shipto_format[] = implode("", explode(" ", $ship_to[7]));
                    $shipto_format[] = $ship_to[8]; //$checkCountry; //country code
                    $shipto_format[] = $ship_to[9];
                } else {
                    $name = $order_selected[$constants->first_name] . ' ' . $order_selected[$constants->last_name];
                    $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByZoneId(
                        $order_selected[$constants->shipping_zone_id]
                    );
                    $country_data = $this->model_localisation_country->getCountry(
                        $order_selected[$constants->shipping_country_id]
                    );
                    $shipto_format[] = $name;
                    $shipto_format[] = $name;
                    $shipto_format[] = $order_selected[$constants->telephone];
                    $shipto_format[] = substr($order_selected[$constants->shipping_address1], 0, 35);
                    $shipto_format[] = substr($order_selected[$constants->shipping_address2], 0, 35);
                    $shipto_format[] = '';
                    $shipto_format[] = $order_selected[$constants->ship_city];
                    $shipto_format[] = (!empty($state_data)) ? $state_data['code'] : '';
                    $shipto_format[] = implode("", explode(" ", $order_selected[$constants->shipping_posttal_code]));
                    $shipto_format[] = (!empty($country_data)) ? $country_data[$constants->iso_code_2_shipment] : 'PL';
                    $shipto_format[] = $order_selected[$constants->email_data];
                    $shipto_format[] = $order_selected[$constants->access_id];
                }
                $data = [];
                $data[$constants->shipping_type] = $shipping_type[0];
                $data[$constants->currency_code_data] = $order_selected[$constants->currency_code];
                $data[$constants->monetary_value] = $order_value;
                $data[$constants->shipper][$constants->name_data] = $ship_from[0];
                $data[$constants->shipper][$constants->attention_name] = $ship_from[0];
                $data[$constants->shipper][$constants->shipper_number] = $ship_from[2];
                $data[$constants->shipper][$constants->phone_data][$constants->number_data] = $ship_from[3];
                $data[$constants->shipper][$constants->address_data][$constants->address_line]
                    = [$ship_from[4], $ship_from[5], $ship_from[6]];
                $data[$constants->shipper][$constants->address_data][$constants->city_data] = $ship_from[7];
                $data[$constants->shipper][$constants->address_data][$constants->province_code_acc] = $ship_from[1];
                $data[$constants->shipper][$constants->address_data][$constants->postal_code_shipment] = $ship_from[8];
                $data[$constants->shipper][$constants->address_data][$constants->country_code_shipment]
                    = $ship_from[9];
                $data[$constants->ship_to_data][$constants->name_data] = $shipto_format[0];
                $data[$constants->ship_to_data][$constants->attention_name] = $shipto_format[1];
                $data[$constants->ship_to_data][$constants->phone_data][$constants->number_data]
                    = $shipto_format[2];
                $data[$constants->ship_to_data][$constants->address_data][$constants->address_line]
                    = [$shipto_format[3], $shipto_format[4], $shipto_format[5]];
                $data[$constants->ship_to_data][$constants->address_data][$constants->city_data]
                    = $shipto_format[6];
                $data[$constants->ship_to_data][$constants->address_data][$constants->province_code_acc]
                    = $shipto_format[7];
                $data[$constants->ship_to_data][$constants->address_data][$constants->postal_code_shipment]
                    = $shipto_format[8];
                $data[$constants->ship_to_data][$constants->address_data][$constants->country_code_shipment]
                    = $shipto_format[9];
                $data[$constants->ship_to_data]["Email"] = $shipto_format[10];
                $data[$constants->ship_from_data][$constants->name_data] = $ship_from[0];
                $data[$constants->ship_from_data][$constants->attention_name] = $ship_from[0];
                $data[$constants->ship_from_data]['company'] = $ship_from[10];
                $data[$constants->ship_from_data][$constants->phone_data][$constants->number_data]
                    = $ship_from[3];
                $data[$constants->ship_from_data][$constants->address_data][$constants->address_line]
                    = [$ship_from[4], $ship_from[5], $ship_from[6]];
                $data[$constants->ship_from_data][$constants->address_data][$constants->city_data]
                    = $ship_from[7];
                $data[$constants->ship_from_data][$constants->address_data][$constants->province_code_acc]
                    = $ship_from[1];
                $data[$constants->ship_from_data][$constants->address_data][$constants->postal_code_shipment]
                    = $ship_from[8];
                $data[$constants->ship_from_data][$constants->address_data][$constants->country_code_shipment]
                    = $ship_from[9];
                $data[$constants->service_data][$constants->code_data] = $shipping_type[1];
                $data[$constants->service_data][$constants->description_data] = $shipping_type[3];
                $data[$constants->payment_infor][$constants->shipment_charge][$constants->type_data] = '01';
                $data[$constants->payment_infor][$constants->shipment_charge][$constants->bill_shipper][$constants->account_number] = $ship_from[2];
                if ($shipping_type['0'] == 'AP') {
                    $address_line = str_replace($constants->address_replace, ' ', $ship_to[3]);
                    $data[$constants->alternate_delivery_address][$constants->name_data]
                        = str_replace($constants->special_remove, '&', $ship_to[0]);
                    $data[$constants->alternate_delivery_address][$constants->attention_name]
                        = str_replace($constants->special_remove, '&', $ship_to[0]);
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->address_line] = substr($address_line, 0, 35);
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->city_data] = $ship_to[6];
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->province_code_acc] = $ship_to[1];
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->postal_code_shipment] = $ship_to[7];
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->country_code_shipment] = $checkCountry;
                    $data[$constants->alternate_delivery_address][$constants->address_data][$constants->access_id] = $ship_to[10];
                }
                if (is_array($accessorial_service) && !empty($accessorial_service)) {
                    foreach ($accessorial_service as $key4 => $value) {
                        $data[$constants->accessorials_data][$key4] = [];
                    }
                }
                if ($cod == 1) {
                    $data[$constants->alternate_delivery_address]['COD'] = '1';
                    if ($shipping_type['0'] == 'AP') {
                        $data[$constants->accessorials_data][$constants->ups_access_point_cod] = [];
                    } else {
                        $data[$constants->accessorials_data][$constants->ups_home_cod] = [];
                    }
                }
                foreach ($package as $key5 => $value) {
                    if (is_numeric($value)) {
                        $list_package_default
                            = $this->model_extension_upsmodule_packagedimension->getListPackageSelected($value);
                        $package_api[] = $list_package_default;
                    } else {
                        $package_api[] = $value;
                    }
                }
                foreach ($package_api as $key6 => $value) {
                    $check_code_unit_weight = $value[$constants->unit_weight];
                    $check_name_unit_weight = '';
                    if ($check_code_unit_weight == 'kgs') {
                        $check_name_unit_weight = $constants->kilograms;
                    } else {
                        $check_name_unit_weight = $constants->pounds;
                    }
                    $check_code_unit = isset($value[$constants->unit_dimension]) ? $value[$constants->unit_dimension] : '';
                    $check_name_unit = '';
                    if ($check_code_unit == 'cm') {
                        $check_name_unit = $constants->centimeter;
                    } else {
                        $check_code_unit = 'in';
                        $check_name_unit = $constants->inches;
                    }
                    if (isset($value[$constants->length]) && isset($value[$constants->width]) && isset($value[$constants->height])) {
                        $data[$constants->package_data][] = [
                            $constants->dimensions => [
                                $constants->unit_of_measurement => [
                                    $constants->code_data => $check_code_unit,
                                    $constants->description_data => $check_name_unit
                                ],
                                $constants->length_data => $value[$constants->length],
                                $constants->width_data => $value[$constants->width],
                                $constants->height_data => $value[$constants->height]
                            ],
                            $constants->package_weight => [
                                $constants->unit_of_measurement => [
                                    $constants->code_data => $check_code_unit_weight,
                                    $constants->description_data => $check_name_unit_weight
                                ],
                                $constants->weight_data => $value[$constants->weight]
                            ],
                            "Packaging" => [
                                $constants->code_data => "02"
                            ],
                            "PackagingType" => [
                                $constants->code_data => "02"
                            ]
                        ];
                    } else {
                        $data[$constants->package_data][] = [
                            $constants->package_weight => [
                                $constants->unit_of_measurement => [
                                    $constants->code_data => $check_code_unit_weight,
                                    $constants->description_data => $check_name_unit_weight
                                ],
                                $constants->weight_data => $value[$constants->weight]
                            ],
                            "Packaging" => [
                                $constants->code_data => "02"
                            ],
                            "PackagingType" => [
                                $constants->code_data => "02"
                            ]
                        ];
                    }
                }
                $data[$constants->ship_rate_option][$constants->ship_negotiated_rate] = "";
                //call api get tracking, shipment number
                $response = $this->callUpsApi($constants->Ship, $data, $license);
                $message = "";
                $currency_code = "";
                if (
                    isset($response->ShipmentResponse->Response->ResponseStatus->Code)
                    && $response->ShipmentResponse->Response->ResponseStatus->Code == 1
                ) {
                    $ship_data = $response->ShipmentResponse->ShipmentResults;
                    $shipping_fee = $ship_data->ShipmentCharges->TotalCharges->MonetaryValue;
                    $currency_code = $ship_data->ShipmentCharges->TotalCharges->CurrencyCode;
                    if (isset($ship_data->NegotiatedRateCharges)) {
                        $shipping_fee = $ship_data->NegotiatedRateCharges->TotalCharge->MonetaryValue;
                        $currency_code = $ship_data->NegotiatedRateCharges->TotalCharge->CurrencyCode;
                    }
                    $shipment_number = $ship_data->ShipmentIdentificationNumber;
                    if (!empty($accessorial_service)) {
                        $accessorial = json_encode($accessorial_service);
                    } else {
                        $accessorial = json_encode([]);
                    }
                    foreach ($list_order as $key => $value) {
                        foreach ($package_api as $key1 => $value1) {
                            if (isset($ship_data->PackageResults->TrackingNumber)) {
                                $tracking_number = $ship_data->PackageResults->TrackingNumber;
                            } else {
                                $tracking_number = $ship_data->PackageResults[$key1]->TrackingNumber;
                            }
                            if(isset($value1[$constants->length]) && isset($value1[$constants->width]) && isset($value1[$constants->height])){
                            if ($value1[$constants->unit_dimension] == 'cm') {
                                $value1[$constants->unit_dimension] = 'cm';
                            } else {
                                $value1[$constants->unit_dimension] = 'inch';
                            }
                            if ($value1[$constants->unit_weight] == 'kgs') {
                                $value1[$constants->unit_weight] = 'Kg';
                            } else {
                                $value1[$constants->unit_weight] = 'Pounds';
                            }
                            $detail_package = $value1[$constants->length] . 'x' . $value1[$constants->width] .
                                'x' . $value1[$constants->height] . ' ' . $value1[$constants->unit_dimension] . ', ' .
                                $value1[$constants->weight] . ' ' . $value1[$constants->unit_weight];
                        }else{
                            
                            if ($value1[$constants->unit_weight] == 'kgs') {
                                $value1[$constants->unit_weight] = 'Kg';
                            } else {
                                $value1[$constants->unit_weight] = 'Pounds';
                            }
                            $detail_package = '' . 'x' . '' .
                                'x' . '' . ' ' . '' . ', ' .
                                $value1[$constants->weight] . ' ' . $value1[$constants->unit_weight];
                        }
                            //save tracking to ups tracking.
                            $this->model_extension_upsmodule_tracking->createTracking(
                                $shipment_number,
                                $tracking_number,
                                $detail_package,
                                $value
                            );
                            $package_api[$key1]['tracking_number'] = $tracking_number;
                        }
                    }
                    $decode_ship_03 = html_entity_decode(html_entity_decode($ship_to[3]));
                    $decode_ship_04 = html_entity_decode(html_entity_decode($ship_to[4]));
                    $decode_ship_05 = html_entity_decode(html_entity_decode($ship_to[5]));
                    if ($shipping_type['0'] == 'ADD' && $check_edit == 1) {
                        //insert shipment to db
                        $shipment_id = $this->model_extension_upsmodule_shipments->createShipment(
                            $shipment_number,
                            $shipping_type[2],
                            $accessorial,
                            $shipping_fee,
                            $cod,
                            $ship_to[0],
                            $ship_to[1],
                            $ship_to[2],
                            $decode_ship_03,
                            $decode_ship_04,
                            $decode_ship_05,
                            $ship_to[6],
                            $ship_to[7],
                            $ship_to[8],
                            $ship_to[9],
                            $order_value
                        );
                    } else {
                        //insert shipment to db
                        $shipment_id = $this->model_extension_upsmodule_shipments->createShipment(
                            $shipment_number,
                            $shipping_type[2],
                            $accessorial,
                            $shipping_fee,
                            $cod,
                            $ship_to[0],
                            $ship_to[1],
                            $ship_to[2],
                            $decode_ship_03,
                            $decode_ship_04,
                            $decode_ship_05,
                            $ship_to[6],
                            $ship_to[7],
                            $checkCountry,
                            $ship_to[9],
                            $order_value,
                            $ship_to[10]
                        );
                    }
                    $this->model_extension_upsmodule_base->saveOptionSetting(
                        "ups_shipping_fee_currency_code",
                        $currency_code
                    );
                    //update status order
                    $order_id_update = implode(',', $list_order);
                    $update_data
                        = $this->model_extension_upsmodule_openorders->updateStatusOrderoid($order_id_update, $shipment_id);
                    //API Manage
                    $this->transferShipments($ship_from[2], $shipment_number);
                    //E API Manage
                    if (!empty($update_data)) {
                        foreach ($update_data as $items) {
                            //update status order in opencart to shipped (status id is 3)
                            $this->model_extension_upsmodule_opencartsetting->updateOrderOpencartStatus(
                                $items[$constants->order_id_opencart],
                                3
                            );
                            $this->model_extension_upsmodule_opencartsetting->addOrderHistoryOpencart(
                                $items[$constants->order_id_opencart],
                                3
                            );
                        }
                    }
                    $message = "success";
                } else {
                    if (!empty($response)) {
                        $message = $response->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode->Description;
                    } else {
                        $message = '';
                    }
                }
                return array("status" => $message);
            }
        } else {
            return array("status" => "Order data not found.");
        }
    }

    public function makeShipInfo($order_data)
    {
        $datatoship = [];
        if (!empty($order_data)) {
            $this->load->model("extension/upsmodule/account");
            $this->load->model("extension/upsmodule/opencartsetting");
            $constants = $this->model_extension_upsmodule_base->listConstanst();
            $defAcc = $this->model_extension_upsmodule_account->getAccountDefault();

            //Fetch additional data required
            $order_data['total_all_decimal'] = $this->currency->format($order_data['total'], $order_data['currency_code'], '', true);
            $product_name = explode(',', $order_data[$constants->list_product_name]);
            $order_data[$constants->product_name] = implode($constants->drop_down, $product_name);
            if ($order_data[$constants->service_symbol_orders] == '&trade;') {
                $order_data[$constants->service_name_info_orders] = 'UPS Access Point&trade; Economy';
            } else {
                $order_data[$constants->service_name_info_orders] = $order_data[$constants->service_name] .
                    $order_data[$constants->service_symbol_orders];
            }
            $order_data['order_value'] = $order_data[$constants->total_all];
            $order_data[$constants->check_shipment] = '';
            $address_format_1 = [
                $order_data[$constants->shipping_address_1_orders],
                $order_data[$constants->shipping_address_2_orders],
                $order_data[$constants->shipping_city],
                $order_data[$constants->shipping_zone_orders],
                $order_data[$constants->shipping_post_code],
                $order_data[$constants->shipping_country]
            ];
            $address_format = $this->addArray($address_format_1);
            $order_data['address'] = implode($constants->drop_down, $address_format);
            $order_data['ap_address_all'] = "";
            $state_data = (isset($order_data['shipping_zone_id']) && !empty($order_data['shipping_zone_id'])) ? $this->model_extension_upsmodule_opencartsetting->getStateByZoneId($order_data['shipping_zone_id']) : '';
            $order_data[$constants->state_data] = isset($state_data['code']) ? $state_data['code'] : "";
            $order_data[$constants->state_name_orders] = $order_data[$constants->shipping_zone_orders];
            $order_data["address_ap"] = "";
            $add_address_all = $this->addArray(
                [
                    $order_data[$constants->shipping_address_1_orders],
                    $order_data[$constants->shipping_address_2_orders]
                ]
            );
            $order_data['add_address_all'] = implode("<br />", $add_address_all);
            $order_data["accessorial_array"] = [];

            $datatoship[$constants->ship_from][] = isset($defAcc['ups_account_name']) ? $defAcc['ups_account_name'] : '';
            $datatoship[$constants->ship_from][] = (isset($defAcc['state_province_code']) && !empty($defAcc['state_province_code'])) ? $defAcc['state_province_code'] : 'XX';
            $datatoship[$constants->ship_from][] = isset($defAcc['ups_account_number']) ? $defAcc['ups_account_number'] : '';
            $datatoship[$constants->ship_from][] = isset($defAcc['phone_number']) ? $defAcc['phone_number'] : '';
            $datatoship[$constants->ship_from][] = isset($defAcc['address_1']) ? $defAcc['address_1'] : '';
            $datatoship[$constants->ship_from][] = isset($defAcc['address_2']) ? $defAcc['address_2'] : '';
            $datatoship[$constants->ship_from][] = isset($defAcc['address_3']) ? $defAcc['address_3'] : '';
            $datatoship[$constants->ship_from][] = isset($defAcc['city']) ? $defAcc['city'] : '';
            $datatoship[$constants->ship_from][] = isset($defAcc['post_code']) ? $defAcc['post_code'] : '';
            $datatoship[$constants->ship_from][] = isset($defAcc['country']) ? $defAcc['country'] : '';
            $datatoship[$constants->ship_from][] = isset($defAcc['company']) ? $defAcc['company'] : '';
            $datatoship["ship_to"][] = (isset($order_data['shipping_firstname']) && isset($order_data['shipping_lastname'])) ? $order_data['shipping_firstname'] . ' ' . $order_data['shipping_lastname'] : '';
            $datatoship["ship_to"][] = isset($order_data['state']) ? $order_data['state'] : '';
            $datatoship["ship_to"][] = isset($order_data['telephone']) ? $order_data['telephone'] : '';
            $datatoship["ship_to"][] = isset($order_data['shipping_address_1']) ? $order_data['shipping_address_1'] : '';
            $datatoship["ship_to"][] = isset($order_data['shipping_address_2']) ? $order_data['shipping_address_2'] : '';
            $datatoship["ship_to"][] = isset($order_data['shipping_address_3']) ? $order_data['shipping_address_3'] : '';
            $datatoship["ship_to"][] = isset($order_data['shipping_city']) ? $order_data['shipping_city'] : '';
            $datatoship["ship_to"][] = isset($order_data['shipping_postcode']) ? $order_data['shipping_postcode'] : '';
            $datatoship["ship_to"][] = isset($order_data['country_code']) ? $order_data['country_code'] : '';
            $datatoship["ship_to"][] = isset($order_data['email']) ? $order_data['email'] : '';
            $datatoship["ship_to"][] = isset($order_data['access_point_id']) ? $order_data['access_point_id'] : '';
            $datatoship["shipping_type"][] = 'ADD';
            $datatoship["shipping_type"][] = isset($order_data['rate_code']) ? $order_data['rate_code'] : '';
            $datatoship["shipping_type"][] = isset($order_data['idservice']) ? $order_data['idservice'] : '';
            $datatoship["shipping_type"][] = isset($order_data['service_name']) ? $order_data['service_name'] : '';
            $datatoship["package"] = isset($order_data['package']) ? json_decode($order_data['package'], true) : '';
            $datatoship["idorder"][] = isset($order_data['order_id_opencart']) ? $order_data['order_id_opencart'] : '';
            $datatoship["cod"] = isset($order_data['cod']) ? $order_data['cod'] : '';
            $datatoship[$constants->order_value][] = isset($order_data['order_value']) ? $order_data['order_value'] : '';
            $datatoship[$constants->order_selected]['id'] = isset($order_data['id']) ? $order_data['id'] : '';
            $datatoship[$constants->order_selected]['order_id_opencart'] = isset($order_data['order_id_opencart']) ? $order_data['order_id_opencart'] : '';
            $datatoship["order_selected"]['shipping_service'] = isset($order_data['shipping_service']) ? $order_data['shipping_service'] : '';
            $datatoship[$constants->order_selected]['accessorial_service'] = isset($order_data['accessorial_service']) ? $order_data['accessorial_service'] : '';
            $datatoship["order_selected"]['package'] = isset($order_data['package']) ? $order_data['package'] : '';
            $datatoship["order_selected"]['shipment_id'] = isset($order_data['shipment_id']) ? $order_data['shipment_id'] : '';
            $datatoship["order_selected"]['quote_id'] = isset($order_data['quote_id']) ? $order_data['quote_id'] : '';
            $datatoship["order_selected"]['status'] = isset($order_data['status']) ? $order_data['status'] : '';
            $datatoship[$constants->order_selected]['ap_name'] = isset($order_data['ap_name']) ? $order_data['ap_name'] : '';
            $datatoship[$constants->order_selected]['ap_address1'] = isset($order_data['ap_address1']) ? $order_data['ap_address1'] : '';
            $datatoship[$constants->order_selected]['ap_address2'] = isset($order_data['ap_address2']) ? $order_data['ap_address2'] : '';
            $datatoship[$constants->order_selected]['ap_address3'] = isset($order_data['ap_address3']) ? $order_data['ap_address3'] : '';
            $datatoship[$constants->order_selected]['ap_state'] = isset($order_data['ap_state']) ? $order_data['ap_state'] : '';
            $datatoship[$constants->order_selected]['ap_postcode'] = isset($order_data['ap_postcode']) ? $order_data['ap_postcode'] : '';
            $datatoship[$constants->order_selected]['ap_city'] = isset($order_data['ap_city']) ? $order_data['ap_city'] : '';
            $datatoship[$constants->order_selected]['ap_country'] = isset($order_data['ap_country']) ? $order_data['ap_country'] : '';
            $datatoship["order_selected"]['location_id'] = isset($order_data['location_id']) ? $order_data['location_id'] : '';
            $datatoship[$constants->order_selected]['access_point_id'] = isset($order_data['access_point_id']) ? $order_data['access_point_id'] : '';
            $datatoship["order_selected"]['cod'] = isset($order_data['cod']) ? $order_data['cod'] : '';
            $datatoship["order_selected"]['archive_orders'] = isset($order_data['archive_orders']) ? $order_data['archive_orders'] : '';
            $datatoship["order_selected"]['date_created'] = isset($order_data['date_created']) ? $order_data['date_created'] : '';
            $datatoship[$constants->order_selected]['order_id'] = isset($order_data['order_id']) ? $order_data['order_id'] : '';
            $datatoship[$constants->order_selected]['firstname'] = isset($order_data['firstname']) ? $order_data['firstname'] : '';
            $datatoship[$constants->order_selected]['lastname'] = isset($order_data['lastname']) ? $order_data['lastname'] : '';
            $datatoship[$constants->order_selected]['email'] = isset($order_data['email']) ? $order_data['email'] : '';
            $datatoship[$constants->order_selected]['telephone'] = isset($order_data['telephone']) ? $order_data['telephone'] : '';
            $datatoship["order_selected"]['shipping_company'] = isset($order_data['shipping_company']) ? $order_data['shipping_company'] : '';
            $datatoship[$constants->order_selected]['shipping_address_1'] = isset($order_data['shipping_address_1']) ? $order_data['shipping_address_1'] : '';
            $datatoship[$constants->order_selected]['shipping_address_2'] = isset($order_data['shipping_address_2']) ? $order_data['shipping_address_2'] : '';
            $datatoship[$constants->order_selected]['shipping_city'] = isset($order_data['shipping_city']) ? $order_data['shipping_city'] : '';
            $datatoship[$constants->order_selected]['shipping_postcode'] = isset($order_data['shipping_postcode']) ? $order_data['shipping_postcode'] : '';
            $datatoship[$constants->order_selected]['shipping_country'] = isset($order_data['shipping_country']) ? $order_data['shipping_country'] : '';
            $datatoship[$constants->order_selected]['shipping_country_id'] = isset($order_data['shipping_country_id']) ? $order_data['shipping_country_id'] : '';
            $datatoship[$constants->order_selected]['shipping_zone'] = isset($order_data['shipping_zone']) ? $order_data['shipping_zone'] : '';
            $datatoship[$constants->order_selected]['shipping_zone_id'] = isset($order_data['shipping_zone_id']) ? $order_data['shipping_zone_id'] : '';
            $datatoship[$constants->order_selected]['date_added'] = isset($order_data['date_added']) ? $order_data['date_added'] : '';
            $datatoship["order_selected"]['shipping_method'] = isset($order_data['shipping_method']) ? $order_data['shipping_method'] : '';
            $datatoship[$constants->order_selected]['payment_code'] = isset($order_data['payment_code']) ? $order_data['payment_code'] : '';
            $datatoship[$constants->order_selected]['currency_code'] = isset($order_data['currency_code']) ? $order_data['currency_code'] : '';
            $datatoship[$constants->order_selected]['total'] = isset($order_data['total']) ? $order_data['total'] : '';
            $datatoship["order_selected"]['currency_value'] = isset($order_data['currency_value']) ? $order_data['currency_value'] : '';
            $datatoship["order_selected"]['totalProduct'] = isset($order_data['totalProduct']) ? $order_data['totalProduct'] : '';
            $datatoship[$constants->order_selected]['listProduct'] = isset($order_data['listProduct']) ? $order_data['listProduct'] : '';
            $datatoship[$constants->order_selected]['service_name'] = isset($order_data['service_name']) ? $order_data['service_name'] : '';
            $datatoship[$constants->order_selected]['service_symbol'] = isset($order_data['service_symbol']) ? $order_data['service_symbol'] : '';
            $datatoship[$constants->order_selected]['service_type'] = isset($order_data['service_type']) ? $order_data['service_type'] : '';
            $datatoship[$constants->order_selected]['country_code'] = isset($order_data['country_code']) ? $order_data['country_code'] : '';
            $datatoship["order_selected"]['rate_code'] = isset($order_data['rate_code']) ? $order_data['rate_code'] : '';
            $datatoship["order_selected"]['idservice'] = isset($order_data['idservice']) ? $order_data['idservice'] : '';
            $datatoship["order_selected"]['shipping_firstname'] = isset($order_data['shipping_firstname']) ? $order_data['shipping_firstname'] : '';
            $datatoship["order_selected"]['shipping_lastname'] = isset($order_data['shipping_lastname']) ? $order_data['shipping_lastname'] : '';
            $datatoship[$constants->order_selected]['name'] = isset($order_data['name']) ? $order_data['name'] : '';
            $datatoship[$constants->order_selected]['totalAll'] = isset($order_data['totalAll']) ? $order_data['totalAll'] : '';
            $datatoship["order_selected"]['code'] = isset($order_data['code']) ? $order_data['code'] : '';
            $datatoship["order_selected"]['symbol_left'] = isset($order_data['symbol_left']) ? $order_data['symbol_left'] : '';
            $datatoship["order_selected"]['symbol_right'] = isset($order_data['symbol_right']) ? $order_data['symbol_right'] : '';
            $datatoship[$constants->order_selected]['decimal_place'] = isset($order_data['decimal_place']) ? $order_data['decimal_place'] : '';
            $datatoship["order_selected"]['total_all_decimal'] = isset($order_data['total_all_decimal']) ? $order_data['total_all_decimal'] : '';
            $datatoship[$constants->order_selected]['product_name'] = isset($order_data['product_name']) ? $order_data['product_name'] : '';
            $datatoship[$constants->order_selected]['service_name_info'] = isset($order_data['service_name_info']) ? $order_data['service_name_info'] : '';
            $datatoship[$constants->order_selected]['order_value'] = isset($order_data['order_value']) ? $order_data['order_value'] : '';
            $datatoship[$constants->order_selected]['checkshipment'] = isset($order_data['checkshipment']) ? $order_data['checkshipment'] : '';
            $datatoship["order_selected"]['address'] = isset($order_data['address']) ? $order_data['address'] : '';
            $datatoship["order_selected"]['ap_address_all'] = isset($order_data['ap_address_all']) ? $order_data['ap_address_all'] : '';
            $datatoship[$constants->order_selected]['state'] = isset($order_data['state']) ? $order_data['state'] : '';
            $datatoship[$constants->order_selected]['state_name'] = isset($order_data['state_name']) ? $order_data['state_name'] : '';
            $datatoship["order_selected"]['address_ap'] = isset($order_data['address_ap']) ? $order_data['address_ap'] : '';
            $datatoship["order_selected"]['add_address_all'] = isset($order_data['add_address_all']) ? $order_data['add_address_all'] : '';
            $datatoship["edit_shipment"] = 0;
            return $datatoship;
        }
    }



    /**
     * ControllerExtensionUpsmodulePackagedimension createBatch
     *
     * @return null
     */
    public function createBatch()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_openorders);
        $this->load->model($constants->link_shipments);
        $this->load->model('extension/upsmodule/tracking');
        $this->load->model($constants->link_opencartsetting);
        $this->load->model($constants->link_packagedimension);
        $this->load->model($constants->link_api_model);
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        $request = $this->request->post;
        $order_ids = implode(",", $request['list_id_orders']);
        $ship_from = $request[$constants->ship_from];
        $check_phone = html_entity_decode($ship_from[3]);
        $ship_from[3] = preg_replace($constants->special_phone, '', $check_phone);
        $check_postal = html_entity_decode($ship_from[8]);
        $ship_from[8] = str_replace('amp', '', preg_replace($constants->special_posttal_code, '', $check_postal));
        //$package_default = $this->model_extension_upsmodule_packagedimension->getPackageDefault();
        $list_detail_order = $this->model_extension_upsmodule_openorders->getMultiDetailOrder($order_ids);

        if (!empty($list_detail_order)) {
            foreach ($list_detail_order as $keyOrder => $value) {
                $country_code = $this->model_extension_upsmodule_opencartsetting->getCountryCodeById(
                    $value[$constants->shipping_country_id]
                );
                $code_zone = $this->model_extension_upsmodule_opencartsetting->getCodeZone(
                    $value[$constants->shipping_zone_id]
                );
                // get package default
                $package_default = json_decode($value['package'], true);
                $data_request_ship = [];
                $data_request_ship = [
                    $constants->shipping_type => $value[$constants->service_type_shipment],
                    $constants->currency_code_data => $value[$constants->currency_code],
                    $constants->monetary_value => strval(number_format($value[$constants->total_shipment], 2)),
                    $constants->shipper => [
                        $constants->name_data => (isset($ship_from[0]) ? $ship_from[0] : ''),
                        $constants->attention_name => (isset($ship_from[0]) ? $ship_from[0] : ''),
                        $constants->shipper_number => (isset($ship_from[2]) ? $ship_from[2] : ''),
                        $constants->phone_data => [
                            $constants->number_data => (isset($ship_from[3]) ? $ship_from[3] : '')
                        ],
                        $constants->address_data => [
                            $constants->address_line => [
                                (isset($ship_from[4]) ? $ship_from[4] : ''),
                                (isset($ship_from[5]) ? $ship_from[5] : ''),
                                (isset($ship_from[6]) ? $ship_from[6] : '')
                            ],
                            $constants->city_data => (isset($ship_from[7]) ? $ship_from[7] : ''),
                            $constants->province_code_acc => $ship_from[1],
                            $constants->postal_code_shipment =>
                            implode("", explode(" ", (isset($ship_from[8]) ? $ship_from[8] : ''))),
                            $constants->country_code_shipment => (isset($ship_from[9]) ? $ship_from[9] : '')
                        ]
                    ],
                    $constants->ship_to_data => [
                        $constants->name_data =>
                        $value[$constants->first_name] . ' ' . $value[$constants->last_name],
                        $constants->attention_name =>
                        $value[$constants->first_name] . ' ' . $value[$constants->last_name],
                        $constants->phone_data => [
                            $constants->number_data => $value[$constants->telephone],
                        ],
                        $constants->address_data => [
                            $constants->address_line => [
                                $value[$constants->shipping_address1],
                                $value[$constants->shipping_address2],
                                ""
                            ],
                            $constants->city_data => $value[$constants->ship_city],
                            $constants->province_code_acc => $code_zone['code'],
                            $constants->postal_code =>
                            implode("", explode(" ", $value[$constants->shipping_posttal_code])),
                            $constants->country_code_shipment => $country_code[$constants->iso_code_2],
                        ],
                        "access_point_id" => $value[$constants->access_point_id],
                        "Email" => $value[$constants->email_data],
                    ],
                    $constants->ship_from_data => [
                        $constants->name_data => (isset($ship_from[0]) ? $ship_from[0] : ''),
                        $constants->company => (isset($ship_from[10]) ? $ship_from[10] : ''),
                        $constants->shipper_number => (isset($ship_from[0]) ? $ship_from[0] : ''),
                        $constants->phone_data => [
                            $constants->number_data => (isset($ship_from[3]) ? $ship_from[3] : '')
                        ],
                        $constants->address_data => [
                            $constants->address_line => [
                                (isset($ship_from[4]) ? $ship_from[4] : ''),
                                (isset($ship_from[5]) ? $ship_from[5] : ''),
                                (isset($ship_from[6]) ? $ship_from[6] : '')
                            ],
                            $constants->city_data => (isset($ship_from[7]) ? $ship_from[7] : ''),
                            $constants->province_code_acc => $ship_from[1],
                            $constants->postal_code_shipment =>
                            implode("", explode(" ", (isset($ship_from[8]) ? $ship_from[8] : ''))),
                            $constants->country_code_shipment => (isset($ship_from[9]) ? $ship_from[9] : '')
                        ]
                    ],
                    $constants->service_data => [
                        $constants->code_data => $value['rate_code'],
                        $constants->description_data => $value[$constants->service_name_shipment]
                    ],
                    $constants->payment_infor => [
                        $constants->shipment_charge => [
                            $constants->type_data => "01",
                            $constants->bill_shipper => [
                                $constants->account_number => (isset($ship_from[2]) ? $ship_from[2] : '')
                            ]
                        ]
                    ],
                    $constants->ship_rate_option => [
                        $constants->ship_negotiated_rate => ""
                    ],
                ];
                if ($value[$constants->service_type] == 'AP') {
                    $address_line_batch = str_replace($constants->address_replace, ' ', $value['ap_address1']);
                    $data_request_ship[$constants->alternate_delivery_address] = [
                        $constants->name_data => $value[$constants->ap_name],
                        $constants->attention_name => $value[$constants->ap_name],
                        $constants->address_data => [
                            $constants->address_line => substr($address_line_batch, 0, 35),
                            $constants->city_data => $value['ap_city'],
                            $constants->province_code_acc => $value['ap_state'],
                            $constants->postal_code => implode("", explode("-", $value['ap_postcode'])),
                            $constants->country_code_shipment => $value['ap_country']
                        ]
                    ];
                }
                $check_code_unit_weight = $package_default[0][$constants->unit_weight];
                $check_name_unit_weight = '';
                if ($check_code_unit_weight == 'kgs') {
                    $check_name_unit_weight = $constants->kilograms;
                } else {
                    $check_name_unit_weight = $constants->pounds;
                }
                $check_code_unit = $package_default[0][$constants->unit_dimension];
                $check_name_unit = '';
                if ($check_code_unit == 'cm') {
                    $check_name_unit = $constants->centimeter;
                } else {
                    $check_code_unit = 'in';
                    $check_name_unit = $constants->inches;
                }
                if (!empty($package_default)) {
                    foreach ($package_default as $package_value) {
                        $data_request_ship[$constants->package_data][] = [
                            $constants->dimensions => [
                                $constants->unit_of_measurement => [
                                    $constants->code_data => $check_code_unit,
                                    $constants->description_data => $check_name_unit
                                ],
                                $constants->length_data => strval($package_value[$constants->length]),
                                $constants->width_data => strval($package_value[$constants->width]),
                                $constants->height_data => strval($package_value[$constants->height])
                            ],
                            $constants->package_weight => [
                                $constants->unit_of_measurement => [
                                    $constants->code_data => $check_code_unit_weight,
                                    $constants->description_data => $check_name_unit_weight
                                ],
                                $constants->weight_data => strval($package_value[$constants->weight])
                            ],
                            "Packaging" => [
                                $constants->code_data => "02"
                            ],
                            "PackagingType" => [
                                $constants->code_data => "02"
                            ]
                        ];
                    }
                } else {
                    $data_request_ship[$constants->package_data][] = [
                        $constants->dimensions => [
                            $constants->unit_of_measurement => [
                                $constants->code_data => 'cm',
                                $constants->description_data => 'cm'
                            ],
                            $constants->length_data => 0,
                            $constants->width_data => 0,
                            $constants->height_data => 0
                        ],
                        $constants->package_weight => [
                            $constants->unit_of_measurement => [
                                $constants->code_data => 'kgs',
                                $constants->description_data => 'kgs'
                            ],
                            $constants->weight_data => 0
                        ],
                        "Packaging" => [
                            $constants->code_data => "02"
                        ],
                        "PackagingType" => [
                            $constants->code_data => "02"
                        ]
                    ];
                }
                $accessorial = [];
                if ($value['payment_code'] == 'cod') {
                    $data_request_ship[$constants->alternate_delivery_address]['COD'] = '1';
                    if ($value[$constants->service_type_shipment] == 'AP') {
                        $data_request_ship[$constants->accessorials_data][$constants->ups_access_point_cod] = [];
                    } else {
                        $data_request_ship[$constants->accessorials_data][$constants->ups_home_cod] = [];
                    }
                } else {
                    if (
                        isset($value[$constants->accessorial_service_shipment])
                        && !empty($value[$constants->accessorial_service_shipment])
                    ) {
                        $accessorial = json_decode($value[$constants->accessorial_service_shipment]);
                        foreach ($accessorial as $key => $value1) {
                            $data_request_ship[$constants->accessorials_data][$key] = [];
                        }
                    }
                }
                $message = "";
                $data_response_ship = $this->callUpsApi($constants->Ship, $data_request_ship, $license);
                $currency_code = "";
                if (
                    isset($data_response_ship->ShipmentResponse->Response->ResponseStatus->Code)
                    && $data_response_ship->ShipmentResponse->Response->ResponseStatus->Code == 1
                ) {
                    $ship_data = $data_response_ship->ShipmentResponse->ShipmentResults;
                    $shipping_fee = $ship_data->ShipmentCharges->TotalCharges->MonetaryValue;
                    $currency_code = $ship_data->ShipmentCharges->TotalCharges->CurrencyCode;
                    if (isset($ship_data->NegotiatedRateCharges)) {
                        $shipping_fee = $ship_data->NegotiatedRateCharges->TotalCharge->MonetaryValue;
                        $currency_code = $ship_data->NegotiatedRateCharges->TotalCharge->CurrencyCode;
                    }
                    $number_shipment = $ship_data->ShipmentIdentificationNumber;
                    $acces = $value[$constants->accessorial_service_shipment];
                    if ($value['payment_code'] == 'cod') {
                        $cod = 1;
                    } else {
                        $cod = 0;
                    }
                    if ($value[$constants->service_type_shipment] == 'AP') {
                        $id_shipment = $this->model_extension_upsmodule_shipments->createShipment(
                            $number_shipment,
                            $value['idservice'],
                            $acces,
                            $shipping_fee,
                            $cod,
                            $value[$constants->ap_name_data],
                            $value['ap_state'],
                            $value[$constants->telephone],
                            $value['ap_address1'],
                            $value['ap_address2'],
                            $value['ap_address3'],
                            $value['ap_city'],
                            $value['ap_postcode'],
                            $country_code[$constants->iso_code_2_shipment],
                            $value[$constants->email],
                            $value['totalAll'],
                            $value[$constants->access_point_id]
                        );
                    } else {
                        $add_name = $value[$constants->first_name] . ' ' . $value[$constants->last_name];
                        $id_shipment = $this->model_extension_upsmodule_shipments->createShipment(
                            $number_shipment,
                            $value['idservice'],
                            $acces,
                            $shipping_fee,
                            $cod,
                            $add_name,
                            $code_zone['code'],
                            $value[$constants->telephone],
                            $value[$constants->shipping_address1],
                            $value[$constants->shipping_address2],
                            '',
                            $value[$constants->ship_city],
                            $value[$constants->shipping_posttal_code],
                            $country_code[$constants->iso_code_2_shipment],
                            $value[$constants->email_data],
                            $value['totalAll']
                        );
                    }
                    $this->model_extension_upsmodule_base->saveOptionSetting(
                        "ups_shipping_fee_currency_code",
                        $currency_code
                    );
                    $detail_package = '0x0x0' . ' ' . 'cm' . ', ' . '0' . ' ' . 'kgs';
                    if (isset($package_default[$keyOrder][$constants->unit_dimension])) {
                        if ($package_default[$keyOrder][$constants->unit_dimension] == 'cm') {
                            $unit_dimension = 'cm';
                        } else {
                            $unit_dimension = 'inch';
                        }
                        if ($package_default[$keyOrder][$constants->unit_weight] == 'kgs') {
                            $unit_weight = 'Kg';
                        } else {
                            $unit_weight = 'Pounds';
                        }

                        $detail_package = $package_default[$keyOrder][$constants->length] . 'x' .
                            $package_default[$keyOrder][$constants->width] . 'x' . $package_default[$keyOrder][$constants->height] . ' ' .
                            $unit_dimension . ', ' . $package_default[$keyOrder][$constants->weight] . ' ' . $unit_weight;
                    } elseif (isset($package_default[$constants->unit_dimension])) {
                        if ($package_default[$constants->unit_dimension] == 'cm') {
                            $unit_dimension = 'cm';
                        } else {
                            $unit_dimension = 'inch';
                        }
                        if ($package_default[$constants->unit_weight] == 'kgs') {
                            $unit_weight = 'Kg';
                        } else {
                            $unit_weight = 'Pounds';
                        }
                        $detail_package = $package_default[$constants->length] . 'x' .
                            $package_default[$constants->width] . 'x' . $package_default[$constants->height] . ' ' .
                            $unit_dimension . ', ' . $package_default[$constants->weight] . ' ' . $unit_weight;
                    }
                    $countOackage = count($ship_data->PackageResults);
                    if ($countOackage > 1) {
                        foreach ($ship_data->PackageResults as $packageResult) {
                            $package_default['trackingnumber'] = $tracking_number = $packageResult->TrackingNumber;
                            $this->model_extension_upsmodule_tracking->createTracking(
                                $number_shipment,
                                $tracking_number,
                                $detail_package,
                                $value['id']
                            );
                        }
                    } else {
                        $tracking_number = $ship_data->PackageResults->TrackingNumber;
                        $this->model_extension_upsmodule_tracking->createTracking(
                            $number_shipment,
                            $tracking_number,
                            $detail_package,
                            $value['id']
                        );
                    }
                    $update_data
                        = $this->model_extension_upsmodule_openorders->updateStatusOrder($value['id'], $id_shipment);
                    //API Manage
                    $this->transferShipments($ship_from[2], $number_shipment);
                    //E API Manage
                    if (!empty($update_data)) {
                        foreach ($update_data as $items) {
                            //update status order in opencart to shipped (status id is 3)
                            $this->model_extension_upsmodule_opencartsetting->updateOrderOpencartStatus(
                                $items[$constants->order_id_opencart],
                                3
                            );
                            $this->model_extension_upsmodule_opencartsetting->addOrderHistoryOpencart(
                                $items[$constants->order_id_opencart],
                                3
                            );
                        }
                    }
                    $list_error[] = [$value[$constants->order_id_shipment], 'true', $value['id'], ""];
                } else {
                    if (!empty($data_response_ship)) {
                        $message = $data_response_ship->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode->Description;
                    } else {
                        $message = '';
                    }
                    $list_error[] = [$value[$constants->order_id_shipment], 'false', $value['id'], $message];
                }
            }
            echo json_encode(['listError' => $list_error]);
        }
    }



    /**
     * ControllerExtensionUpsmodulePackagedimension rateShipment
     *
     * @return null
     */
    public function rateShipment()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model('localisation/country');
        $this->load->model($constants->link_api_model);
        $this->load->model($constants->link_packagedimension);
        $this->load->model($constants->link_openorders);
        $this->load->model($constants->link_opencartsetting);
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        $data_post = $this->request->post;
        $ship_from = $data_post[$constants->ship_from];
        $ship_to = $data_post['ship_to'];
        $shipping_type = $data_post['shipping_type'];
        $package = $data_post['package'];
        if (!empty($data_post[$constants->accessorial_service_shipment])) {
            $accessorial_service = $data_post[$constants->accessorial_service_shipment];
        } else {
            $accessorial_service = '';
        }
        $order_selected = $data_post[$constants->order_selected];
        $list_order = $data_post['idorder'];
        $cod = $data_post['cod'];
        $order_value = $data_post[$constants->order_value];
        $check_edit = $data_post['edit_shipment'];
        $cut_off_time = $this->model_extension_upsmodule_opencartsetting->getCutOffTime();
        $time_data = $this->model_extension_upsmodule_opencartsetting->getTime();
        $pickup_date = '';
        if (!empty($time_data) && !empty($cut_off_time)) {
            $current_time = new DateTime($time_data['date']);
            if ((int)$current_time->format('H') < (int)$cut_off_time['value']) {
                $pickup_date = $current_time->format('Ymd');
            } else {
                $next_day = $current_time->modify('+1 day');
                $pickup_date = $next_day->format('Ymd');
            }
        }
        //format data
        $shipto_format = [];
        if ($shipping_type['0'] == 'ADD' && $check_edit == 1) {
            $shipto_format[] = $ship_to[0];
            $shipto_format[] = $ship_to[0];
            $shipto_format[] = $ship_to[2];
            $shipto_format[] = $ship_to[3];
            $shipto_format[] = $ship_to[4];
            $shipto_format[] = $ship_to[5];
            $shipto_format[] = $ship_to[6];
            $shipto_format[] = $ship_to[1];
            $shipto_format[] = implode("", explode(" ", $ship_to[7]));
            $shipto_format[] = $ship_to[8];
            $shipto_format[] = $ship_to[9];
        } else {
            $name = $order_selected[$constants->first_name] . ' ' . $order_selected[$constants->last_name];
            $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByZoneId(
                $order_selected[$constants->shipping_zone_id]
            );
            $country_data = $this->model_localisation_country->getCountry(
                $order_selected[$constants->shipping_country_id]
            );
            $shipto_format[] = $name;
            $shipto_format[] = $name;
            $shipto_format[] = $order_selected[$constants->telephone];
            $shipto_format[] = $order_selected[$constants->shipping_address1];
            $shipto_format[] = $order_selected[$constants->shipping_address2];
            $shipto_format[] = '';
            $shipto_format[] = $order_selected[$constants->ship_city];
            $shipto_format[] = (!empty($state_data)) ? $state_data['code'] : '';
            $shipto_format[] = implode("", explode(" ", $order_selected[$constants->shipping_posttal_code]));
            $shipto_format[] = (!empty($country_data)) ? $country_data[$constants->iso_code_2_shipment] : 'PL';
            $shipto_format[] = $order_selected[$constants->email_data];
        }
        $data = [];
        $data[$constants->shipping_type] = $shipping_type[0];
        $data["Typerate"] = 'createshipment';
        $data["Request"]["RequestOption"] = 'RATETIMEINTRANSIT';
        $data["DeliveryTimeInformation"]["Pickup"]["Date"] = $pickup_date;
        $data[$constants->shipper][$constants->name_data] = $ship_from[0];
        $data[$constants->shipper][$constants->shipper_number] = $ship_from[2];
        $data[$constants->shipper][$constants->address_data][$constants->address_line] = [$ship_from[4], $ship_from[5], $ship_from[6]];
        $data[$constants->shipper][$constants->address_data][$constants->city_data] = $ship_from[7];
        $data[$constants->shipper][$constants->address_data][$constants->province_code_acc] = $ship_from[1];
        $data[$constants->shipper][$constants->address_data][$constants->postal_code_shipment] = implode("", explode(" ", $ship_from[8]));
        $data[$constants->shipper][$constants->address_data][$constants->country_code_shipment] = $ship_from[9];
        $data[$constants->ship_to_data][$constants->name_data] = $shipto_format[0];
        $data[$constants->ship_to_data][$constants->address_data][$constants->address_line] = [
            $shipto_format[3],
            $shipto_format[4], $shipto_format[5]
        ];
        $data[$constants->ship_to_data][$constants->address_data][$constants->city_data] = $shipto_format[6];
        $data[$constants->ship_to_data][$constants->address_data][$constants->province_code_acc] = $shipto_format[7];
        $data[$constants->ship_to_data][$constants->address_data][$constants->postal_code_shipment] = $shipto_format[8];
        $data[$constants->ship_to_data][$constants->address_data][$constants->country_code_shipment] = $shipto_format[9];
        $data[$constants->ship_from_data][$constants->name_data] = $ship_from[0];
        $data[$constants->ship_from_data][$constants->shipper_number] = $ship_from[2];
        $data[$constants->ship_from_data][$constants->address_data][$constants->address_line] = [
            $ship_from[4], $ship_from[5],
            $ship_from[6]
        ];
        $data[$constants->ship_from_data][$constants->address_data][$constants->city_data] = $ship_from[7];
        $data[$constants->ship_from_data][$constants->address_data][$constants->province_code_acc] = $ship_from[1];
        $data[$constants->ship_from_data][$constants->address_data][$constants->postal_code_shipment] = implode("", explode(" ", $ship_from[8]));
        $data[$constants->ship_from_data][$constants->address_data][$constants->country_code_shipment] = $ship_from[9];
        $data["PaymentDetails"][$constants->shipment_charge][$constants->type_data] = "01";
        $data["PaymentDetails"][$constants->shipment_charge][$constants->bill_shipper][$constants->account_number] = $ship_from[2];
        $data[$constants->service_data][$constants->code_data] = $shipping_type[1];
        $data[$constants->service_data][$constants->description_data] = $shipping_type[3];
        $data['InvoiceLineTotal'][$constants->currency_code_data]
            = $order_selected[$constants->currency_code];
        $data['InvoiceLineTotal'][$constants->monetary_value] = $order_value;
        if ($shipping_type['0'] == 'AP') {
            $data[$constants->alternate_delivery_address][$constants->name_data]
                = str_replace($constants->special_remove, '&', $ship_to[0]);
            $data[$constants->alternate_delivery_address][$constants->attention_name]
                = str_replace($constants->special_remove, '&', $ship_to[0]);
            $data[$constants->alternate_delivery_address][$constants->address_data][$constants->address_line] = str_replace($constants->address_replace, ' ', $ship_to[3]);
            $data[$constants->alternate_delivery_address][$constants->address_data][$constants->city_data] = $ship_to[6];
            $data[$constants->alternate_delivery_address][$constants->address_data][$constants->province_code_acc] = $ship_to[1];
            $data[$constants->alternate_delivery_address][$constants->address_data][$constants->postal_code_shipment] = implode("", explode(" ", $ship_to[7]));
            $data[$constants->alternate_delivery_address][$constants->address_data][$constants->country_code_shipment] = $ship_to[8];
        }
        if ($accessorial_service) {
            foreach ($accessorial_service as $key => $value) {
                $data[$constants->accessorials_data][$key] = [];
            }
        }
        if ($cod == 1) {
            $data[$constants->alternate_delivery_address]['COD'] = '1';
            if ($shipping_type['0'] == 'AP') {
                $data[$constants->accessorials_data][$constants->ups_access_point_cod] = [];
            } else {
                $data[$constants->accessorials_data][$constants->ups_home_cod] = [];
            }
        }
        $package_api = [];
        foreach ($package as $key => $value) {
            if (is_numeric($value)) {
                $list_package_default
                    = $this->model_extension_upsmodule_packagedimension->getListPackageSelected($value);
                $package_api[] = $list_package_default;
            } else {
                $package_api[] = $value;
            }
        }
        foreach ($package_api as $key => $value) {
            $check_code_unit_weight = $value[$constants->unit_weight];
            $check_name_unit_weight = '';
            if ($check_code_unit_weight == 'kgs') {
                $check_name_unit_weight = $constants->kilograms;
            } else {
                $check_name_unit_weight = $constants->pounds;
            }
            $check_code_unit = isset($value[$constants->unit_dimension]) ? $value[$constants->unit_dimension] : '';
            $check_name_unit = '';
            if ($check_code_unit == 'cm') {
                $check_name_unit = $constants->centimeter;
            } else {
                $check_code_unit = 'in';
                $check_name_unit = $constants->inches;
            }
            if(isset($value[$constants->length]) && isset($value[$constants->width]) && isset($value[$constants->height])){
            $data[$constants->package_data][] = [
                $constants->dimensions => [
                    $constants->unit_of_measurement => [
                        $constants->code_data => $check_code_unit,
                        $constants->description_data => $check_name_unit
                    ],
                    $constants->length_data => $value[$constants->length],
                    $constants->width_data => $value[$constants->width],
                    $constants->height_data => $value[$constants->height]
                ],
                $constants->package_weight => [
                    $constants->unit_of_measurement => [
                        $constants->code_data => $value[$constants->unit_weight],
                        $constants->description_data => $check_name_unit_weight
                    ],
                    $constants->weight_data => $value[$constants->weight]
                ]
            ];
        }else{
            $data[$constants->package_data][] = [
            $constants->package_weight => [
                $constants->unit_of_measurement => [
                    $constants->code_data => $value[$constants->unit_weight],
                    $constants->description_data => $check_name_unit_weight
                ],
                $constants->weight_data => $value[$constants->weight]
            ]
        ];
        }
        }
        //call api get tracking, shipment number
        $response = $this->callUpsApi($constants->Rate, $data, $license);
        $check = true;
        $result_data = [];
        $message = "";
        if (
            isset($response->RateResponse->Response->ResponseStatus->Code)
            && $response->RateResponse->Response->ResponseStatus->Code == 1
        ) {
            $rate_data = $response->RateResponse->RatedShipment;
            $result_data[$constants->currency_code] = $rate_data->TotalCharges->CurrencyCode;
            $result_data['monetary_value'] = $rate_data->TotalCharges->MonetaryValue;
            if (isset($rate_data->NegotiatedRateCharges)) {
                $result_data[$constants->currency_code] = $rate_data->NegotiatedRateCharges->TotalCharge->CurrencyCode;
                $result_data['monetary_value'] = $rate_data->NegotiatedRateCharges->TotalCharge->MonetaryValue;
            }
            $date1 = $rate_data->TimeInTransit->ServiceSummary->EstimatedArrival->Arrival->Date;
            $time1 = $rate_data->TimeInTransit->ServiceSummary->EstimatedArrival->Arrival->Time;
            $date2 = $rate_data->TimeInTransit->ServiceSummary->EstimatedArrival->Pickup->Date;
            $time2 = $rate_data->TimeInTransit->ServiceSummary->EstimatedArrival->Pickup->Time;
            if ((int)$date1 > (int)$date2) {
                $date = $date1;
                $time = $time1;
            } else {
                $date = $date2;
                $time = $time2;
            }
            $result_data['time_in_transit'] = substr($date, 0, 4) . '-' . substr($date, 4, 2) . '-' .
                substr($date, 6, 2) . ' ' . substr($time, 0, 2) . ':' . substr($time, 2, 2) . ':' . substr($time, 4, 2);
            $message = "";
        } else {
            $check = false;
            if (!empty($response)) {
                $message = $response->Fault->detail->Errors->ErrorDetail->PrimaryErrorCode->Description;
            }
        }
        echo json_encode(
            [
                'check' => $check,
                'message' => $message,
                'result' => $result_data
            ]
        );
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension detailShipment
     *
     * @return null
     */
    public function detailShipment()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_shipments);
        $this->load->model('extension/upsmodule/shippingservice');
        $this->load->language($constants->link_translate);
        $this->load->model($constants->link_opencartsetting);
        $this->load->model($constants->link_country);
        $estimated_currency_code = $this->model_extension_upsmodule_base->getSettingByKey(
            "ups_shipping_fee_currency_code"
        );
        $pl_api = $constants->poland_api_code;
        if (!empty($estimated_currency_code)) {
            $pl_api = $estimated_currency_code;
        }
        $request = $this->request->post;
        $order_id = $request[$constants->order_id_shipment];
        $tracking_id = $request['id_tracking'];
        $detail_shipment = $this->model_extension_upsmodule_shipments->getViewDetailShipment($tracking_id);
        if ($detail_shipment[$constants->decimal_place] != '') {
            $decimal_value = $detail_shipment[$constants->decimal_place];
        } else {
            $decimal_value = 0;
        }
        $order_id = $detail_shipment[$constants->order_id_shipment];
        $shipment_number = $detail_shipment[$constants->shipment_number];
        $tracking_number = $detail_shipment[$constants->tracking_number];
        $currency_code = $detail_shipment[$constants->currency_code];
        $name = $detail_shipment[$constants->name_value];
        $phone = $detail_shipment[$constants->phone_shipment];
        $email = $detail_shipment[$constants->email_data];
        $package_detail = $detail_shipment[$constants->package_detail];
        $detail_shipment['plApi'] = $pl_api;
        $detail_shipment['order_id'] = $order_id;
        $detail_shipment['shipment_number'] = $shipment_number;
        $detail_shipment[$constants->tracking_number] = $tracking_number;
        $detail_shipment['currency_code'] = $currency_code;
        $detail_shipment['name'] = $name;
        $detail_shipment['phone'] = $phone;
        $detail_shipment['email'] = $email;
        $detail_shipment['package_detail'] = $package_detail;
        $detail_shipment[$constants->create_date]
            = date('M d, Y, H:i:s', strtotime($detail_shipment[$constants->create_date]));
        $detail_shipment[$constants->shipping_fee]
            = number_format((float)$detail_shipment[$constants->shipping_fee], 2);
        $detail_shipment[$constants->order_value]
            = number_format($detail_shipment[$constants->order_value], $decimal_value);
        $package_status = $this->model_extension_upsmodule_shipments->getPackageStatus(
            $detail_shipment[$constants->tracking_number]
        );
        //region update status API
        $select_name = $this->model_extension_upsmodule_shipments->selectNameStatusOrderStatus($package_status);
        if (strtolower($package_status) == "delivered") {
            $update_delivered_order_pl = $this->model_extension_upsmodule_shipments->updateStatusOrderViewDetail(
                $select_name['order_status_id'],
                $order_id
            );
        }
        //end
        $package_status = ucwords(str_replace('_', ' ', $package_status));
        $detail_shipment['package_status'] = $package_status;
        $this->model_extension_upsmodule_shipments->updateStatus($detail_shipment['shipment_id'], $package_status);
        $product_name = explode(',', $detail_shipment['listProduct']);
        $cut_name = array_unique($product_name);
        $detail_shipment['product_name'] = implode('<br/>', $cut_name);
        //handling address
        $shipment_address = [];
        $customer_address = [];
        $arr_country_state_name = $this->model_extension_upsmodule_country->getStateCountryName();
        $array_country_state_name  = [];
        foreach ($arr_country_state_name as $item) {
            $array_country_state_name[$item['countryCode']][$item['stateCode']] = $item['StateName'];
        }
        $check_state = '';
        if (isset($array_country_state_name[$detail_shipment[$constants->country_shipment]][$detail_shipment[$constants->state_shipment]])) {
            $check_state = $array_country_state_name[$detail_shipment[$constants->country_shipment]][$detail_shipment[$constants->state_shipment]];
        }
        $country_data = $this->model_extension_upsmodule_opencartsetting->getCountryByCountryCode(
            $detail_shipment[$constants->country_shipment]
        );
        $state = '';
        $country = '';
        if (!empty($state_data)) {
            $state = $state_data[$constants->name_value];
        }
        if (!empty($country_data)) {
            $country = $country_data[$constants->name_value];
        }
        $shipment_address[] = $detail_shipment[$constants->name_value];
        $customer_address = [
            $detail_shipment[$constants->shipping_address1],
            $detail_shipment[$constants->shipping_address2],
            $detail_shipment[$constants->ship_city],
            $detail_shipment[$constants->ship_zone],
            $detail_shipment[$constants->shipping_posttal_code],
            $detail_shipment[$constants->shipping_country_shipment],
        ];
        $customer_address = $this->addArray($customer_address);
        $detail_shipment['customer_address'] = html_entity_decode(implode('<br/>', $customer_address));
        if (!empty($detail_shipment[$constants->address1])) {
            array_push($shipment_address, $detail_shipment[$constants->address1]);
        } else {
            $detail_shipment[$constants->address1] = null;
        }
        if (!empty($detail_shipment[$constants->address2])) {
            array_push($shipment_address, $detail_shipment[$constants->address2]);
        } else {
            $detail_shipment[$constants->address2] = null;
        }
        if (!empty($detail_shipment[$constants->address3])) {
            array_push($shipment_address, $detail_shipment[$constants->address3]);
        } else {
            $detail_shipment[$constants->address3] = null;
        }
        if (!empty($detail_shipment['city'])) {
            $shipment_address[] = $detail_shipment['city'];
        }
        $check_state = '';
        if (isset($array_country_state_name[$detail_shipment[$constants->country_shipment]][$detail_shipment[$constants->state_shipment]])) {
            $check_state = $array_country_state_name[$detail_shipment[$constants->country_shipment]][$detail_shipment[$constants->state_shipment]];
        }
        $shipment_address[] = $check_state;
        if (!empty($detail_shipment[$constants->name_zone])) {
            $state_data = $this->model_extension_upsmodule_opencartsetting->getStateByStateCode(
                $detail_shipment[$constants->name_zone],
                $detail_shipment[$constants->country_shipment]
            );
            if (!empty($state_data)) {
                array_push($shipment_address, $state_data[$constants->name_value]);
            }
        }
        if (!empty($detail_shipment[$constants->post_code_shipment])) {
            $shipment_address[] = $detail_shipment[$constants->post_code_shipment];
        }
        if (!empty($detail_shipment[$constants->country_shipment])) {
            $country_data = $this->model_extension_upsmodule_opencartsetting->getCountryByCountryCode(
                $detail_shipment[$constants->country_shipment]
            );
            if (!empty($country_data)) {
                $shipment_address[] = $country_data[$constants->name_value];
            }
        }
        $shipment_address = $this->addArray($shipment_address);
        $detail_shipment['shipment_address'] = html_entity_decode(implode('<br />', $shipment_address));
        if ($detail_shipment['service_symbol'] == '&trade;') {
            $detail_shipment['service_name_info'] = 'UPS Access Point&trade; Economy';
        } else {
            $detail_shipment['service_name_info'] = $detail_shipment[$constants->service_name_shipment] .
                $detail_shipment['service_symbol'];
        }
        // Accessorial
        $accessorial_array = [];
        if (!empty($detail_shipment[$constants->accessorial_service_shipment])) {
            $accessorial_array = json_decode($detail_shipment[$constants->accessorial_service_shipment], true);
            foreach ($accessorial_array as $key => $value) {
                $accessorial_translate = $this->language->get($key);
                $accessorial_array[$key] = $accessorial_translate;
            }
        }
        $detail_shipment['accessorial_array'] = $accessorial_array;
        //Call API Updtate Status
        if (isset($detail_shipment['shipment_number'])) {
            $this->load->model($this->_plugin_manage_model);
            $shipment = [];
            $shipment_data = new \stdClass();
            $shipment_data->tracking_number = $detail_shipment[$constants->tracking_number];
            $shipment_data->shipment_status = $package_status;
            $shipment[] = $shipment_data;
            $this->model_extension_upsmodule_pluginmanage->updateShipmentStatus($shipment);
        }
        echo json_encode($detail_shipment);
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension addArray
     *
     * @param string $array //The array
     *
     * @return null
     */
    function addArray($array)
    {
        $array_return = [];
        foreach ($array as $key => $value) {
            if (!empty($value)) {
                $array_return[] = $value;
            }
        }
        return $array_return;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension transferShipments
     *
     * @param string $shipment_number //The shipment_number
     *
     * @return null
     */
    public function transferShipments($account_number, $shipment_number)
    {
        $this->load->model($this->_plugin_manage_model);
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_shipments);
        //Find shippmet
        $shipment_info = $this->model_extension_upsmodule_shipments->getShipmentInfo($shipment_number);
        //accessorial accessorial_name
        $accessorial_service = $shipment_info->accessorial_service;
        $arr_accessorial = $this->model_extension_upsmodule_base->getAccessorialAll();
        $accessorial = [];
        if (!empty($accessorial_service)) {
            $object = json_decode($accessorial_service, true);
            foreach ($object as $key => $name) {
                if (isset($arr_accessorial[$key])) {
                    $obj_accessorial = new \stdClass();
                    $obj_accessorial->accessorial_name = $arr_accessorial[$key];
                    $accessorial[] = $obj_accessorial;
                }
            }
        }
        $tracking_info = $this->model_extension_upsmodule_shipments->getTrackingInfo($shipment_number);
        $arr_package = [];
        foreach ($tracking_info as $item) {
            $detail_package = $this->sliptPackage($item['package_detail']);
            $object_package = new \stdClass();
            $object_package->tracking_number = $item[$constants->tracking_number];
            $object_package->weight = $detail_package->weight;
            $object_package->unit_weight = $detail_package->unit_weight;
            $object_package->length = $detail_package->length;
            $object_package->width = $detail_package->width;
            $object_package->height = $detail_package->height;
            $object_package->unit_dimension = $detail_package->unit_dimension;
            $arr_package[] = $object_package;
        }
        $this->model_extension_upsmodule_pluginmanage->transferShipment($shipment_info, $account_number, $accessorial, $arr_package);
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension sliptPackage
     *
     * @param string $str //The str
     *
     * @return null
     */
    function sliptPackage($str)
    {
        $str_arr = explode(',', $str);
        $object_package = new \stdclass();
        $object_package->weight = '';
        $object_package->unit_weight = '';
        $object_package->length = '';
        $object_package->width = '';
        $object_package->height = '';
        $object_package->unit_dimension = '';
        if (isset($str_arr[0]) && isset($str_arr[1])) {
            $object_package->weight = substr($str_arr[1], 0, -2);
            $object_package->unit_weight = substr($str_arr[1], -2);
            $arr_first = explode(' ', $str_arr[0]);
            if (isset($arr_first[1])) {
                $object_package->unit_dimension = $arr_first[1];
            }
            if (isset($arr_first[0])) {
                $arr_lwh = explode('x', $arr_first[0]);
                if (isset($arr_lwh[0])) {
                    $object_package->length = $arr_lwh[0];
                }
                if (isset($arr_lwh[1])) {
                    $object_package->width = $arr_lwh[1];
                }
                if (isset($arr_lwh[2])) {
                    $object_package->height = $arr_lwh[2];
                }
            }
        }
        return $object_package;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension callUpsApi
     *
     * @param string $method  //The method
     * @param string $data    //The data
     * @param string $license //The license
     *
     * @return null
     */
    public function callUpsApi($method, $data, $license = null)
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $commonInfo = $this->model_extension_upsmodule_base->getCommonInfo();
        include_once "$constants->link_api_ups";
        //get api
        $api = new Ups();
        $api->setCommonApiInfo($commonInfo);
        $response = null;
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($method);
        switch ($method) {
            case $constants->LBRecovery:
                $response = json_decode($api->printLabel($data, $license));
                break;
            case $constants->Ship:
                $response = json_decode($api->createShipment($data, $license));
                break;
            case $constants->Rate:
                $response = json_decode($api->getRate($data, $license));
                break;
            default:
                break;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);
        return $response;
    }
}
