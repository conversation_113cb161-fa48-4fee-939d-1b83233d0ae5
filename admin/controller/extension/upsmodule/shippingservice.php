<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleShippingservice file
 *
 * @category Shippingservice_Controller
 */

class ControllerExtensionUpsmoduleShippingservice extends Controller
{
    private $_user_token = 'user_token';
    private $_user_token_link = 'user_token=';
    private $_text_sp_services = 'text_SPServices';
    private $_extension_shipping_service = 'extension/upsmodule/shippingservice';
    private $_value = 'value';
    private $_to_ap_delivery = 'to_ap_delivery';
    private $_to_add_delivery = 'to_add_delivery';
    private $_base_model = 'extension/upsmodule/base';
    private $_country_model = 'extension/upsmodule/country';
    private $_plugin_manage_model = 'extension/upsmodule/pluginmanage';

    /**
     * ControllerExtensionUpsmoduleShippingservice index
     *
     * @return null
     */
    public function index()
    {
        //Load model
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "shippingservice",
            "extension/upsmodule/shippingservice"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $this->load->language('extension/upsmodule/translate');
        $this->document->setTitle($this->language->get($this->_text_sp_services));
        $this->load->model($this->_extension_shipping_service);
        $this->load->model('extension/upsmodule/opencartsetting');
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleShippingservice getForm
     *
     * @return null
     */
    protected function getForm()
    {
        $this->load->model($this->_base_model);
        $this->load->model($this->_country_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $list_check_account = $this->model_extension_upsmodule_shippingservice->getListCheckAccount();
        foreach ($list_check_account as $row) {
            if ($row[$this->_value] == 1) {
                $this->url->link(
                    $this->_extension_shipping_service,
                    $this->_user_token_link .
                    $this->session->data[$this->_user_token],
                    true
                );
            } else {
                $this->response->redirect(
                    $this->url->link(
                        'extension/upsmodule/account',
                        $this->_user_token_link .
                        $this->session->data[$this->_user_token],
                        true
                    )
                );
            }
        }
        $data['action'] = $this->url->link(
            'extension/upsmodule/shippingservice/saveService&user_token=' .
            $this->session->data[$this->_user_token],
            true
        );
        $list_setting = $this->model_extension_upsmodule_shippingservice->getSetting();
        foreach ($list_setting as $key => $setting) {
            $data[$setting['key']] = $setting[$this->_value];
        }
        $list_country_code = $this->model_extension_upsmodule_opencartsetting->getCountryCode();
        if ($list_country_code) {
            $data['services_to_ap'] = $this->model_extension_upsmodule_shippingservice->listShippingServices(
                $list_country_code[$this->_value],
                'AP'
            );
            $data['services_to_add'] = $this->model_extension_upsmodule_shippingservice->listShippingServices(
                $list_country_code[$this->_value],
                'ADD'
            );
        }
        $data['get_list_account'] = $this->model_extension_upsmodule_shippingservice->getListAccount();
        $data['option_chosen_number_of_access'] = [3, 4, 5, 6, 7, 8, 9, 10];
        $data['option_chosen_display_all'] = [5, 10, 15, 20, 30, 50];
        $data['option_cut_off_time'] = [
            '00' => '12 AM',
            '01' => '1 AM',
            '02' => '2 AM',
            '03' => '3 AM',
            '04' => '4 AM',
            '05' => '5 AM',
            '06' => '6 AM',
            '07' => '7 AM',
            '08' => '8 AM',
            '09' => '9 AM',
            '10' => '10 AM',
            '11' => '11 AM',
            '12' => '12 PM',
            '13' => '1 PM',
            '14' => '2 PM',
            '15' => '3 PM',
            '16' => '4 PM',
            '17' => '5 PM',
            '18' => '6 PM',
            '19' => '7 PM',
            '20' => '8 PM',
            '21' => '9 PM',
            '22' => '10 PM',
            '23' => '11 PM',
            '24' => 'Disable'
        ];
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data[$this->_text_sp_services] = $this->language->get($this->_text_sp_services);
        $data['text_errors_select_ship'] = $this->language->get('text_errors_select_ship');
        $url = '';
        //Infor account
        $data['text_home'] = $this->language->get('text_home');
        $data['text_Yes'] = $this->language->get('text_Yes');
        $data['text_No'] = $this->language->get('text_No');
        $data[$this->_text_sp_services] = $this->language->get($this->_text_sp_services);
        $data['text_desc_spservices'] = $this->language->get('text_desc_spservices');
        $data['text_outside'] = $this->language->get('text_outside_eu');
        $data['text_sat_delivery'] = $this->language->get('text_sat_delivery_eu');

        $data['text_ap_delivery'] = $this->language->get('text_ap_delivery');
        $data['text_desc_ap_delivery'] = $this->language->get('text_desc_ap_delivery');
        $data['text_num_ap'] = $this->language->get('text_num_ap');
        $data['text_ap_range'] = $this->language->get('text_ap_range');
        $data['text_desc_ap_range'] = $this->language->get('text_desc_ap_range');
        $data['text_desc_cut_off_time'] = $this->language->get('text_desc_cut_off_time');
        $data['text_ap_setting'] = $this->language->get('text_ap_setting');

        $country_data = $this->model_extension_upsmodule_country->getCountryCode();
        $data['country_code'] = '';
        $data['ups_footer'] = '';
        if (!empty($country_data) && strtolower($country_data[$constants->value]) == 'us') {
            $data['text_ap_delivery'] = $this->language->get('text_ap_delivery_US');
            $data['text_to_us'] = $this->language->get('text_to_us');
            $data['text_to_international'] = $this->language->get('text_to_international');
            $data['text_desc_ap_delivery'] = $this->language->get('text_desc_ap_delivery_US');
            $data['text_num_ap'] = $this->language->get('text_num_ap_US');
            $data['text_ap_range'] = $this->language->get('text_ap_range_US');
            $data['text_desc_ap_range'] = $this->language->get('text_desc_ap_range_US');
            $data['text_desc_cut_off_time'] = $this->language->get('text_desc_cut_off_time_US');
            $data['text_require_adult_signature'] = $this->language->get('text_require_adult_signature');
            $data['text_ap_setting'] = $this->language->get('text_ap_setting_US');
            $data['country_code'] = $country_data[$constants->value];
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        }

        $data['text_ship_option'] = $this->language->get('text_ship_option');
        $data['text_select_ap'] = $this->language->get('text_select_ap');
        $data['text_ap_choose'] = $this->language->get('text_ap_choose');
        $data['text_add_delivery'] = $this->language->get('text_add_delivery');
        $data['text_desc_add_delivery'] = $this->language->get('text_desc_add_delivery');
        $data['text_select_add'] = $this->language->get('text_select_add');
        $data['text_add_choose'] = $this->language->get('text_add_choose');
        $data['text_cut_off_time'] = $this->language->get('text_cut_off_time');
        $data['key_select'] = $this->language->get('key_select');
        //Button
        $data['button_save'] = $this->language->get('button_save');
        $data['button_next'] = $this->language->get('button_next');
        $data[$this->_user_token] = $this->session->data[$this->_user_token];
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['shipping_service'] = $this->url->link(
            'extension/upsmodule/shippingservice&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $ss = 0;
        if (isset($this->session->data['err'])) {
            $ss = $this->session->data['err'];
        }
        $data['error'] = $ss;
        $this->session->data['err'] = 0;
        //xoa session
        $this->response->setOutput($this->load->view($this->_extension_shipping_service, $data));
    }

    /**
     * ControllerExtensionUpsmoduleShippingservice validate
     *
     * @param string $data_form //The dataForm
     *
     * @return null
     */
    public function validate($data_form)
    {
        $count_validate = 0;
        if (($data_form[$this->_to_ap_delivery] == '1' && !isset($data_form['serviceAp']))
            || ($data_form[$this->_to_add_delivery] == '1' && !isset($data_form['serviceAdd']))
        ) {
            $count_validate += 4;
        }
        if ($data_form[$this->_to_ap_delivery] == '0' && $data_form[$this->_to_add_delivery] == '0') {
            $count_validate += 2;
        }
        if ($count_validate == 6 || $count_validate == 2 || $count_validate == 4) {
            return 0;
        } else {
            return 1;
        }
    }

    /**
     * ControllerExtensionUpsmoduleShippingservice saveToAp
     *
     * @param string $request              //The request
     * @param array $list_key_delivery      //The listKeyDelivery
     * @param string $list_country_code    //The list_country_code
     * @param string $list_shipping_services //The listShippingservices
     *
     * @return null
     */
    public function saveToAp($request, $list_key_delivery, $list_country_code, $list_shipping_services)
    {
        $country_code = $list_country_code[$this->_value];
        $this->model_extension_upsmodule_shippingservice->updateSetting(
            'ups_shipping_default_shipping',
            $request['default_shipping']
        );
        $this->model_extension_upsmodule_base->saveOptionSetting(
            'ups_shipping_apadr_toshipadr',
            $request['apadr_toshipadr']
        );
        $this->model_extension_upsmodule_shippingservice->updateSetting(
            'ups_shipping_chosen_number_of_access',
            $request['chosen_number_of_access']
        );
        $this->model_extension_upsmodule_shippingservice->updateSetting(
            'ups_shipping_chosen_display_all',
            $request['chosen_display_all']
        );
        $this->model_extension_upsmodule_shippingservice->updateSetting(
            'ups_shipping_choose_account_number_ap',
            $request['choose_account_number_ap']
        );
        foreach ($list_shipping_services as $service) {
            if ($service['service_type'] == 'AP' && $service['country_code'] == $country_code) {
                $this->model_extension_upsmodule_shippingservice->updateService($service['id'], '0');
            }
        }
        foreach ($request['serviceAp'] as $key => $id) {
            $this->model_extension_upsmodule_shippingservice->updateService($id, '1');
            if (!in_array($id, $list_key_delivery)) {
                $this->model_extension_upsmodule_shippingservice->saveDeliveryRates($country_code, $id);
            }
        }
    }

    /**
     * ControllerExtensionUpsmoduleShippingservice saveToAdd
     *
     * @param string $request              //The request
     * @param array $list_key_delivery      //The listKeyDelivery
     * @param string $list_country_code    //The list_country_code
     * @param string $list_shipping_services //The listShippingservices
     *
     * @return null
     */
    public function saveToAdd($request, $list_key_delivery, $list_country_code, $list_shipping_services)
    {
        $country_code = $list_country_code[$this->_value];
        $this->model_extension_upsmodule_shippingservice->updateSetting(
            'ups_shipping_choose_account_number_add',
            $request['choose_account_number_add']
        );
        foreach ($list_shipping_services as $service) {
            if ($service['service_type'] == 'ADD' && $service['country_code'] == $list_country_code[$this->_value]) {
                $this->model_extension_upsmodule_shippingservice->updateService($service['id'], '0');
            }
        }
        foreach ($request['serviceAdd'] as $key => $id) {
            $this->model_extension_upsmodule_shippingservice->updateService($id, '1');
            if (!in_array($id, $list_key_delivery)) {
                $this->model_extension_upsmodule_shippingservice->saveDeliveryRates($country_code, $id);
            }
        }
    }

    /**
     * ControllerExtensionUpsmoduleShippingservice saveService
     *
     * @return null
     */
    function saveService()
    {
        $url = '';
        $this->load->model($this->_base_model);
        $this->load->model($this->_extension_shipping_service);
        $this->load->language('extension/upsmodule/translate');
        $this->load->model('extension/upsmodule/opencartsetting');
        $list_country_code = $this->model_extension_upsmodule_opencartsetting->getCountryCode();
        $list_shipping_services = $this->model_extension_upsmodule_shippingservice->listAllShippingServices();
        $get_list_delivery = $this->model_extension_upsmodule_shippingservice->getListDelivery();
        $list_key_delivery = [];
        foreach ($get_list_delivery as $delivery) {
            $list_key_delivery[] = $delivery['service_id'];
        }

        $request = $this->request->post;
        $validate = $this->validate($request);
        if ($validate == '0') {
            $this->session->data['err'] = 1;
            $this->response->redirect(
                $this->url->link(
                    $this->_extension_shipping_service,
                    $this->_user_token_link .
                    $this->session->data[$this->_user_token] . $url,
                    true
                )
            );
        } else {
            if ($request[$this->_to_ap_delivery] == '0') {
                $this->saveToAdd($request, $list_key_delivery, $list_country_code, $list_shipping_services);
            } elseif ($request[$this->_to_add_delivery] == '0') {
                $this->saveToAp($request, $list_key_delivery, $list_country_code, $list_shipping_services);
            } else {
                $this->saveToAp($request, $list_key_delivery, $list_country_code, $list_shipping_services);
                $this->saveToAdd($request, $list_key_delivery, $list_country_code, $list_shipping_services);
            }
            $get_list_uncheck
                = $this->model_extension_upsmodule_shippingservice->getListUncheckServices(
                    $list_country_code[$this->_value]
                );
            foreach ($get_list_uncheck as $value) {
                $this->model_extension_upsmodule_shippingservice->deleteDelivery($value['id']);
            }
            if (!empty($list_country_code) &&
                isset($list_country_code[$this->_value]) &&
                strtolower($list_country_code[$this->_value]) == 'us'
            ) {
                $this->model_extension_upsmodule_base->saveOptionSetting(
                    'ups_shipping_require_signature',
                    $request['require_signature']
                );
            }
            $this->model_extension_upsmodule_shippingservice->updateSetting(
                'ups_shipping_to_ap_delivery',
                $request[$this->_to_ap_delivery]
            );
            $this->model_extension_upsmodule_shippingservice->updateSetting(
                'ups_shipping_to_add_delivery',
                $request[$this->_to_add_delivery]
            );
            $this->model_extension_upsmodule_shippingservice->updateSetting(
                'ups_shipping_cut_off_time',
                $request['cut_off_time']
            );
            $this->model_extension_upsmodule_shippingservice->updateCheckShippingService();
            //region add Update API Manage
            $this->load->model($this->_base_model);
            $setting = $this->model_extension_upsmodule_base->getSetting();
            if (isset($setting->ups_shipping_check_manage) && $setting->ups_shipping_check_manage == 1) {
                $this->transferShippingServices();
            }
            //end
            if (isset($request['method']) && $request['method'] == 'next') {
                $this->response->redirect(
                    $this->url->link(
                        'extension/upsmodule/cashondelivery',
                        $this->_user_token_link .
                        $this->session->data[$this->_user_token],
                        true
                    )
                );
            } else {
                $this->response->redirect(
                    $this->url->link(
                        $this->_extension_shipping_service,
                        $this->_user_token_link .
                        $this->session->data[$this->_user_token],
                        true
                    )
                );
            }
        }
    }

    /**
     * ControllerExtensionUpsmoduleShippingservice transferShippingServices
     *
     * @return null
     */
    public function transferShippingServices()
    {
        $this->load->model($this->_plugin_manage_model);
        $this->model_extension_upsmodule_pluginmanage->transferShippingService();
    }
}
