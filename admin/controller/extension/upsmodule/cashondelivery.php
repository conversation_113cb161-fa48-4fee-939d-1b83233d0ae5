<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleCashondelivery file
 *
 * @category Cashondelivery_Controller
 */

class ControllerExtensionUpsmoduleCashondelivery extends Controller
{
    private $_error = [];
    private $_base_model = 'extension/upsmodule/base';
    private $_country_model = 'extension/upsmodule/country';

    /**
     * ControllerExtensionUpsmoduleCashondelivery index
     *
     * @return null
     */
    public function index()
    {
        //Load base model
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "cashondelivery",
            "extension/upsmodule/cashondelivery"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->txt_cashondelivery));
        //load model link
        $this->load->model($constants->link_opencartsetting);
        $this->load->model($constants->link_cashondelivery);
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleCashondelivery updateCheckCOD
     *
     * @return null
     */
    public function updateCheckCod()
    {
        //Load base model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->txt_cashondelivery));
        //load model link
        $this->load->model($constants->link_cashondelivery);
        //get post data
        $request = $this->request->post;
        //check $request['method']
        if (isset($request['method']) && $request['method'] == 'next') {
            //update
            $this->model_extension_upsmodule_cashondelivery->updateCheckCod();
            $this->model_extension_upsmodule_cashondelivery->updateCheckOption($request['option']);
            $this->response->redirect(
                $this->url->link('extension/upsmodule/packagedimension', $constants->token, true)
            );
        } else {
            $this->response->redirect($this->url->link($constants->link_cashondelivery, $constants->token, true));
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleCashondelivery getForm
     *
     * @return null
     */
    protected function getForm()
    {
        //Load base model
        $this->load->model($this->_base_model);
        $this->load->model($this->_country_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //get getListCheckShippingService()
        $list_check_shipping_service = $this->model_extension_upsmodule_cashondelivery->getListCheckShippingService();
        foreach ($list_check_shipping_service as $row) {
            //check $row['value']
            if ($row['value'] == 1) {
                $this->url->link($constants->link_cashondelivery, $constants->token, true);
            } else {
                $this->response->redirect(
                    $this->url->link('extension/upsmodule/shippingservice', $constants->token, true)
                );
            }
        }
        //get getCheckOption()
        $list_check_option = $this->model_extension_upsmodule_cashondelivery->getCheckOption();
        foreach ($list_check_option as $row) {
            $data['getValueOption'] = ($row['value']);
        }
        //link $data['action']
        $data['action'] = $this->url->link(
            'extension/upsmodule/cashondelivery/updateCheckCOD',
            $constants->token,
            true
        );
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data[$constants->txt_cashondelivery] = $this->language->get($constants->txt_cashondelivery);
        //check $this->request->get['id']
        if (!isset($this->request->get['id'])) {
            $data[$constants->txt_cashondelivery] = $this->language->get($constants->txt_cashondelivery);
        } else {
            $data[$constants->txt_cashondelivery] = $this->language->get('text_cashondelivery_default');
        }
        $country_data = $this->model_extension_upsmodule_country->getCountryCode();
        $data['country_code'] = '';
        $data['ups_footer'] = "";
        if (!empty($country_data)) {
            $data['country_code'] = $country_data[$constants->value];
            if (strtolower($country_data[$constants->value]) == 'us') {
                $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
            }
        }
        //translate
        $data['text_home'] = $this->language->get('text_home');
        $data['text_label_cashondelivery'] = $this->language->get('text_label_cashondelivery');
        $data['text_enable_cod'] = $this->language->get('text_enable_cod');
        $data['text_disable_cod'] = $this->language->get('text_disable_cod');
        $data['text_enable_info'] = $this->language->get('text_enable_info');
        $data['text_disable_info'] = $this->language->get('text_disable_info');
        $data['text_info_cashondelivery_1'] = $this->language->get('text_info_cashondelivery_1');
        $data['text_info_cashondelivery_2'] = $this->language->get('text_info_cashondelivery_2');
        $data['text_info_cashondelivery_3'] = $this->language->get('text_info_cashondelivery_3');
        $data['text_info_cashondelivery_4'] = $this->language->get('text_info_cashondelivery_4');
        //Button
        $data['button_next'] = $this->language->get('button_next');
        if (isset($this->_error['warning'])) {
            $data['error_warning'] = $this->_error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        //region cashondelivery
        $data['cashondelivery'] = json_encode($this->model_extension_upsmodule_opencartsetting->getCashOnDelivery());
        //end
        //check $this->_error['name']
        if (isset($this->_error['name'])) {
            $data['error_name'] = $this->_error['name'];
        } else {
            $data['error_name'] = [];
        }
        //check $this->_error['meta_title']
        if (isset($this->_error['meta_title'])) {
            $data['error_meta_title'] = $this->_error['meta_title'];
        } else {
            $data['error_meta_title'] = [];
        }
        //load model language
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['cod'] = $this->url->link(
            'extension/upsmodule/cashondelivery&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $this->response->setOutput($this->load->view($constants->link_cashondelivery, $data));
    }
}
