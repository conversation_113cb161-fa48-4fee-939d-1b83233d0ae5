<?php

/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleBillingpreference file
 *
 * @category Billingpreference_Controller
 */

class ControllerExtensionUpsmoduleBillingpreference extends Controller
{
    private $_error = [];
    private $_base_model = 'extension/upsmodule/base';
    private $_plugin_manage_model = 'extension/upsmodule/pluginmanage';
    private $_opencart_model = 'extension/upsmodule/opencartsetting';
    private $_country_model = 'extension/upsmodule/country';
    /**
     * ControllerExtensionUpsmoduleBillingpreference index
     *
     * @return null
     */
    public function index()
    {
        //Load base model
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "billingpreference",
            "extension/upsmodule/billingpreference"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->text_billing_preference));
        //load model link
        $this->load->model($constants->link_billingpreference);
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleBillingpreference updateCheckBillingPreference
     *
     * @return null
     */
    public function updateCheckBillingPreference()
    {
        //Load base model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load model link
        $this->load->language($constants->link_translate);
        $this->load->model($constants->link_billingpreference);
        //load request post
        $request = $this->request->post;
        $token = $constants->token;
        //Update
        if (isset($request['method']) && $request['method'] == 'next') {

            include_once DIR_CATALOG . 'model/extension/upsmodule/package_algorithm.php';
            $this->load->model('extension/upsmodule/packagedimension');
            $this->load->model('sale/order');
            $this->model_extension_upsmodule_billingpreference->updateCheckBillingPreference();
            $this->model_extension_upsmodule_billingpreference->updateCheckOpenOrders();
            $old_order_ids = $this->model_extension_upsmodule_billingpreference->migratecheck();
            $listProductDimension = $this->model_extension_upsmodule_packagedimension->listProductDimension();
            $model = new package();
            $packageSettingType = $this->model_extension_upsmodule_packagedimension->getPackageSettingType();
            $listDefaultPackage = $listPackageDefault = $this->model_extension_upsmodule_packagedimension->getListDefaultPackage();
            foreach ($old_order_ids->rows as $order_id) {
                if ($packageSettingType == '1' || empty($packageSettingType)) {
                    
                    if (isset($listPackageDefault[0]) && empty($listPackageDefault[0]['package_item']) && empty($packageSettingType)) {
                        $listDefaultPackage = [];
                        $listPackageDefault[0]['package_item'] = 1;
                        $listDefaultPackage[] = $listPackageDefault[0];
                    }
                    $order_products = $this->model_sale_order->getOrderProducts($order_id['order_id']);
                    $numberOfCartItem = count($order_products); //$this->cart->countProducts();
                    $listPackagebyItems = $this->model_extension_upsmodule_packagedimension->getListPackageByItems();

                    if (empty($listPackagebyItems)) {
                        $listPackagebyItems = $listDefaultPackage;
                    }
                    $packageIndex = 0;
                    $numberOfItem = 1;
                    foreach ($listDefaultPackage as $key => $defaultPackage) {
                        $weightUnit = $defaultPackage['unit_weight'];
                        $dimensionUnit = $defaultPackage['unit_dimension'];
                        if (isset($defaultPackage['package_item']) && $defaultPackage['package_item'] <= $numberOfCartItem && $numberOfItem <= $defaultPackage['package_item']) {
                            $packageIndex = $key;
                            $numberOfItem = $defaultPackage['package_item'];
                        }
                    }
                    $countItem = count($listDefaultPackage);
                    if (0 < $countItem && isset($listDefaultPackage[$countItem - 1]['package_item']) && $numberOfCartItem < $listDefaultPackage[$countItem - 1]['package_item']) {
                        $packageIndex = $countItem - 1;
                    }
                    $pkgLength = $listDefaultPackage[$packageIndex]['length'];
                    $pkgWidth  = $listDefaultPackage[$packageIndex]['width'];
                    $pkgHeight = $listDefaultPackage[$packageIndex]['height'];
                    $pkgWeight = $listDefaultPackage[$packageIndex]['weight'];
                    $description = 'Get default package dimension';


                    $package = new stdClass();
                    // Set package info
                    $package->length = $pkgLength;
                    $package->width = $pkgWidth;
                    $package->height = $pkgHeight;
                    $package->unit_dimension = $dimensionUnit;
                    $package->weight = $pkgWeight;
                    $package->unit_weight = $weightUnit;
                    $package->number_of_package = '1';
                    // return
                    $this->session->data['ups_service_shipping_package'] = $package;
                    $model->entryorder($package, $order_id['order_id'], $this);
                } else {
                    $products = $this->model_sale_order->getOrderProducts($order_id['order_id']);
                    if($products){
                        $this->load->model('catalog/product');
                        $mod_dimensionUnit = $this->length->getUnit($this->config->get('config_length_class_id'));
                        $mod_weightUnit = $this->weight->getUnit($this->config->get('config_weight_class_id'));
                        $mod_lan_cls_id = $this->config->get('config_length_class_id');
                        $mod_weg_cls_id = $this->config->get('config_weight_class_id');
                        foreach($products as $ky=>$prdct){

                            $prd_data = $this->db->query("SELECT length_class_id,weight_class_id,weight,length,width,height FROM " . DB_PREFIX . "product WHERE product_id = '" . $prdct['product_id'] . "'");
                            $length_class_id = (string) !empty($prd_data->row) ? $prd_data->row['length_class_id'] : '';
                            $weight_class_id = (string) !empty($prd_data->row) ? $prd_data->row['weight_class_id'] : '';
                            $prd_weg = (string) !empty($prd_data->row) ? $prd_data->row['weight'] : '';
                            $prd_len = (string) !empty($prd_data->row) ? $prd_data->row['length'] : '';
                            $prd_wid = (string) !empty($prd_data->row) ? $prd_data->row['width'] : '';
                            $prd_heig = (string) !empty($prd_data->row) ? $prd_data->row['height'] : '';
                            $prod_wei = $this->weight->getUnit($weight_class_id);
                            $prod_len = $this->length->getUnit($length_class_id);
                            if($prod_wei != $mod_weightUnit && !empty($prd_weg)){
                                //convert($value, $from, $to) only support cls_id
                            $products[$ky]['weight'] = round($this->weight->convert( $prd_weg, $weight_class_id, $this->config->get('config_weight_class_id') ), 2);
                            }
                            if($prod_len != $this->dimensionUnit){
                                $products[$ky]['length'] = round($this->length->convert( $prd_len, $length_class_id, $mod_lan_cls_id ), 2);
                                $products[$ky]['width'] = round($this->length->convert( $prd_wid, $length_class_id, $mod_lan_cls_id ), 2);
                                $products[$ky]['height'] = round($this->length->convert( $prd_heig, $length_class_id, $mod_lan_cls_id ), 2);
                            }
                            
                        }
                    }
            
                    $package = $model->get_ups_packages($products, $listProductDimension, $this);
                    $model->entryorder($package, $order_id['order_id'], $this);
                }
            }
            //load getSetting()
            $setting = $this->model_extension_upsmodule_base->getSetting();
            //check run transfer
            if (!isset($setting->ups_shipping_check_manage) || intval($setting->ups_shipping_check_manage) !== 1) {
                $this->transferMerchantInfo();
                //get bing map key
                if (empty($setting->ups_shipping_bing_map_key)) {
                    $this->model_extension_upsmodule_base->getBingMapCredential();
                }
            }
            $this->response->redirect(
                $this->url->link($constants->link_openorders, $token . '&sort=id&order=DESC&page=1', true)
            );
        } else {
            $this->response->redirect($this->url->link($constants->billingLink, $token, true));
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleBillingpreference getForm
     *
     * @return null
     */
    protected function getForm()
    {
        //Load base model
        $this->load->model($this->_base_model);
        $this->load->model($this->_opencart_model);
        $this->load->model($this->_country_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load model link
        $link = $constants->link_search_address_point; //get Constants
        $token = $constants->token;
        //get getListCheckDeliveryRates()
        $url = '';
        $list_delivery_rates = $this->model_extension_upsmodule_billingpreference->getListCheckDeliveryRates();
        foreach ($list_delivery_rates as $row) {
            if ($row[$constants->value] == 1) {
                $this->url->link($constants->link_billingpreference, $token, true);
            } else {
                $this->response->redirect($this->url->link($constants->link_deliveryrates, $token . $url, true));
            }
        }
        $data['action'] = $this->url->link(
            $constants->link_billingpreference . '/updateCheckBillingPreference',
            $token,
            true
        );

        $sql = "SELECT `Username` FROM " . DB_PREFIX . "upsmodule_shipping_license";
        $query = $this->db->query($sql);
        $userIds = "";
        if (isset($query->rows) && !empty($query->rows)) {
            foreach ($query->rows as $u_name) {
                $userIds .= (isset($u_name['Username']) && !empty($u_name['Username'])) ? $u_name['Username'] . ", " : "";
            }
        }
        // echo '<pre>';print_r($userIds);die();
        //S Translate
        $data['text_home'] = $this->language->get('text_home');
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data[$constants->text_billing_preference] = $this->language->get($constants->text_billing_preference);
        $data['text_Ycstar'] = $this->language->get('text_Ycstar');
        $data['text_Shipment_Bill'] = $this->language->get('text_Shipment_Bill');
        $data['text_Drop_Off'] = $this->language->get('text_Drop_Off');
        $data['text_neg_rate_head'] = $this->language->get('text_neg_rate_head');
        $data['text_neg_rate_p1'] = $this->language->get('text_neg_rate_p1') . " [ " . rtrim($userIds, ", ") . " ].";
        $data['text_neg_rate_p2'] = $this->language->get('text_neg_rate_p2') . " [ " . rtrim($userIds, ", ") . " ]. " . $this->language->get('text_neg_rate_p3');

        $ups_link = $this->model_extension_upsmodule_base->createUpsReferenceLink("preparing_to_ship");
        $link_pickup = '<a target="__blank" href="' . $ups_link . '">' . $ups_link . '</a>';

        $data['text_Pickup'] = sprintf($this->language->get('text_Pickup'), $link_pickup);
        $data['text_Searchpoint'] = $this->language->get('text_Searchpoint');

        $pickup_link = $this->model_extension_upsmodule_base->createUpsReferenceLink("pickup");
        $regular_link = '<a target="__blank" href="' . $pickup_link . '">' . $pickup_link . '</a>';
        $contact_link = $this->model_extension_upsmodule_base->createUpsReferenceLink("contact");
        $regular_link_1 = '<a target="__blank" href="' . $contact_link . '">';
        $regular_link_2 = "</a>";
        //get_admin_language
        $get_admin_language = $this->model_extension_upsmodule_opencartsetting->getAdminLanguage();
        $country_code = (object) $this->model_extension_upsmodule_country->getCountryCode();
        $data['ups_footer'] = "";
        if (strtolower($country_code->value) == "us") {
            $data['text_Regular_Ups'] = sprintf($this->language->get('text_Regular_Ups_US'), $regular_link);
            $data['text_Not_shipment'] = $this->language->get('text_Not_shipment_US');
            $data['text_Pickup'] = sprintf($this->language->get('text_Pickup_US'), $link_pickup);
            $data['text_Searchpoint'] = $this->language->get('text_Searchpoint_US');
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        } elseif (strtolower($country_code->value) != "pl") {
            $data['text_Regular_Ups'] = sprintf(
                $this->language->get('text_Regular_Ups'),
                $regular_link,
                $regular_link_1,
                $regular_link_2
            );
            $data['text_Not_shipment'] = sprintf($this->language->get('text_Not_shipment'), $regular_link_1, $regular_link_2);
        } else {
            $data['text_Regular_Ups'] = sprintf($this->language->get('text_Regular_Ups_PL'), $regular_link);
            $data['text_Not_shipment'] = $this->language->get('text_Not_shipment_PL');
        }
        $data['text_complete_configuration'] = $this->language->get('text_complete_configuration');
        $data['text_Print_Form'] = $this->language->get('text_Print_Form');
        //E translate
        $lang_billing = '';
        if (!isset($this->request->get['id'])) {
            $lang_billing = $this->language->get($constants->text_billing_preference);
        } else {
            $lang_billing = $this->language->get('text_Billingpreference_default');
        }
        $data[$constants->text_billing_preference] = $lang_billing;
        //S error
        if (isset($this->_error['warning'])) {
            $data['error_warning'] = $this->_error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        //E eroor
        if (isset($this->_error['name'])) {
            $data['error_name'] = $this->_error['name'];
        } else {
            $data['error_name'] = [];
        }
        if (isset($this->_error['meta_title'])) {
            $data['error_meta_title'] = $this->_error['meta_title'];
        } else {
            $data['error_meta_title'] = [];
        }
        //region URL
        $request = $this->request->get;
        $url = '';
        foreach ($request as $key => $val) {
            $url .= '&' .  $key . '=' . urlencode(html_entity_decode($val, ENT_QUOTES, 'UTF-8'));
        }
        //end
        $form_pickup_registration = "";
        $cod_registration = "";
        //check get_admin_language pl
        if (strtolower($country_code->value) != "pl") {
            $form_pickup_registration = '';
            $cod_registration = '';
        } else {
            if ($get_admin_language[$constants->value] == 'pl-PL') {
                $form_pickup_registration = 'view/image/upsmodule/PickUpRegistrationPL.pdf';
                $cod_registration = 'view/image/upsmodule/CODRegistrationPL.pdf';
            } else {
                $form_pickup_registration = 'view/image/upsmodule/PickUpRegistrationEN.pdf';
                $cod_registration = 'view/image/upsmodule/CODRegistrationEN.pdf';
            }
        }
        $data[$constants->user_token] = $this->session->data[$constants->user_token];
        //Load model language
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        $data['link'] = $link;
        $data['formPickUpRegistration'] = $form_pickup_registration;
        $data['codRegistration'] = $cod_registration;
        //end
        $data['billing_preference'] = $this->url->link(
            'extension/upsmodule/billingpreference&user_token=' .
                $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
                $this->session->data[$constants->user_token]
        );
        $this->response->setOutput($this->load->view($constants->link_billingpreference, $data));
    }

    /**
     * ControllerExtensionUpsmoduleBillingpreference transferMerchantInfo
     *
     * @param string $token //The token
     *
     * @return null
     */
    public function transferMerchantInfo()
    {
        //Load model
        $this->load->model($this->_plugin_manage_model);
        $this->model_extension_upsmodule_pluginmanage->transferMerchantInfo();
    }
}
