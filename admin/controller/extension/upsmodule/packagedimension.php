<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmodulePackagedimension file
 *
 * @category Packagedimension_Controller
 */

class ControllerExtensionUpsmodulePackagedimension extends Controller
{
    private $_error = [];
    private $_base_model = 'extension/upsmodule/base';
    private $_country_model = 'extension/upsmodule/country';
    private $_plugin_manage_model = 'extension/upsmodule/pluginmanage';

    /**
     * ControllerExtensionUpsmodulePackagedimension index
     *
     * @return null
     */
    public function index()
    {
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "packagedimension",
            "extension/upsmodule/packagedimension"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->language($constants->link_translate);
        $this->document->setTitle($this->language->get($constants->text_pkgdimension));
        $this->load->model($constants->link_packagedimension);
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension getForm
     *
     * @param string $array //The array
     *
     * @return null
     */
    protected function getForm($array = [])
    {
        $this->load->model($this->_base_model);
        $this->load->model($this->_country_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $list_check_cccessorial = $this->model_extension_upsmodule_packagedimension->getListCheckAccessorial();
        foreach ($list_check_cccessorial as $row) {
            if ($row['value'] == 1) {
                $this->url->link($constants->link_packagedimension, $constants->token, true);
            } else {
                $this->response->redirect(
                    $this->url->link('extension/upsmodule/cashondelivery', $constants->token, true)
                );
            }
        }
        $constants_error = false;
        if (!empty($array[$constants->error])) {
            $constants_error = $array[$constants->error];
        }
        $data[$constants->error] = $constants_error;
        $data['action'] = $this->url->link(
            'extension/upsmodule/packagedimension/addPackage&user_token=' .
            $this->session->data[$constants->user_token],
            true
        );
        $data['checkpackage'] = $this->url->link(
            'extension/upsmodule/packagedimension/checkPackage&user_token=' .
            $this->session->data[$constants->user_token],
            true
        );
        $data['checkpackage1'] = $this->url->link(
            'extension/upsmodule/packagedimension/checkPackage1&user_token=' .
            $this->session->data[$constants->user_token],
            true
        );
        $data['urlDelete'] = $this->url->link(
            'extension/upsmodule/packagedimension/deletePackage&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['urlGetPackage'] = $this->url->link(
            'extension/upsmodule/packagedimension/getPackageEdit&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['urlpackageEdit'] = $this->url->link(
            'extension/upsmodule/packagedimension/packageEdit&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['nextPage'] = $this->url->link(
            'extension/upsmodule/packagedimension/nextPage&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['pagePackage'] = $this->url->link(
            'extension/upsmodule/packagedimension&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $country_data = $this->model_extension_upsmodule_country->getCountryCode();
        $data['text_home'] = $this->language->get('text_home');
        $data[$constants->text_ups_module] = $this->language->get($constants->text_ups_module);
        $data['text_label_packagedimension'] = $this->language->get('text_label_packagedimension');
        $data['text_PkgWeightSize'] = $this->language->get('text_PkgWeightSize');
        $data['text_PkgDefault'] = $this->language->get('text_PkgDefault');
        $data['text_PkgAddNewPackage'] = $this->language->get('text_PkgAddNewPackage');
        $data['text_PkgPackageName'] = $this->language->get('text_PkgPackageName');
        $data['text_AddPackageWeight'] = $this->language->get('text_AddPackageWeight');
        $data['text_PkgUnit'] = $this->language->get('text_PkgUnit');
        $data['text_PkgKg'] = $this->language->get('text_PkgKg');
        $data['text_PkgPounds'] = $this->language->get('text_PkgPounds');
        $data['text_PkgDimension'] = $this->language->get('text_PkgDimension');
        $data['text_PkgExample'] = $this->language->get('text_PkgExample');
        $data['text_AddPackageLength'] = $this->language->get('text_AddPackageLength');
        $data['text_AddPackageWidth'] = $this->language->get('text_AddPackageWidth');
        $data['text_AddPackageHeight'] = $this->language->get('text_AddPackageHeight');
        $data['text_PkgCm'] = $this->language->get('text_PkgCm');
        $data['text_PkgInch'] = $this->language->get('text_PkgInch');
        $data['text_PkgAddPackage'] = $this->language->get('text_PkgAddPackage');
        $data['text_PkgEditing'] = $this->language->get('text_PkgEditing');
        $data['button_save'] = $this->language->get('button_save');
        $data['text_PkgOkToRemove'] = $this->language->get('text_PkgOkToRemove');
        $data['button_cancel'] = $this->language->get('button_cancel');
        $data[$constants->button_delete] = $this->language->get($constants->button_delete);
        $data['button_edit'] = $this->language->get('button_edit');
        $data['text_PkgRemove'] = $this->language->get('text_PkgRemove');
        $data['text_PkgDefaultPackage'] = $this->language->get('text_PkgDefaultPackage');
        $data['text_package_exist'] = $this->language->get('text_package_exist');
        $data['text_error'] = $this->language->get('text_error');

        $check_data = $this->model_extension_upsmodule_base->getPackageCheckData();

        $data['medium_weight'] = $check_data->medium_weight;
        $data['max_weight'] = $check_data->max_weight;
        $data['medium_size'] = $check_data->medium_size;
        $data['max_size'] = $check_data->max_size;
        $data['medium_length'] = $check_data->medium_length;
        $data['max_length'] = $check_data->max_length;

        $data['text_warning_title'] = $this->language->get('text_warning_title');
        $data['text_warning_weight'] = $check_data->message_weight_warning;
        $data['text_error_weight'] = $check_data->message_weight_error;
        $data['text_warning_size'] = $check_data->message_size_warning;
        $data['text_error_size'] = $check_data->message_size_error;
        $data['text_warning_size_exceeds'] = $check_data->message_size_exceed_warning;
        $data['text_error_size_exceeds'] = $check_data->message_size_exceed_error;

        $data['country_code'] = '';
        $data['ups_footer'] = "";
        if (!empty($country_data)) {
            $data['country_code'] = $country_data[$constants->value];
            if (strtolower($country_data[$constants->value]) == "us") {
                $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
            }
        }

        // Text for shipping service name from us
        $data['text_to_us'] = $this->language->get('text_to_us');
        $data['text_to_international'] = $this->language->get('text_to_international');
        // Get setting
        $data['ups_shipping_package_setting_type'] = $this->model_extension_upsmodule_base->getSettingByKey('ups_shipping_package_setting_type');
        $data['ups_shipping_package_setting_include_dimension'] = $this->model_extension_upsmodule_base->getSettingByKey('ups_shipping_package_setting_include_dimension');
        // Get data for package and backup rate
        $data['list_default_package'] = $listPackageDefault = $this->model_extension_upsmodule_packagedimension->listDefaultPackage();
        if (isset($listPackageDefault[0]) && empty($listPackageDefault[0]['package_item'])) {
            $listPackageDefault[0]['package_item'] = 1;
            $data['list_default_package'] = [];
            $data['list_default_package'][] = $listPackageDefault[0];
        }
        $data['list_product_dimension'] = $this->model_extension_upsmodule_packagedimension->listProductDimension();
        $data['list_backup_rate'] = $this->model_extension_upsmodule_packagedimension->listBackupRate();
        $data['list_shipping_service'] = $this->model_extension_upsmodule_packagedimension->listShippingService($data['country_code']);
        // Get currency
        $this->load->model('localisation/currency');
        $results = $this->model_localisation_currency->getCurrencies();
        foreach ($results as $result) {
            if ($result['code'] == $this->config->get('config_currency')) {
                $result['code'] = $result['code'] . $this->language->get($constants->text_default);
            }
            $currencyDefault = explode(' ', $result['code']);
            if (isset($currencyDefault[1]) == '(Default)') {
                $data['currency'] = $currencyDefault[0];
            }
            if (strlen($result['code']) > 3 && substr($result['code'], -9) == '(Default)') {
                $data['currency'] = substr($result['code'], 0, 3);
            }
            if (strlen($result['code']) > 3 && substr($result['code'], -12) == $constants->text_default) {
                $data['currency'] = substr($result['code'], 0, 3);
            }
        }
        $data['button_next'] = $this->language->get('button_next');
        $data['text_empty'] = $this->language->get('text_empty');
        $data['button_cancel'] = $this->language->get('button_cancel');
        $data['button_ok'] = $this->language->get('button_ok');
        $lang_package = '';
        $data['text_packagedimension'] = $this->language->get('text_packagedimension');
        if (isset($this->_error['warning'])) {
            $data['error_warning'] = $this->_error['warning'];
        } else {
            $data['error_warning'] = 'packagedimension';
        }
        //package settings

        $data['package_setting_pckalgorithm'] = $this->model_extension_upsmodule_base->getSettingByKey('package_setting_pckalgorithm');
        $data['package_setting_dim_unit'] = $this->model_extension_upsmodule_base->getSettingByKey('package_setting_dim_unit');
        $data['package_setting_weight_unit'] = $this->model_extension_upsmodule_base->getSettingByKey('package_setting_weight_unit');
        $data['max_weight_per_package'] = $this->model_extension_upsmodule_base->getSettingByKey('max_weight_per_package');


        //region URL
        $request = $this->request->get;
        $url = '';
        foreach ($request as $key => $val) {
            $url .= '&' .  $key . '=' . urlencode(html_entity_decode($val, ENT_QUOTES, $constants->utf));
        }
        //end
        $data[$constants->user_token] = $this->session->data[$constants->user_token];
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
//        $ss = 0;
//        if (isset($this->session->data['err'])) {
//            $ss = $this->session->data['err'];
//        }
//        $data[$constants->error] = $ss;
//        $this->session->data['err'] = 0;
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $this->response->setOutput($this->load->view($constants->link_packagedimension, $data));
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension checkPackageWeight
     *
     * @param object $data //The data
     *
     * @return $check
     */
    public function checkPackageWeight($data)
    {
        $this->load->model('extension/upsmodule/base');
        $this->load->language("extension/upsmodule/language");
        $check_data = $this->model_extension_upsmodule_base->getPackageCheckData();
        $check = false;
        $message = $this->language->get("text_error");

        $medium_weight = $check_data->medium_weight;
        $max_weight = $check_data->max_weight;
        $message_weight_warning = $check_data->message_weight_warning;
        $message_weight_error = $check_data->message_weight_error;

        switch ($data->unit_weight) {
            case "kgs":
                if (floatval($data->weight) <= 20) {
                    $check = true;
                    $message = "";
                } elseif (floatval($data->weight) > 20 && floatval($data->weight) <= 70) {
                    $check = true;
                    $message = $message_weight_warning;
                } else {
                    $check = false;
                    $message = $message_weight_error;
                }
                break;
            case "lbs":
                if (floatval($data->weight) <= $medium_weight) {
                    $check = true;
                    $message = "";
                } elseif (floatval($data->weight) > $medium_weight && floatval($data->weight) <= $max_weight) {
                    $check = true;
                    $message = $message_weight_warning;
                } else {
                    $check = false;
                    $message = $message_weight_error;
                }
                break;
            default:
                break;
        }
        $result = new \stdClass();
        $result->check = $check;
        $result->message = $message;

        return $result;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension checkPackageSize
     *
     * @param object $data //The data
     * @param string $unit //The unit
     *
     * @return $check
     */
    public function checkPackageSize($data)
    {
        $this->load->model('extension/upsmodule/base');
        $this->load->language("extension/upsmodule/language");
        $check_data = $this->model_extension_upsmodule_base->getPackageCheckData();
        $check = false;
        $message = $this->language->get("text_error");

        $medium_size = $check_data->medium_size;
        $max_size = $check_data->max_size;
        $medium_length = $check_data->medium_length;
        $max_length = $check_data->max_length;
        $message_size_warning = $check_data->message_size_warning;
        $message_size_error = $check_data->message_size_error;
        $message_size_exceed_warning = $check_data->message_size_exceed_warning;
        $message_size_exceed_error = $check_data->message_size_exceed_error;

        switch ($data->unit_dimension) {
            case "cm":
                $size = floatval($data->length) + 2 * floatval($data->width) + 2 * floatval($data->height);
                if ($size <= 330) {
                    $check = true;
                    $message = "";
                } elseif ($size > 330 && $size <= 400) {
                    $check = true;
                    $message = $message_size_warning;
                } else {
                    $check = false;
                    $message = $message_size_error;
                }
                break;
            case "inch":
                $check_size = false;
                $size = floatval($data->length) + 2 * floatval($data->width) + 2 * floatval($data->height);
                if ($size <= $medium_size) {
                    $check_size = true;
                    $message = "";
                } elseif ($size > $medium_size && $size <= $max_size) {
                    $check_size = true;
                    $message = $message_size_warning;
                } else {
                    $check_size = false;
                    $message = $message_size_error;
                }
                if (isset($check_data->country_code) && strtolower($check_data->country_code) == "us" && $check_size) {
                    $max_side = $this->getLongestSide($data);
                    if ($max_side <= $medium_length) {
                        $check = $check_size;
                        $message = $message . "";
                    } elseif ($max_side > $medium_length && $max_side <= $max_length) {
                        $check = $check_size;
                        if (!empty($message)) {
                            $message = $message_size_exceed_warning . '<br>' . $message;
                        } else {
                            $message = $message_size_exceed_warning;
                        }
                    } else {
                        $check = $check & $check_size;
                        $message = $message_size_exceed_error;
                    }
                } else {
                    $check = $check_size;
                }
                break;
            default:
                break;
        }
        $result = new \stdClass();
        $result->check = $check;
        $result->message = $message;

        return $result;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension getLongestSide
     *
     * @param string $package_data //The package_data
     *
     * @return $result
     */
    public function getLongestSide($package_data)
    {
        $max_side = floatval($package_data->length);
        if (floatval($package_data->width) > $max_side) {
            $max_side = floatval($package_data->width);
        }
        if (floatval($package_data->height) > $max_side) {
            $max_side = floatval($package_data->height);
        }
        return $max_side;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension validatePackageValue
     *
     * @param string $package //The package_data
     *
     * @return $result
     */
    public function validatePackageValue($package)
    {
        $this->load->language("extension/upsmodule/language");

        $check_package_name = true;
        $check_number_of_item = true;
        $error_message = '';
        $package_info = (object) $package;

        // Check number of item
        if (property_exists($package_info, 'number_of_item')) {
            if ($package_info->number_of_item <= 0 || !is_numeric($package_info->number_of_item)) {
                $check_number_of_item = false;
                $error_message = $this->language->get("text_error");
            }
        }

        // Check package name
        if (property_exists($package_info, 'package_name')) {
            if (empty(trim($package_info->package_name))) {
                $check_package_name = false;
                $error_message = $this->language->get("text_error");
            }
        }

        $validate_weight = $this->checkPackageWeight($package_info);
        $validate_size = $this->checkPackageSize($package_info);

        $check = ($check_number_of_item && $check_package_name && $validate_weight->check && $validate_size->check);

        $message = [];
        if (!$check) {
            $message = array_filter([$error_message, $validate_weight->message, $validate_size->message]);
        }

        $result = new \stdClass();
        $result->check = $check;
        $result->message = $message; //not use
        return $result;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension validateBackupRate
     *
     * @param string $rate //backup rate
     *
     * @return $result
     */
    public function validateBackupRate($rate)
    {
        $check = true;

        // Validate fallback rate
        if (trim($rate) == '') {
            $check = false;
        } else {
            if (!preg_match('/^\d+(\.\d{1,2})?$/', $rate)) {
                $check = false;
            } else {
                $rate = floatval($rate);
                if (($rate < 0) || ($rate > 9999.99)) {
                    $check = false;
                }
            }
        }
        return $check;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension addPackage
     *
     * @return null
     */
    public function addPackage()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();

        $this->document->setTitle($this->language->get($constants->text_ups_module));

        $request = $this->request->post;
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $validate = $this->validatePackageDimension();
            if ($validate->check == true) {
                $this->savePackageDimensionSetting();
                if ($request['btn_package'] == 'next') {
                    $this->response->redirect(
                        $this->url->link($constants->link_deliveryrates, $constants->token, true)
                    );
                } else {
                    $this->response->redirect(
                        $this->url->link($constants->link_packagedimension, $constants->token, true)
                    );
                }
            } else {
                $array[$constants->error] = 1;
                $array[$constants->message] = $validate->message;
                $this->getForm($array);
            }
        }
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension addSavePackage
     *
     * @param string $check //The check
     *
     * @return null
     */
    public function savePackageDimensionSetting()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $post_data = $this->request->post;
        if ($post_data['package_setting_option'] == 1) {
            $this->model_extension_upsmodule_packagedimension->addPackageDefault($post_data['default_package']);
        } elseif ($post_data['package_setting_option'] == 2) {
            $this->model_extension_upsmodule_packagedimension->addProductDimension($post_data['product_dimension']);
            $this->model_extension_upsmodule_packagedimension->addFallbackRate($post_data['backup_rate']);
            // $this->model_extension_upsmodule_base->saveOptionSetting(
            //     'ups_shipping_package_setting_include_dimension',
            //     $post_data['include_dimension']
            // );
            $this->model_extension_upsmodule_base->saveOptionSetting(
                'package_setting_pckalgorithm',
                $post_data['package_setting_pckalgorithm']
            );
            $this->model_extension_upsmodule_base->saveOptionSetting(
                'package_setting_dim_unit',
                $post_data['package_setting_dim_unit']
            );
            $this->model_extension_upsmodule_base->saveOptionSetting(
                'package_setting_weight_unit',
                $post_data['package_setting_weight_unit']
            );
            $this->model_extension_upsmodule_base->saveOptionSetting(
                'max_weight_per_package',
                $post_data['max_weight_per_package']
            );
        }
        $this->model_extension_upsmodule_base->saveOptionSetting(
            'ups_shipping_package_setting_type',
            $post_data['package_setting_option']
        );
        $this->load->model($this->_base_model);
        $setting = $this->model_extension_upsmodule_base->getSetting();
        if (isset($setting->ups_shipping_check_manage) && $setting->ups_shipping_check_manage == 1) {
            $this->transferDefaultPackage();
        }
        $this->model_extension_upsmodule_packagedimension->updateCheckPackageDimension();
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension validatePackageDimension
     *
     * @param string $check //The check
     *
     * @return null
     */
    public function validatePackageDimension()
    {
        $this->load->language("extension/upsmodule/language");
        $validateResult = new stdClass();
        $validateResult->check = true;
        $validateResult->message = [];
        $post_data = $this->request->post;
        if ($post_data['package_setting_option'] == 1) {
            foreach ($post_data['default_package'] as $defaultPackage) {
                $validateRow = $this->validatePackageValue($defaultPackage);
                if ($validateRow->check == false) {
                    $validateResult->check = false;
                    $validateResult->message = array_merge($validateResult->message, $validateRow->message);
                }
            }
        } elseif ($post_data['package_setting_option'] == 2) {
            foreach ($post_data['product_dimension'] as $productDimension) {
                // $validateRow = $this->validatePackageValue($productDimension);
                // if ($validateRow->check == false) {
                //     $validateResult->check = false;
                //     $validateResult->message = array_merge($validateResult->message, $validateRow->message);
                // }
            }
            $listServiceId = [];
            $checkBackupRate = true;
            foreach ($post_data['backup_rate'] as $backupRate) {
                if (in_array($backupRate['service_id'], $listServiceId)) {
                    $checkBackupRate = false;
                }
                $checkRow = $this->validateBackupRate($backupRate['rate']);
                if ($checkRow == false) {
                    $checkBackupRate = false;
                }
                $listServiceId[] = $backupRate['service_id'];
            }
            if ($checkBackupRate == false) {
                $validateResult->check = false;
                $validateResult->message[] = $this->language->get("text_error");
            }
        }
        $validateResult->message = implode("<br/>", array_unique($validateResult->message)); //not use
        return $validateResult;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension deletePackage
     *
     * @return null
     */
    public function deletePackage()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $request = $this->request->post;
        $checkSQL = $this->model_extension_upsmodule_packagedimension->deletePackage($request[$constants->package_id]);
        if ($checkSQL == true) {
            $check = true;
        } else {
            $check = false;
        }
        echo json_encode([$check]);
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension getPackageEdit
     *
     * @return null
     */
    public function getPackageEdit()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $request = $this->request->post;
        $checkSQL = $this->model_extension_upsmodule_packagedimension->getPackage($request[$constants->package_id]);
        $checkSQL->row[$constants->package_name] = html_entity_decode($checkSQL->row[$constants->package_name]);
        echo json_encode($checkSQL);
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension packageEdit
     *
     * @return null
     */
    public function packageEdit()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $session = $this->session->data;
        $request = $this->request->post;
        $data = [
            $constants->package_id => $request[$constants->package_id],
            $constants->package_name => $request[$constants->package_name_value],
            'weight' => $request['weight'],
            'unit_weight' => $request['weightunit'],
            'length' => $request['length'],
            'width' => $request['width'],
            'height' => $request['height'],
            'unit_dimension' => $request['lengthunit'],
        ];
        $validate = $this->validatePackageValue($request);
        if ($validate->check) {
            $checkSQL = $this->model_extension_upsmodule_packagedimension->editPackage($data, $session);
            //region send API Manage
            $check_package_default = $this->model_extension_upsmodule_packagedimension->checkPackage(
                $request[$constants->package_name_value]
            );
            if ($check_package_default) {
                $this->load->model($this->_base_model);
                $setting = $this->model_extension_upsmodule_base->getSetting();
                if (isset($setting->ups_shipping_check_manage) && $setting->ups_shipping_check_manage == 1) {
                    $this->transferDefaultPackage();
                }
            }
            //end Send
        } else {
            $array[$constants->error] = 1;
            $array[$constants->message] = $validate->message;
            $this->getForm($array);
        }
        //region URL
        $url = '';
        foreach ($request as $key => $val) {
            $url .= '&' .  $key . '=' .
                urlencode(html_entity_decode($val, ENT_QUOTES, $constants->utf));
        }
        //end
        $this->response->redirect($this->url->link($constants->link_packagedimension, $constants->token, true));
        if ($checkSQL == true) {
            $check = true;
        } else {
            $check = false;
        }
        echo json_encode([$check]);
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension nextPage
     *
     * @return null
     */
    public function nextPage()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $check_empty = $this->model_extension_upsmodule_packagedimension->countPkg();
        if ($check_empty['total'] >= 1) {
            $this->response->redirect($this->url->link('extension/upsmodule/deliveryrates', $constants->token, true));
        } else {
            $this->session->data['err'] = 1;
            $this->response->redirect($this->url->link($constants->link_packagedimension, $constants->token, true));
        }
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension checkPackage
     *
     * @return null
     */
    public function checkPackage()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $request = $this->request->post;
        $check_package = $this->checkPackage3($request[$constants->name_package], $request[$constants->package_id]);
        echo $check_package;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension checkPackage1
     *
     * @return null
     */
    public function checkPackage1()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $request = $this->request->post;
        $check = $this->checkPackage2($request[$constants->name_package]);
        echo $check;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension checkPackage2
     *
     * @param string $namePackage //The namePackage
     *
     * @return null
     */
    public function checkPackage2($namePackage)
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $check_package = $this->model_extension_upsmodule_packagedimension->checkNameExits($namePackage);
        $response = 0;
        if (count($check_package) == 0) {
            $response = 0;
        } else {
            foreach ($check_package as $item) {
                if (empty($item[$constants->package_name])) {
                    $response = 0;
                }
                if (strtolower($item[$constants->package_name]) == strtolower($namePackage)) {
                    $response = 1;
                    break;
                } else {
                    $response = 0;
                }
            }
        }
        return $response;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension checkPackage3
     *
     * @param string $namePackage //The namePackage
     * @param int    $idPackage   //The idPackage
     *
     * @return null
     */
    public function checkPackage3($namePackage, $idPackage)
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_packagedimension);
        $check_package = $this->model_extension_upsmodule_packagedimension->checkNameExits($namePackage, $idPackage);
        $response = 0;
        if (count($check_package) == 0) {
            $response = 0;
        } else {
            foreach ($check_package as $item) {
                if (empty($item[$constants->package_name])) {
                    $response = 0;
                }
                if (strtolower($item[$constants->package_name]) == strtolower($namePackage)
                    && $item[$constants->package_id] != $idPackage
                ) {
                    $response = 1;
                    break;
                } else {
                    $response = 0;
                }
            }
        }
        return $response;
    }

    /**
     * ControllerExtensionUpsmodulePackagedimension transferDefaultPackage
     *
     * @return null
     */
    public function transferDefaultPackage()
    {
        $this->load->model($this->_plugin_manage_model);
        $this->model_extension_upsmodule_pluginmanage->transferDefaultPackage();
    }
}
