<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleDeliveryrates file
 *
 * @category Deliveryrates_Controller
 */

class ControllerExtensionUpsmoduleDeliveryrates extends Controller
{
    private $_base_model = 'extension/upsmodule/base';
    private $_country_model = 'extension/upsmodule/country';
    private $_plugin_manage_model = 'extension/upsmodule/pluginmanage';

    /**
     * ControllerExtensionUpsmoduleDeliveryrates index
     *
     * @return null
     */
    public function index()
    {
        //load _base_model
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "deliveryrates",
            "extension/upsmodule/deliveryrates"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load link_translate
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->text_deliveryrates));
        //load mode link_deliveryrates
        $this->load->model($constants->link_deliveryrates);
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleDeliveryrates updateDeliveryRates
     *
     * @return null
     */
    public function updateDeliveryRates()
    {
        //load _base_model
        $this->load->model($this->_base_model);
        //load mode listConstanst
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load link_translate
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->text_deliveryrates));
        //load mode link_deliveryrates
        $this->load->model($constants->link_deliveryrates);
        //load session
        $session = $this->session->data;
        //load request
        $request = $this->request->post;
        //check request
        if ($this->request->server['REQUEST_METHOD'] == 'POST') {
            $this->model_extension_upsmodule_base->saveOptionSetting('ups_shipping_tax_class_id',$request['upsshipping_tax_class_id']);
            //updateDeliveryRates
            $this->model_extension_upsmodule_deliveryrates->updateDeliveryRates($this->request->post, $session);
            //updateCheckDeliveryRates
            $this->model_extension_upsmodule_deliveryrates->updateCheckDeliveryRates();
            //region send API Manage
            //load _base_model
            $this->load->model($this->_base_model);
            //getSetting
            $setting = $this->model_extension_upsmodule_base->getSetting();
            //check getSetting
            if (isset($setting->ups_shipping_check_manage) && $setting->ups_shipping_check_manage == 1) {
                $this->transferDeliveryRates();
            }
            //end
            //check request
            if (isset($request['method']) && $request['method'] == 'next') {
                $this->response->redirect(
                    $this->url->link('extension/upsmodule/billingpreference', $constants->token, true)
                );
            } else {
                $this->response->redirect($this->url->link($constants->link_deliveryrates, $constants->token, true));
            }
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleDeliveryrates checkLink
     *
     * @return $checkLink
     */
    protected function checkLink()
    {
        //load _base_model
        $this->load->model($this->_base_model);
        //load mode listConstanst
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $check_link = '';
        //getListCheckPackageDimension
        $listPackageDimension = $this->model_extension_upsmodule_deliveryrates->getListCheckPackageDimension();
        //check listPackageDimension
        foreach ($listPackageDimension as $row) {
            //$constants->valuedeli
            if ($row[$constants->valuedeli] == 1) {
                $check_link = $this->url->link($constants->link_deliveryrates, $constants->token, true);
            } else {
                $check_link = $this->response->redirect(
                    $this->url->link('extension/upsmodule/packagedimension', $constants->token, true)
                );
            }
        }
        return $check_link;
    }

    /**
     * ControllerExtensionUpsmoduleDeliveryrates getDefaultFlat
     *
     * @return $checkDefaultFlat
     */
    protected function getDefaultFlat()
    {
        //load _base_model
        $this->load->model($this->_base_model);
        //load mode listConstanst
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //getListSetting
        $list_delivery_rate = $this->model_extension_upsmodule_deliveryrates->getListSetting();
        $list_delivery_rates = [];
        //check listDeliveryRate
        foreach ($list_delivery_rate as $key => $value) {
            if ($value['rate_type'] == 'real_time') {
                $value['delivery_rate'] = 0;
            }
            $list_delivery_rates[$value['service_id']][$value['id']] = $value;
        }
        $checkDefaultFlat = $list_delivery_rates;
        return $checkDefaultFlat;
    }

    /**
     * ControllerExtensionUpsmoduleDeliveryrates getDefaultReal
     *
     * @return $checkDefaultReal
     */
    protected function getDefaultReal()
    {
        //load _base_model
        $this->load->model($this->_base_model);
        //load mode listConstanst
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //getListService
        $list_service = $this->model_extension_upsmodule_deliveryrates->getListService();
        $list_service_format = [];
        //check listService
        foreach ($list_service as $key => $value) {
            //check $value['rate_type']
            if ($value['rate_type'] != 'real_time') {
                $value['value_real_time'] = 100;
            } else {
                $value['value_real_time'] = $value['delivery_rate'];
            }
            $list_service_format[] = $value;
        }
        $checkDefaultReal = $list_service_format;
        return $checkDefaultReal;
    }

    /**
     * ControllerExtensionUpsmoduleDeliveryrates getForm
     *
     * @return null
     */
    protected function getForm()
    {
        //load _base_model
        $this->load->model($this->_base_model);
        $this->load->model($this->_country_model);
        //load mode listConstanst
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $country_data = $this->model_extension_upsmodule_country->getCountryCode();
        $country_code = '';
        if (!empty($country_data) && isset($country_data[$constants->value])) {
            $country_code = $country_data[$constants->value];
        }
        //call checkLink
        $this->checkLink();
        //check AP ADD
        $check_list_ap = $this->model_extension_upsmodule_deliveryrates->getCheckListAp();
        $check_list_add = $this->model_extension_upsmodule_deliveryrates->getCheckListAdd();
        $data['checkListADD'] = $check_list_add[$constants->valuedeli];
        $data = [];
        $check_value_default_flat = $this->getDefaultFlat();
        $data['listDeliveryRates'] = $check_value_default_flat;
        $check_value_default_real = $this->getDefaultReal();
        $data['listServices'] = $check_value_default_real;

        $this->load->model('localisation/currency');
        $results = $this->model_localisation_currency->getCurrencies();
        $result_currency = null;
        foreach ($results as $result) {
            if ($result['code'] == $this->config->get('config_currency')) {
                $result_currency = $this->language->get($constants->text_default);
            }
            $data['currencies'][] = [
                'title' => $result['title'] . $result_currency,
                'code' => $result['code']
            ];
            if ($result['code'] == $this->config->get('config_currency')) {
                $result['code'] = $result['code'] . $this->language->get($constants->text_default);
            }
            $currencyDefault = [];
            $currencyDefault = explode(' ', $result['code']);
            if (isset($currencyDefault[1]) == '(Default)') {
                $data[$constants->currency] = $currencyDefault[0];
            }
            if (strlen($result['code']) > 3 && substr($result['code'], -9) == '(Default)') {
                $data[$constants->currency] = substr($result['code'], 0, 3);
            }
            if (strlen($result['code']) > 3 && substr($result['code'], -12) == $constants->text_default) {
                $data[$constants->currency] = substr($result['code'], 0, 3);
            }
        }

        $setting = $this->model_extension_upsmodule_base->getSetting();
        $this->load->model('localisation/tax_class');
		$data['tax_classes'] = $this->model_localisation_tax_class->getTaxClasses();
        $data['ups_shipping_tax_class_id'] = $setting->ups_shipping_tax_class_id;

        $data['action'] = $this->url->link(
            'extension/upsmodule/deliveryrates/updateDeliveryRates',
            $constants->token,
            true
        );
        $data['text_home'] = $this->language->get('text_home');
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data[$constants->link_translate] = $this->language->get($constants->link_translate);
        $data['text_currency'] = $this->language->get('text_currency');
        $data['text_shipto'] = $this->language->get('text_shipto');
        $data['text_shipform'] = $this->language->get('text_shipform');
        $data['text_flat_rates'] = $this->language->get('text_flat_rates');
        $data['text_real_time'] = $this->language->get('text_real_time');
        $data['text_threshold'] = $this->language->get('text_threshold');
        $data['text_deli_is'] = $this->language->get('text_deli_is');
        $data['text_shipping_rates'] = $this->language->get('text_shipping_rates');
        $data['text_deli_shopper'] = $this->language->get('text_deli_shopper');
        $data['text_error_duplicate'] = $this->language->get('text_error_duplicate');
        $data['text_error'] = $this->language->get('text_error');
        $data['text_please_note'] = $this->language->get('text_please_note');
        $data['ups_footer'] = "";
        $data['country_code'] = $country_code;
        if (!empty($country_code) && strtolower($country_code) == "us") {
            $data['text_deli_shopper'] = $this->language->get('text_deli_shopper_US');
            $data['text_please_note'] = $this->language->get('text_please_note_US');
            $data['text_shipto'] = $this->language->get('text_shipto_US');
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        }
        $data['text_real'] = $this->language->get('text_real');
        $url = '';
        //Button
        $data['button_save'] = $this->language->get('button_save');
        $data['button_next'] = $this->language->get('button_next');
        $data[$constants->user_token] = $this->session->data[$constants->user_token];
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['checkListAP'] = $check_list_ap[$constants->valuedeli];
        $data['checkListADD'] = $check_list_add[$constants->valuedeli];
        $data['shipping_rate'] = $this->url->link(
            'extension/upsmodule/deliveryrates&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $this->load->model('localisation/country');
        $data['countries'] = $this->model_localisation_country->getCountries();
        $this->response->setOutput($this->load->view($constants->link_deliveryrates, $data));
    }

    /**
     * ControllerExtensionUpsmoduleDeliveryrates transferDeliveryRates
     *
     * @return null
     */
    public function transferDeliveryRates()
    {
        $this->load->model($this->_plugin_manage_model);
        $this->model_extension_upsmodule_pluginmanage->transferDeliveryRates();
    }
}
