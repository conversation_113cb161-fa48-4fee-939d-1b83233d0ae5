<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleAccessorial file
 *
 * @category Accessorial_Controller
 */

class ControllerExtensionUpsmoduleAccessorial extends Controller
{
    private $_error = [];
    private $_base_model = 'extension/upsmodule/base';
    private $_country_model = 'extension/upsmodule/country';
    /**
     * ControllerExtensionUpsmoduleAccessorial index
     *
     * @return null
     */
    public function index()
    {
        //load object
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "accessorial",
            "extension/upsmodule/accessorial"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load language
        $this->load->language($constants->link_translate);
        //load set title
        $this->document->setTitle($this->language->get($constants->txt_accessorial));
        //load model link access
        $this->load->model($constants->link_accessorial);
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleAccessorial getForm
     *
     * @return null
     */
    protected function getForm()
    {
        //load object
        $this->load->model($this->_base_model);
        $this->load->model($this->_country_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        //load link
        $data['action'] = $this->url->link('extension/upsmodule/accessorial/add', $constants->token, true);
        //load text translate
        $data['text_home'] = $this->language->get('text_home');
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data['text_label_accessorial'] = $this->language->get('text_label_accessorial');
        if (!isset($this->request->get['id'])) {
            $data[$constants->txt_accessorial] = $this->language->get($constants->txt_accessorial);
        } else {
            $data[$constants->txt_accessorial] = $this->language->get('text_accessorial_default');
        }
        $data['text_enabled'] = $this->language->get('text_enabled');
        $data['text_disabled'] = $this->language->get('text_disabled');
        $data['text_none'] = $this->language->get('text_none');
        $data['help_category'] = $this->language->get('help_category');
        $data['help_intro_text'] = $this->language->get('help_intro_text');
        $data['additional_handling'] = $this->language->get('additional_handling');
        $data['quantum_view_ship_notification'] = $this->language->get('quantum_view_ship_notification');
        $data['quantum_view_delivery_notification'] = $this->language->get('quantum_view_delivery_notification');
        $data['residential_address'] = $this->language->get('residential_address');
        $data['saturday_delivery'] = $this->language->get('saturday_delivery');
        $data['carbon_neutral'] = $this->language->get('carbon_neutral');
        $data['direct_delivery_only'] = $this->language->get('direct_delivery_only');
        $data['declared_value'] = $this->language->get('declared_value');
        $data['signature_required'] = $this->language->get('signature_required');
        $data['adult_signature_required'] = $this->language->get('adult_signature_required');
        $data['to_access_point_cod'] = $this->language->get('UPS_ACSRL_ACCESS_POINT_COD');
        $data['to_home_cod'] = $this->language->get('UPS_ACSRL_TO_HOME_COD');
        //Column
        $data['column_title'] = $this->language->get('column_title');
        $data['column_description'] = $this->language->get('column_description');
        $data['column_action'] = $this->language->get('column_action');
        //Button
        $data['button_save'] = $this->language->get('button_save');
        $data['button-next'] = $this->language->get('button_next');
        $data['tab_general'] = $this->language->get('tab_general');
        $data['continue'] = $this->language->get('continue');
        //check error
        if (isset($this->_error['warning'])) {
            $data['error_warning'] = $this->_error['warning'];
        } else {
            $data['error_warning'] = '';
        }
        $url = '';
        //region accessorial
        //load model access
        $accessorials = $this->model_extension_upsmodule_accessorial->getListAccessorial();
        $arr_accessorials = [];
        foreach ($accessorials as $key => $val) {
            $val['translate_pl'] = $this->language->get($val['accessorial_key']);
            $arr_accessorials[$key] = $val;
        }
        //end
        //check error name
        if (isset($this->_error['name'])) {
            $data['error_name'] = $this->_error['name'];
        } else {
            $data['error_name'] = [];
        }
        //check error title
        if (isset($this->_error['meta_title'])) {
            $data['error_meta_title'] = $this->_error['meta_title'];
        } else {
            $data['error_meta_title'] = [];
        }
        //region URL
        //post request
        $request = $this->request->get;
        $url = '';
        $example_info = '';
        foreach ($request as $key => $val) {
            $url .= '&' .  $key . '=' . urlencode(html_entity_decode($val, ENT_QUOTES, 'UTF-8'));
        }
        //end
        //get data
        $data[$constants->user_token] = $this->session->data[$constants->user_token];
        //get model
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        //link
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        $data['accessorials'] = $arr_accessorials;
        //end
        //add footer for us
        $country_data = (object) $this->model_extension_upsmodule_country->getCountryCode();
        $data['ups_footer'] = "";
        if (!empty($country_data) && strtolower($country_data->value) == "us") {
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
        }
        $this->response->setOutput($this->load->view($constants->link_accessorial, $data));
    }

    /**
     * ControllerExtensionUpsmoduleAccessorial add
     *
     * @return null
     */
    public function add()
    {
        //load object
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $url = '';
        //load language
        $this->load->language($constants->link_translate);
        //load set title
        $this->document->setTitle($this->language->get('heading_title'));
        //load model link
        $this->load->model($constants->link_accessorial);
        $request = $this->request->post;
        $listAccessorialService = $this->model_extension_upsmodule_accessorial->getListAccessorial();
        //check update
        foreach ($listAccessorialService as $row) {
            $showShipping = (isset($request[$row['accessorial_key']])) ? 1 : 0;
            $this->model_extension_upsmodule_accessorial->updateListAccessorial($row['id'], $showShipping);
            $this->model_extension_upsmodule_accessorial->updateCheckAccessorial();
        }
        //check button next
        if (isset($request['btnNext'])) {
            $this->response->redirect(
                $this->url->link('extension/upsmodule/packagedimension', $constants->token, true)
            );
        } else {
            $this->response->redirect($this->url->link('extension/upsmodule/accessorial', $constants->token, true));
        }
    }
}
