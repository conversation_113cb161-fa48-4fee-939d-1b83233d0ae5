<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleCountry file
 *
 * @category Country_Controller
 */

// $this->response->redirect($this->url->link($constants->link_account, $token, true));

class ControllerExtensionUpsmoduleStatus extends Controller
{
    private $_error = [];
    private $_base_model = 'extension/upsmodule/base';

    /**
     * ControllerExtensionUpsmoduleCountry index
     *
     * @return null
     */
    public function index()
    {
        //Load Model
        $this->load->model($this->_base_model);

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        //load language
        $this->load->language($constants->link_translate);
        //load setTitle
        $this->document->setTitle($this->language->get($constants->text_country));
        
        $this->url->link($constants->link_status, $token, true);
        
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleCountry getForm
     *
     * @return null
     */
    protected function getForm()
    {
        //Load model
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $token = $constants->token;
        //link
        $data['user_token'] = $this->session->data[$constants->user_token];
        $this->load->model('localisation/language');
        $data['languages'] = $this->model_localisation_language->getLanguages();
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');

        // text
        $data['text_ssl'] = $this->language->get('text_ssl');
        $data['text_maintenance'] = $this->language->get('text_maintenance');
        $data['text_settings'] = $this->language->get('text_settings');
        $data['text_status'] = $this->language->get('text_status');
        $data['tooltip'] = '';

        $status_green = '<i class="fa fa-check-circle-o" style="font-size:36px;color:green"></i>&nbsp &nbsp&nbsp &nbsp&nbsp&nbsp&nbsp &nbsp &nbsp &nbsp';
        $status_red = '<i class="fa fa fa-minus-circle" style="font-size:36px;color:red"></i>&nbsp &nbsp <a href=""><i class="fa fa-question-circle" style="font-size:15px"> Help</i></a>';

        // SSL Check
        $data['ssl_status'] = $status_green;
        $value_1 = $value_2 = $value_3 = $value_4 = $value_5 = true;
        if (!isset($_SERVER['HTTPS']) || $_SERVER['HTTPS'] != 'on') {
            $data['ssl_status'] = $status_red;
            $value_1 = false;
        }

        

        // maintenance mode check
        $data['maintenance_status'] = $status_green;
        
        if($this->config->get('config_maintenance')){
            $data['maintenance_status'] = $status_red;
            $value_2 = false;
        }

        // check URL status
        $data['url_1'] = $data['url_2'] = $data['url_3'] = $status_green;
        
        $url_1 = 'https://fa-ecptools-prd.azurewebsites.net';
        $url_2 = 'https://onlinetools.ups.com';
        $url_3 = 'https://fa-ecpanalytics-prd.azurewebsites.net';
        $res1 = $this->exec_curl($url_1);
        $res2 = $this->exec_curl($url_2);
        $res3 = $this->exec_curl($url_3);
        
        if($res1 != '200'){
            $data['url_1'] = $status_red;
            $value_3 = false;
        }

        if($res2 != '200'){
            $data['url_2'] = $status_red;
            $value_4 = false;
        }

        if($res3 != '200'){
            $data['url_3'] = $status_red;
            $value_5 = false;
        }
        // $title = '';
        // $data['tooltip'] = 'data-toggle="tooltip" title="'.$title.'"';

        if($value_1 && $value_2 && $value_3 && $value_4 && $value_5){
            $this->response->redirect($this->url->link($constants->link_country, $token, true));
        }
       
        $this->response->setOutput($this->load->view($constants->link_status, $data));
    }


    public function exec_curl($url){

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_USERAGENT, "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.0)");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,false);
        curl_setopt($ch, CURLOPT_MAXREDIRS, 10);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
        curl_setopt($ch, CURLOPT_TIMEOUT, 20);
        $rt = curl_exec($ch);
        $info = curl_getinfo($ch);
        return $info["http_code"];
    }

   
}
