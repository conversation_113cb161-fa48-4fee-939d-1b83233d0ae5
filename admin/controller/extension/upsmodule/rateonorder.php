<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleAccountsuccess file
 *
 * @category Accountsuccess_Controller
 */

class ControllerExtensionUpsmoduleRateonorder extends Controller
{
	private $_base_model = 'extension/upsmodule/base';
	public function getRates()
	{
		$data = $this->request->post;
		if (isset($data['order_id']) && !empty($data['order_id'])) {
			$this->load->model('sale/order');
			$order_data = $this->model_sale_order->getOrder($data['order_id']);
			$order_totals = $this->model_sale_order->getOrderTotals($data['order_id']);
			$this->load->model('extension/upsmodule/base');
			$constants = $this->model_extension_upsmodule_base->listConstanst();
			
			$order_sub_total = 0;
			foreach ($order_totals as $total) {
				if (isset($total['code']) && $total['code'] == "sub_total") {
					$order_sub_total = $total['value'];
				}
			}
			$this->load->model('extension/upsmodule/rateonorder');
			$configs = $this->model_extension_upsmodule_rateonorder->getConfigs();

			$this->load->model('extension/upsmodule/account');
            $def_acc = $this->model_extension_upsmodule_account->getAccountDefault();

			$rate_req_data = [];
	        $alternate_delivery_address = $constants->alternate_delivery_address;
	        $rate_req_data["ShippingType"]  = "ADD";
	        $rate_req_data["Typerate"]  = "createshipment";
	        $rate_req_data["Request"]["RequestOption"]  = "Shoptimeintransit";
	        //Date now
	        $date = $this->model_extension_upsmodule_rateonorder->getDateTime();
	        $cut_off_time = (int)($configs->ups_shipping_cut_off_time . '00');
	        $h_now = (int)date('Hi', strtotime($date));
	        $time = date('Ymd', strtotime($date));
	        if ($h_now > $cut_off_time) { // if cut off time > current time, add 1 day
	            $time = date('Ymd', strtotime(date("Y-m-d", strtotime($date))." +1 day"));
	        }
	        $rate_req_data["DeliveryTimeInformation"]["Pickup"]["Date"]  =  $time;

	        //shipper
	        $rate_req_data[$constants->shipper][$constants->name_data] = $def_acc['fullname'];
	        $rate_req_data[$constants->shipper][$constants->shipper_number] = $def_acc[$constants->ups_account_number];
	        $rate_req_data[$constants->shipper][$constants->address_data][$constants->address_line] = [$def_acc[$constants->address_1],
	        $def_acc[$constants->address_2], $def_acc[$constants->address_3]];
	        $rate_req_data[$constants->shipper][$constants->address_data]["City"] = $def_acc['city'];
	        $rate_req_data[$constants->shipper][$constants->address_data][$constants->province_code_acc] = $def_acc['state_province_code'];
	        $rate_req_data[$constants->shipper][$constants->address_data][$constants->postal_code] = $def_acc['post_code'];
	        $rate_req_data[$constants->shipper][$constants->address_data][$constants->country_code_acc] = $def_acc['country'];
	        //ship_from
	        $rate_req_data[$constants->ship_from_data][$constants->name_data] = $def_acc['fullname'];
	        $rate_req_data[$constants->ship_from_data][$constants->shipper_number] =  $def_acc[$constants->ups_account_number];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data][$constants->address_line] = [$def_acc[$constants->address_1],
	        $def_acc[$constants->address_2], $def_acc[$constants->address_3]];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data]["City"] = $def_acc['city'];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data][$constants->province_code_acc] = $def_acc['state_province_code'];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data][$constants->postal_code] = $def_acc['post_code'];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data][$constants->country_code_acc] = $def_acc['country'];
	        //ship_to
	        $rate_req_data[$constants->ship_to_data][$constants->name_data] = $order_data['shipping_firstname'] . ' ' . $order_data['shipping_lastname'];
	        $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->address_line] = [$order_data['shipping_address_1'], $order_data['shipping_address_2'], ""];
	        $rate_req_data[$constants->ship_to_data][$constants->address_data]["City"] = $order_data['shipping_city'];
	        if (!empty($order_data['shipping_zone_code'])) {
	            $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->province_code_acc] = $order_data['shipping_zone_code'];
	        } else {
	            $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->province_code_acc] = "XX";
	        }
	        $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->postal_code] = $order_data['shipping_postcode'];
	        $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->country_code_acc] = $order_data['shipping_iso_code_2'];

	        $rate_req_data["InvoiceLineTotal"]["CurrencyCode"] =  $order_data['currency_code'];
        	$rate_req_data["InvoiceLineTotal"]["MonetaryValue"] = $order_sub_total;

        	$rate_req_data["PaymentDetails"]["ShipmentCharge"] = [
	            "Type" => "01",
	            "BillShipper" => [
	                "AccountNumber" => $def_acc[$constants->ups_account_number]
	            ]
	        ];

            $order_packs = $this->model_extension_upsmodule_rateonorder->getPackageByOrder($data['order_id']);
            $packages = (isset($order_packs['package']) && !empty($order_packs['package'])) ? json_decode($order_packs['package'], true) : [];
            if (!empty($packages)) {
            	foreach ($packages as $p) {
			if(isset($p['length']) && isset($p['length']) && isset($p['height'])){
            		$rate_req_data["Package"][] =
	                [
	                    "Dimensions" => [
	                        "UnitOfMeasurement" => [
	                            "Code" => strtoupper($p['unit_dimension']),
	                            "Description" => $this->transfer($p['unit_dimension'])
	                        ],
	                        "Length" => strval($p['length']),
	                        "Width" => strval($p['width']),
	                        "Height" => strval($p['height'])
	                    ],
	                    "PackageWeight" => [
	                        "UnitOfMeasurement" => [
	                            "Code" => strtoupper($p['unit_weight']),
	                            "Description" => $this->transfer($p['unit_weight'])
	                        ],
	                        "Weight" => strval($p['weight'])
	                    ]
	                ];
				}else{
					$rate_req_data["Package"][] =
	                [
						"PackageWeight" => [
	                        "UnitOfMeasurement" => [
	                            "Code" => strtoupper($p['unit_weight']),
	                            "Description" => $this->transfer($p['unit_weight'])
	                        ],
	                        "Weight" => strval($p['weight'])
	                    ]
						];
				}
            	}
            }

            $license = $this->model_extension_upsmodule_rateonorder->getLicense();
			$services = $this->model_extension_upsmodule_rateonorder->getEnableServices();
			$service_codes = [];
			if (!empty($services)) {
				foreach ($services as $ser) {
					if (isset($ser['rate_code']) && $ser['service_name']) {
						$service_codes[$ser['rate_code']] = $ser['service_name'];
					}
				}
			}
			
			$rate_res = $this->callUpsApi('Rate', $rate_req_data, $license);
			$final_rates = [];
			$error_msg = "";
			if (isset($rate_res->RateResponse->RatedShipment)) {
				foreach ($rate_res->RateResponse->RatedShipment as $rates) {
					if (isset($rates->Service->Code) && isset($service_codes[(string)$rates->Service->Code])) {
						$curr_rates = [];
						$curr_rates['rate_des'] = $service_codes[(string)$rates->Service->Code];
						$curr_rates['rate_code'] = (string)$rates->Service->Code;
						$curr_rates['currency'] = (string)$rates->TotalCharges->CurrencyCode;
						$curr_rates['rate'] = isset($rates->NegotiatedRateCharges) ? (string)$rates->NegotiatedRateCharges->TotalCharge->MonetaryValue : (string)$rates->TotalCharges->MonetaryValue;
						$final_rates[] = $curr_rates;
					}
				}
			} else {
				if (isset($rate_res->Fault->faultstring)) {
					$error_msg = (string)$rate_res->Fault->faultstring;
				} else {
					$error_msg = "Unknown error/response found.";
				}
			}
			if (!empty($final_rates) && empty($error_msg)) {
				echo json_encode(array("success" => true, "rates_list" => $final_rates));
			} else {
				if (!empty($error_msg)) {
					echo '{error: '.$error_msg.'}';
				} else {
					echo '{error: No rates available for enabled services.}';
				}
			}
		} else {
			echo '{error: Unknown order.}';
		}
	}
	public function getCurrencyCodeByCountryCode($country_code)
    {
        $list_currency = new \stdClass();
		$list_currency->DE = "EUR";
		$list_currency->ES = "EUR";
		$list_currency->BE = "EUR";
		$list_currency->FR = "EUR";
		$list_currency->IS = "ISK";
		$list_currency->IT = "EUR";
        $list_currency->NL = "EUR";
		$list_currency->GB = "GBP";
		$list_currency->PL = "PLN";
		$list_currency->AT = "EUR";
		$list_currency->BG = "BGN";
		$list_currency->HR = "HRK";
		$list_currency->CY = "EUR";
		$list_currency->CZ = "CZF";
		$list_currency->DK = "DKK";
		$list_currency->EE = "EUR";
		$list_currency->FI = "EUR";
		$list_currency->GR = "EUR";
		$list_currency->HU = "HUF";
		$list_currency->IE = "EUR";
		$list_currency->LT = "EUR";
		$list_currency->LU = "EUR";
		$list_currency->LV = "EUR";
		$list_currency->MT = "EUR";
		$list_currency->PT = "EUR";
		$list_currency->RO = "RON";
		$list_currency->SE = "SEK";
		$list_currency->SI = "EUR";
		$list_currency->SK = "EUR";
		$list_currency->CH = "CHF";
		$list_currency->RS = "EUR";
		$list_currency->NO = "NOK";
        $list_currency->US = "USD";
		$list_currency->TR = "TRY";
		$list_currency->JE = "GBP";

        if (isset($list_currency->{$country_code})) {
            return $list_currency->{$country_code};
        }
        return "";
    }

	public function Check_AP_RateRequest()
	{
			$this->load->model('extension/upsmodule/base');
			$this->load->model('extension/upsmodule/account');
			$this->load->model('extension/upsmodule/rateonorder');
			$configs = $this->model_extension_upsmodule_rateonorder->getConfigs();
			$constants = $this->model_extension_upsmodule_base->listConstanst();
			$def_acc = $this->model_extension_upsmodule_account->getAccountDefault();
			$rate_req_data = [];
	        $alternate_delivery_address = $constants->alternate_delivery_address;
	        $rate_req_data["ShippingType"]  = "AP";
	        $rate_req_data["Typerate"]  = "createshipment";
	        $rate_req_data["Request"]["RequestOption"]  = "Shoptimeintransit";
	        //Date now
	        $date = $this->model_extension_upsmodule_rateonorder->getDateTime();
	        $cut_off_time = (int)($configs->ups_shipping_cut_off_time . '00');
	        $h_now = (int)date('Hi', strtotime($date));
	        $time = date('Ymd', strtotime($date));
	        if ($h_now > $cut_off_time) { // if cut off time > current time, add 1 day
	            $time = date('Ymd', strtotime(date("Y-m-d", strtotime($date))." +1 day"));
	        }
	        $rate_req_data["DeliveryTimeInformation"]["Pickup"]["Date"]  =  $time;

	        //shipper
	        $rate_req_data[$constants->shipper][$constants->name_data] = $def_acc['fullname'];
	        $rate_req_data[$constants->shipper][$constants->shipper_number] = $def_acc[$constants->ups_account_number];
	        $rate_req_data[$constants->shipper][$constants->address_data][$constants->address_line] = [$def_acc[$constants->address_1],
	        $def_acc[$constants->address_2], $def_acc[$constants->address_3]];
	        $rate_req_data[$constants->shipper][$constants->address_data]["City"] = $def_acc['city'];
	        $rate_req_data[$constants->shipper][$constants->address_data][$constants->province_code_acc] = $def_acc['state_province_code'];
	        $rate_req_data[$constants->shipper][$constants->address_data][$constants->postal_code] = $def_acc['post_code'];
	        $rate_req_data[$constants->shipper][$constants->address_data][$constants->country_code_acc] = $def_acc['country'];
	        //ship_from
	        $rate_req_data[$constants->ship_from_data][$constants->name_data] = $def_acc['fullname'];
	        $rate_req_data[$constants->ship_from_data][$constants->shipper_number] =  $def_acc[$constants->ups_account_number];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data][$constants->address_line] = [$def_acc[$constants->address_1],
	        $def_acc[$constants->address_2], $def_acc[$constants->address_3]];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data]["City"] = $def_acc['city'];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data][$constants->province_code_acc] = $def_acc['state_province_code'];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data][$constants->postal_code] = $def_acc['post_code'];
	        $rate_req_data[$constants->ship_from_data][$constants->address_data][$constants->country_code_acc] = $def_acc['country'];
	        //ship_to
	        $rate_req_data[$constants->ship_to_data][$constants->name_data] = $def_acc['fullname'];
	        $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->address_line] = [$def_acc[$constants->address_1],$def_acc[$constants->address_2], $def_acc[$constants->address_3]];
	        $rate_req_data[$constants->ship_to_data][$constants->address_data]["City"] = $def_acc['city'];
	        if (!empty($def_acc['state_province_code'])) {
	            $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->province_code_acc] = $def_acc['state_province_code'];
	        } else {
	            $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->province_code_acc] = "XX";
	        }
	        $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->postal_code] = $def_acc['post_code'];
	        $rate_req_data[$constants->ship_to_data][$constants->address_data][$constants->country_code_acc] = $def_acc['country'];
			$currency = $this->getCurrencyCodeByCountryCode($def_acc['country']);
	        $rate_req_data["InvoiceLineTotal"]["CurrencyCode"] =  $currency;
        	$rate_req_data["InvoiceLineTotal"]["MonetaryValue"] = '64';

			// Alternate address
			$rate_req_data[$constants->alternate_delivery_address][$constants->name_data] = $def_acc['fullname'];
            $rate_req_data[$constants->alternate_delivery_address][$constants->attention_name] = " ";
            $rate_req_data[$constants->alternate_delivery_address][$constants->address_data][$constants->address_line] = [$def_acc[$constants->address_1],$def_acc[$constants->address_2], $def_acc[$constants->address_3]];
            $rate_req_data[$constants->alternate_delivery_address][$constants->address_data]["City"] = $def_acc['city'];
            if (!empty($def_acc['state_province_code'])) {
                $rate_req_data[$constants->alternate_delivery_address][$constants->address_data][$constants->province_code_acc] = $def_acc['state_province_code'];
            } else {
                $rate_req_data[$constants->alternate_delivery_address][$constants->address_data][$constants->province_code_acc] = "XX";
            }
            $rate_req_data[$constants->alternate_delivery_address][$constants->address_data][$constants->postal_code] = $def_acc['post_code'];
            $rate_req_data[$constants->alternate_delivery_address][$constants->address_data][$constants->country_code_acc] = $def_acc['country'];


        	$rate_req_data["PaymentDetails"]["ShipmentCharge"] = [
	            "Type" => "01",
	            "BillShipper" => [
	                "AccountNumber" => $def_acc[$constants->ups_account_number]
	            ]
	        ];
			
			// $this->load->model('localisation/length_class');
			// $this->load->model('localisation/weight_class');
			$value = $this->datahelper_country();
			$units = $value[$def_acc['country']]['weight'];
			$dimension_unit = 'IN';
			$Weight_unit = 'LBS';
			if($units == 'KG_CM'){
				$dimension_unit = 'CM';
				$Weight_unit = 'KGS';
				$ddes = 'centimeter';
				$wdes = 'kilograms';
			}
			// $weightUnit = 'lbs';
			// $dimensionUnit = $this->length->getUnit($this->config->get('config_length_class_id'));
			// $weightUnit = $this->weight->getUnit($this->config->get('config_weight_class_id'));
			
			// if ($weightUnit == 'kg') {
			// 	$this->weightUnit = 'kgs';
			// };
            $packages = [];
            
            	
            		$rate_req_data["Package"][] =
	                [
	                    "Dimensions" => [
	                        "UnitOfMeasurement" => [
	                            "Code" => strtoupper($dimension_unit),
	                            "Description" => $this->transfer($ddes)
	                        ],
	                        "Length" => '10.00',
	                        "Width" => '10.00',
	                        "Height" => '10.00'
	                    ],
	                    "PackageWeight" => [
	                        "UnitOfMeasurement" => [
	                            "Code" => strtoupper($Weight_unit),
	                            "Description" => $this->transfer($wdes)
	                        ],
	                        "Weight" => strval('1.00')
	                    ]
	                ];
            	
					$rate_req_data['RequestForAPcheck'] = 1;

            $license = $this->model_extension_upsmodule_rateonorder->getLicense();
			$services = $this->model_extension_upsmodule_rateonorder->getEnableServices();
			$service_codes = [];
			if (!empty($services)) {
				foreach ($services as $ser) {
					if (isset($ser['rate_code']) && $ser['service_name']) {
						$service_codes[$ser['rate_code']] = $ser['service_name'];
					}
				}
			}
			
			$rate_res = $this->callUpsApi('Rate', $rate_req_data, $license);
			$error_msg = "";
			if (isset($rate_res->RateResponse->Response->ResponseStatus->Description) && $rate_res->RateResponse->Response->ResponseStatus->Description == 'Success') {
				echo json_encode(array("message" => 'success', "error" => false));
			}else{
				echo json_encode(array("message" => 'Failed to enable AccessPoint service. Contact support.', "error" => true));
			}
	}
	public function datahelper_country(){
		$value['DE'] = array('region' => 'AP', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['ES'] = array('region' => 'AP', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['BE'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['FR'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['IS'] = array('region' => 'EU', 'currency' =>'ISK', 'weight' => 'KG_CM');
		$value['IT'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['NL'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['GB'] = array('region' => 'EU', 'currency' =>'GBP', 'weight' => 'KG_CM');
		$value['PL'] = array('region' => 'EU', 'currency' =>'PLN', 'weight' => 'KG_CM');
		$value['AT'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['BG'] = array('region' => 'EU', 'currency' =>'BGN', 'weight' => 'KG_CM');
		$value['HR'] = array('region' => 'AP', 'currency' =>'HRK', 'weight' => 'KG_CM');
		$value['CY'] = array('region' => 'AP', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['CZ'] = array('region' => 'EU', 'currency' =>'CZF', 'weight' => 'KG_CM');
		$value['DK'] = array('region' => 'AM', 'currency' =>'DKK', 'weight' => 'KG_CM');
		$value['EE'] = array('region' => 'AP', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['FI'] = array('region' => 'AP', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['GR'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['HU'] = array('region' => 'EU', 'currency' =>'HUF', 'weight' => 'KG_CM');
		$value['IE'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['LT'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['LU'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['LV'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['MT'] = array('region' => 'AP', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['PT'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['RO'] = array('region' => 'EU', 'currency' =>'RON', 'weight' => 'KG_CM');
		$value['SE'] = array('region' => 'EU', 'currency' =>'SEK', 'weight' => 'KG_CM');
		$value['SI'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['SK'] = array('region' => 'EU', 'currency' =>'EUR', 'weight' => 'KG_CM');
		$value['CH'] = array('region' => 'EU', 'currency' =>'CHF', 'weight' => 'KG_CM');
		$value['RS'] = array('region' => 'AP', 'currency' =>'RSD', 'weight' => 'KG_CM');
		$value['NO'] = array('region' => 'EU', 'currency' =>'NOK', 'weight' => 'KG_CM');
		$value['TR'] = array('region' => 'AP', 'currency' =>'TRY', 'weight' => 'KG_CM');
		$value['JE'] = array('region' => 'AM', 'currency' =>'GBP', 'weight' => 'KG_CM');
		return $value;
	}
	public function arrangePackage($pack)
	{
		$package = !empty($pack) ? json_decode($pack) : '';
		$rate_pack = [];
		if (isset($package[0])) {
			$rate_pack['length'] = $package[0]->length;
			$rate_pack['width'] = $package[0]->width;
			$rate_pack['height'] = $package[0]->height;
			$rate_pack['unit_dimension'] = $package[0]->unit_dimension;
			$rate_pack['weight'] = $package[0]->weight;
			$rate_pack['unit_weight'] = $package[0]->unit_weight;
			$rate_pack['number_of_package'] = count($package);
		}
		return (object)$rate_pack;
	}

	function transfer($key)
    {
        $array = [];
        $array['lbs'] = 'pounds';
        $array['kgs'] = 'kilograms';
        $array['cm'] = 'centimeter';
        $array['inch'] = 'inch';
        if (isset($array[$key])) {
            return $array[$key];
        } else {
            return $key;
        }
    }
    public function callUpsApi($method, $data, $license = null)
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $commonInfo = $this->model_extension_upsmodule_base->getCommonInfo();
        include_once "$constants->link_api_ups";
        //get api
        $api = new Ups();
        $api->setCommonApiInfo($commonInfo);
        $response = null;
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($method);
        switch ($method) {
            case $constants->LBRecovery:
                $response = json_decode($api->printLabel($data, $license));
                break;
            case $constants->Ship:
                $response = json_decode($api->createShipment($data, $license));
                break;
            case $constants->Rate:
                $response = json_decode($api->getRate($data, $license));
                break;
            default:
                break;
        }
        $data_api = $api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);
        return $response;
    }
}