<?php
/**
 * _USER_TECHNICAL_AGREEMENT
 *
 * @category  UPS eCommerce Integrations
 * @package   UPS Shipping and UPS Access Point™ : Official Extension for OpenCart
 * <AUTHOR> Parcel Service of America, Inc.
 * @copyright (c) 2019 United Parcel Service of America, Inc., all rights reserved
 * @license   This work is Licensed under the License and Data Service Terms available
 * at: https://www.ups.com/assets/resources/media/ups-license-and-data-service-terms.pdf
 * @link      https://www.ups.com/pl/en/services/technology-integration/ecommerce-plugins.page
 */

/**
 * ControllerExtensionUpsmoduleAccountsuccess file
 *
 * @category Accountsuccess_Controller
 */

class ControllerExtensionUpsmoduleAccountsuccess extends Controller
{
    private $_validate_data = [];
    private $_base_model = 'extension/upsmodule/base';
    private $_plugin_manage_model = 'extension/upsmodule/pluginmanage';
    private $_api_model = 'extension/upsmodule/apiModel';
    private $_api_manage = '../system/library/upsmodule/API/Manage.php';

    /**
     * ControllerExtensionUpsmoduleAccountsuccess index
     *
     * @return null
     */
    public function index()
    {
        $this->load->model($this->_base_model);

        //setup default language to english
        $this->load->model("extension/upsmodule/opencartsetting");
        $this->model_extension_upsmodule_opencartsetting->setEnglishToDefaultLanguage(
            "accountsuccess",
            "extension/upsmodule/accountsuccess"
        );

        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->language($constants->link_translate);
        $this->document->setTitle($this->language->get($constants->text_account));
        $this->load->model($constants->link_account);
        $this->load->model($constants->link_opencartsetting);
        $account_default_check = $this->model_extension_upsmodule_account->getAccountDefault();
        if (!$account_default_check) {
            $this->response->redirect($this->url->link($constants->link_account, $constants->token, true));
        }
        $this->getForm();
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess getListAccount
     *
     * @return $list_account
     */
    public function getListAccount()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_account);
        $this->load->model($constants->link_opencartsetting);
        $list_account = $this->model_extension_upsmodule_account->getListAccount();
        if (!empty($list_account) && is_array($list_account)) {
            foreach ($list_account as $key => $value) {
                $list_account[$key][$constants->post_code] = trim($value[$constants->post_code]);
                $country_data = $this->model_extension_upsmodule_country->getListCountry();
                if ($country_data) {
                    $country_code = $value['country'];
                    $list_account[$key][$constants->country_name] = $country_data[$country_code];
                }
            }
        }
        return $list_account;
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess checkLink
     *
     * @return $checkLink
     */
    protected function checkLink()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $check_link = '';
        $list_check_termcondition = $this->model_extension_upsmodule_account->getListCheckTermcondition();
        foreach ($list_check_termcondition as $row) {
            if ($row['value'] == 1) {
                $check_link = $this->url->link($constants->link_account, $constants->token, true);
            } else {
                $check_link = $this->response->redirect(
                    $this->url->link($constants->link_termcondition, $constants->token, true)
                );
            }
        }
        return $check_link;
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess getForm
     *
     * @return null
     */
    protected function getForm()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $constants_title_account = $this->model_extension_upsmodule_base->listTitleAccount();
        $constants_currency = $this->model_extension_upsmodule_base->listCurrency();
        $this->checkLink();
        $list_currencies = $constants_currency->list_currency;
        $list_title = $constants_title_account->list_title_account;
        $data['list_currencies'] = $list_currencies;
        $data['list_title'] = $list_title;
        $data['text_UPS_Shipping_Module'] = $this->language->get('text_UPS_Shipping_Module');
        $data['text_accountsuccess'] = $this->language->get($constants->text_account);
        $data[$constants->text_error] = $this->language->get($constants->text_error);
        $url = '';
        $data[$constants->error] = false;
        $token = trim($this->session->data[$constants->user_token]);
        //action url
        $data['action']
            = $this->url->link('extension/upsmodule/accountsuccess/verifyAccount&user_token=' . $token, true);
        $data['rest_pwd_action']
            = $this->url->link('extension/upsmodule/accountsuccess/resetPass&user_token=' . $token, true);
        $data['shipping_service_url'] = $this->url->link('extension/upsmodule/shippingservice&user_token=' . $token);
        $data['shipping_service_checkall'] = $this->url->link(
            'extension/upsmodule/accountsuccess/checkAllService&user_token=' . $token,
            true
        );
        $data['remove_account_url']
            = $this->url->link('extension/upsmodule/accountsuccess/deleteAccount&user_token=' . $token);
        //get list account
        $data['list_account'] = $this->getListAccount();
        $config_shipping_service = '0';
        $option_setting = $this->model_extension_upsmodule_base->getSetting();
        if (!empty($option_setting) && isset($option_setting->ups_shipping_menu_shipping_service)) {
            $config_shipping_service = $option_setting->ups_shipping_menu_shipping_service;
        }
        $data['config_shipping_service'] = $config_shipping_service;
        //get default account info
        $account_default = $this->model_extension_upsmodule_account->getAccountDefault();
        $data['account_default_full_name'] = $account_default[$constants->fullname];
        $data['account_default_company'] = $account_default[$constants->company];
        $data['account_default_email'] = $account_default[$constants->email];
        $data['account_default_phone_number'] = $account_default['phone_number'];
        $address = [
            $account_default['address_1'],
            $account_default['address_2'],
            $account_default['address_3']
        ];
        $data['account_default_address'] = implode('<br>', $this->addArray($address));
        $data['account_default_postal_code'] = $account_default[$constants->post_code];
        $data['account_default_city'] = $account_default['city'];
        $country_code = $account_default[$constants->country_account];
        if (!empty($account_default['state_province_code'])) {
            $state_date = $this->model_extension_upsmodule_country->getStateName(
                $account_default['state_province_code'],
                $country_code
            );
            $data['account_default_state'] = $state_date['name'];
        }
        //get country code
        $get_country_code = $this->model_extension_upsmodule_opencartsetting->getCountryCode();
        $totals = $this->model_extension_upsmodule_country->getListCountry();
        if (!empty($totals) && is_array($totals)) {
            foreach ($totals as $key => $value) {
                if ($key == $get_country_code['value']) {
                    $data[$constants->country_code] = $key;
                    $data[$constants->country_name] = $value;
                }
                if ($key == $country_code) {
                    $data['account_default_country_code'] = $country_code;
                    $data['account_default_country'] = $value;
                }
            }
        }
        $data['ups_footer'] = "";
        $data['accountsuccess_title_with_90'] = $this->language->get('account_title_with_90');
        if (!empty($get_country_code) && strtolower($get_country_code[$constants->value]) == "us") {
            $data['ups_footer'] = $this->load->view('extension/upsmodule/upsfooter');
            $data['accountsuccess_title_with_90'] = $this->language->get('account_title_with_90_US');
        }
        //Infor account
        $data['text_home'] = $this->language->get('text_home');

        $ups_link = $this->model_extension_upsmodule_base->createUpsReferenceLink("account");
        $link = '<a target="_blank" href="' . $ups_link . '">';
        $data['account_link'] = sprintf($this->language->get('account_link'), $link, '</a>');

        $data['accountsuccess_title_profile'] = $this->language->get('accountsuccess_title_profile');
        $data['accountsuccess_full_name'] = $this->language->get('account_full_name');
        $data['accountsuccess_address'] = $this->language->get('account_address');
        $data['account_tooltip_pickup_address'] = $this->language->get('account_tooltip_pickup_address');
        $data['accountsuccess_company'] = $this->language->get('account_company');
        $data['accountsuccess_postal_code'] = $this->language->get('account_postal_code');
        $data['accountsuccess_email'] = $this->language->get('account_email');
        $data['accountsuccess_city'] = $this->language->get('account_city');
        $data['accountsuccess_state'] = $this->language->get('account_state');
        $data['accountsuccess_phone_number'] = $this->language->get('account_phone_number');
        $data['accountsuccess_country'] = $this->language->get('account_country');
        $data['accountsuccess_confirm_remove_account'] = $this->language->get('accountsuccess_confirm_remove_account');
        $data['accountsuccess_remove_account_title'] = $this->language->get('accountsuccess_remove_account_title');
        $data['btn_cancel'] = $this->language->get('button_cancel');
        $data['btn_ok'] = $this->language->get('button_ok');
        $data['accountsuccess_title_payment'] = $this->language->get('accountsuccess_title_payment');
        $data['accountsuccess_add_account'] = $this->language->get('accountsuccess_add_account');
        $data['accountsuccess_add_account_inform'] = $this->language->get('accountsuccess_add_account_inform');
        $data['account_title_form_note'] = $this->language->get('account_title_form_note');
        $data['accountsuccess_address_type'] = $this->language->get('account_address_type');
        $data['accountsuccess_placeholder_address_type'] = $this->language->get('account_placeholder_address_type');
        $data['accountsuccess_tooltip_address_type'] = $this->language->get('account_tooltip_address_type');
        $data['accountsuccess_account_name'] = $this->language->get('account_account_name');
        $data['accountsuccess_tooltip_account_name'] = $this->language->get('account_tooltip_account_name');
        $data['accountsuccess_placeholder_address1'] = $this->language->get('account_placeholder_address1');
        $data['accountsuccess_placeholder_address2'] = $this->language->get('account_placeholder_address2');
        $data['accountsuccess_placeholder_address3'] = $this->language->get('account_placeholder_address3');
        $data['accountsuccess_tooltip_postal_code'] = $this->language->get('account_tooltip_postal_code');
        $data['accountsuccess_phone'] = $this->language->get('accountsuccess_phone');
        $data['accountsuccess_account_number'] = $this->language->get('account_account_number');
        $data['accountsuccess_tooltip_account_number'] = $this->language->get('account_tooltip_account_number');
        $data['accountsuccess_invoice_number'] = $this->language->get('account_invoice_number');
        $data['accountsuccess_invoice_control_id'] = $this->language->get('account_invoice_control_id');
        $data['accountsuccess_tooltip_invoice_number'] = $this->language->get('account_tooltip_invoice_number');
        $data['accountsuccess_invoice_amount'] = $this->language->get('account_invoice_amount');
        $data['accountsuccess_tooltip_invoice_amount'] = $this->language->get('account_tooltip_invoice_amount');
        $data['accountsuccess_currency'] = $this->language->get('account_currency');
        $data['accountsuccess_invoice_date'] = $this->language->get('account_invoice_date');
        $data['accountsuccess_tooltip_invoice_date'] = $this->language->get('account_tooltip_invoice_date');
        $data['accountsuccess_number_with_90'] = $this->language->get('account_number_with_90');
        $data['accountsuccess_number_without_90'] = $this->language->get('account_number_without_90');
        $data['account_number_existed'] = $this->language->get('account_number_existed');
        $data['change_acc_user_and_pass'] = $this->language->get('change_acc_user_and_pass');
        $data['button_reset_pass'] = $this->language->get('button_reset_pass');
        $data['modal_loading'] = $this->load->view('extension/upsmodule/modalloading');

        $data['list_state'] = $this->model_extension_upsmodule_country->getListStateByCountryCode(
            $data[$constants->country_code]
        );

        //Button
        $data['button_verify'] = $this->language->get('button_verify');
        $data['button_next'] = $this->language->get('button_next');
        $data['remove_account'] = $this->language->get('text_accountsuccess_remove');
        $data[$constants->user_token] = $token;
        //region Include
        $data['header'] = $this->load->controller('common/header');
        $data['column_left'] = $this->load->controller('common/column_left');
        $data['footer'] = $this->load->controller('common/footer');
        //end
        $data['account_success'] = $this->url->link(
            'extension/upsmodule/accountsuccess&user_token=' .
            $this->session->data[$constants->user_token]
        );
        $data['home'] = $this->url->link(
            'common/dashboard&user_token=' .
            $this->session->data[$constants->user_token]
        );
        //show message account default
        $success = 0;
        $error = 0;
        $message = '';
        if (isset($this->session->data[$constants->success])) {
            $success = $this->session->data[$constants->success];
            $message = $this->session->data[$constants->message];
        }
        if (isset($this->session->data[$constants->error])) {
            $error = $this->session->data[$constants->error];
            $message = $this->session->data[$constants->message];
        }
        $data[$constants->success] = $success;
        $data[$constants->error] = $error;
        $data['content_message'] = $message;
        $this->session->data[$constants->success] = 0;
        $this->session->data[$constants->error] = 0;
        $this->session->data[$constants->message] = '';
        $this->response->setOutput($this->load->view($constants->link_accountsuccess, $data));
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess deleteAccount
     *
     * @return null
     */
    public function deleteAccount()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_account);
        $test = $this->session->data;
        $data = $this->request->post;
        $account_id = $data['account_id'];
        $setting = $this->model_extension_upsmodule_base->getSetting();
        if (!empty($setting)) {
            $account_default = (object) $this->model_extension_upsmodule_account->getAccountDefault();
            $account_default_id = 1;
            if (!empty($account_default)) {
                $account_default_id = $account_default->account_id;
            }
            if (isset($setting->ups_shipping_choose_account_number_ap) &&
                intval($setting->ups_shipping_choose_account_number_ap) == intval($account_id)
            ) {
                $this->model_extension_upsmodule_base->saveOptionSetting(
                    'ups_shipping_choose_account_number_ap',
                    $account_default_id
                );
            }
            if (isset($setting->ups_shipping_choose_account_number_add) &&
                intval($setting->ups_shipping_choose_account_number_add) == intval($account_id)
            ) {
                $this->model_extension_upsmodule_base->saveOptionSetting(
                    'ups_shipping_choose_account_number_add',
                    $account_default_id
                );
            }
            if (isset($setting->ups_shipping_check_manage) && $setting->ups_shipping_check_manage == 1) {
                $this->updateMerchantStatusDelete($account_id);
            }
        }
        $check = $this->model_extension_upsmodule_account->deleteAccount($account_id);
        echo json_encode($check);
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess resetPass
     *
     * @return null
     */
    public function resetPass()
    {        
        $data = $this->request->post;
        if (isset($data['resetName']) && isset($data['resetPass']) && !empty($data['resetName']) && !empty($data['resetPass'])) {
            $this->load->model($this->_base_model);
            $constants = $this->model_extension_upsmodule_base->listConstanst();
            $this->load->model($constants->link_api_model);
            $this->model_extension_upsmodule_apiModel->updateLicense($data['resetName'], $data['resetPass']);
            echo '{"success":"true"}';
        } else {
            echo '{"error":"Required data missing/invalid."}';
        }
    }
    
    /**
     * ControllerExtensionUpsmoduleAccountsuccess verifyAccount
     *
     * @return null
     */
    public function verifyAccount()
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->language($constants->link_translate);
        $this->load->model($constants->link_account);
        $this->load->model($constants->link_api_model);
        $error = true;
        $message = '';
        $data = $this->request->post;
        $message = $this->language->get($constants->text_error);
        $account_number = '';
        if (isset($data[$constants->optradio]) && $data[$constants->optradio] == 2) {
            $account_number = $data[$constants->account_number_2];
        } elseif (isset($data[$constants->optradio]) && $data[$constants->optradio] == 1) {
            $account_number = $data[$constants->account_number_1];
        } else {
            $account_number = '';
        }
        if ($this->model_extension_upsmodule_account->checkAccount($account_number) == false) {
            if ($this->checkValidate()) {
                if (!$this->checkValidateOption()) {
                    echo json_encode(
                        [$constants->error => $error, $constants->message => $message, $constants->has_account => false]
                    );
                } else {
                    $this->checkDataValue();
                }
            } else {
                echo json_encode(
                    [$constants->error => $error, $constants->message => $message, $constants->has_account => false]
                );
            }
        } else {
            echo json_encode(
                [$constants->error => $error, $constants->message => $message, $constants->has_account => true]
            );
        }
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess checkDataValue
     *
     * @return null
     */
    public function checkDataValue()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_account);
        $this->load->model($constants->link_api_model);
        $license = $this->model_extension_upsmodule_apiModel->getLicense();
        $data = $this->request->post;
        $check_phone_number = html_entity_decode($data[$constants->phone_number]);
        $phone_number = preg_replace('/[^0-9]/s', '', $check_phone_number);
        $postal_code = html_entity_decode(trim($data[$constants->postal_code]));
        $check_account_name = htmlentities($data['AccountName']);
        $data['AccountName'] = html_entity_decode($check_account_name);
        //get 4 columns of account default
        $default_account = $this->model_extension_upsmodule_account->getAccountDefault();
        $check_default_account = htmlentities($default_account[$constants->fullname]);
        $default_account[$constants->fullname] = html_entity_decode($check_default_account);
        $state_province_code = "";
        if (!empty($data[$constants->province_code_acc])) {
            $state_province_code = $data[$constants->province_code_acc];
        }
        $data_account = [
            $constants->title_account => $default_account[$constants->title_account],
            $constants->customer_name => $default_account[$constants->fullname],
            $constants->company => $default_account[$constants->company],
            $constants->email => $default_account[$constants->email],
            $constants->address_type => $data['AddressType'],
            $constants->ups_account_name => $data[$constants->account_name],
            $constants->address_1 => html_entity_decode($data[$constants->address_line_1]),
            $constants->address_2 => html_entity_decode($data['AddressLine2']),
            $constants->address_3 => html_entity_decode($data['AddressLine3']),
            $constants->post_code => $postal_code,
            $constants->city => $data[$constants->account_city],
            $constants->state_province_code => $state_province_code,
            $constants->country_code => $data['CountryCode'],
            $constants->phone => $phone_number,
            $constants->account_type => $data[$constants->optradio],
            $constants->account_default => 0
        ];
        if ($data[$constants->optradio] == 1) {
            $date = date('Y-m-d', strtotime(str_replace('-', '/', $data['InvoiceDate'])));
            $data_option = [
                $constants->ups_account_number => $data[$constants->account_number_1],
                $constants->ups_invoice_number => $data['InvoiceNumber'],
                $constants->ups_invoice_amount => $data['InvoiceAmount'],
                $constants->ups_currency => $data['Currency'],
                $constants->ups_invoice_date => $date,
            ];
            if (isset($data['ControlID'])) {
                $data_option['control_id'] = $data['ControlID'];
            }
            $data_account = array_merge($data_account, $data_option);
            //call api
            $this->saveAccount($data_account, $license);
        }
        if ($data[$constants->optradio] == 2) {
            $data_option = [
                $constants->ups_account_number => $data[$constants->account_number_2],
            ];
            $data_account = array_merge($data_account, $data_option);
            //call api
            $this->saveAccount($data_account, $license);
        }
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess saveAccount
     *
     * @param string $data_account //The data_account
     * @param string $license      //The license
     *
     * @return null
     */
    public function saveAccount($data_account, $license)
    {
        $this->load->model($this->_base_model);
        //set secure header
        $this->model_extension_upsmodule_base->setHeaderSecure();
        //get constant
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $this->load->model($constants->link_account);
        $this->load->model($constants->link_api_model);
        $response = $this->callUpsApi($constants->RegistrationSuccess, $data_account, $license);
        if ($response['check']) {
            $this->model_extension_upsmodule_account->saveAccount($data_account);
            $setting = $this->model_extension_upsmodule_base->getSetting();
            if (isset($setting->ups_shipping_check_manage) && $setting->ups_shipping_check_manage == 1) {
                //Send data to API
                $this->updateMerchantStatus($data_account['ups_account_number']);
            }
            $this->session->data[$constants->success] = 1;
            $this->session->data[$constants->message] = $this->language->get('accountsuccess_create_success');
            echo json_encode(
                [$constants->error => false,
                $constants->message => '',
                $constants->has_account => false]
            );
        } else {
            $error = true;
            $message = $response[$constants->message];
            echo json_encode(
                [$constants->error => $error,
                $constants->message => $message,
                $constants->has_account => false]
            );
        }
    }

    /**
     * ControllerExtensionUpsmoduleAccount checkAllService
     *
     * @param string $account_data //The account_data
     * @return null
     */
    public function checkAllService()
    {
        //Load model
        $this->load->model($this->_base_model);
        $this->load->model('extension/upsmodule/shippingservice');
        //constants
        $option = $this->model_extension_upsmodule_base->getSetting();
        $country_code = '';
        if (!empty($option) && isset($option->ups_shipping_country_code)) {
            $country_code = $option->ups_shipping_country_code;
        }

        if (!empty($country_code) && strtolower($country_code) == 'us') {
            $list_services = $this->model_extension_upsmodule_shippingservice->getShippingServiceByCountry(
                $country_code
            );
            $this->model_extension_upsmodule_shippingservice->updateSetting(
                'ups_shipping_to_ap_delivery',
                '1'
            );
            $this->model_extension_upsmodule_shippingservice->updateSetting(
                'ups_shipping_to_add_delivery',
                '1'
            );
            $this->model_extension_upsmodule_shippingservice->updateSetting(
                'ups_shipping_default_shipping',
                '0'
            );
            foreach ($list_services as $item) {
                $service = (object) $item;
                $this->model_extension_upsmodule_shippingservice->updateService($service->id, '1');
                $this->model_extension_upsmodule_shippingservice->saveDeliveryRateForUS($service->id);
            }
        }
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess checkValidate
     *
     * @return null
     */
    public function checkValidate()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $constants_validate = $this->model_extension_upsmodule_base->listRegexValidate();
        $data = $this->request->post;
        $regex = $constants_validate->regex_validate;
        $this->_validate_data = [];
        $this->_validate_data = [
            !empty($data[$constants->account_name]),
            !empty($data[$constants->postal_code]),
            !empty($data[$constants->address_line_1]),
            !empty($data[$constants->phone_number]),
            $this->validateAccount($data[$constants->account_city], $regex[$constants->validate_null])
        ];
        if (!in_array(false, $this->_validate_data)) {
            return true;
        }
        return false;
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess checkValidateOption
     *
     * @return null
     */
    public function checkValidateOption()
    {
        $this->load->model($this->_base_model);
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $constants_validate = $this->model_extension_upsmodule_base->listRegexValidate();
        $data = $this->request->post;
        $regex = $constants_validate->regex_validate;
        if ($data[$constants->optradio] == 2) {
            if (!$this->validateAccount($data[$constants->account_number_2], $regex['accountNumber'])) {
                return false;
            }
        } elseif ($data[$constants->optradio] == 1) {
            $this->_validate_data = [];
            $this->_validate_data = [
                $this->validateAccount($data[$constants->account_number_1], $regex['accountNumber']),
                $this->validateAccount($data['InvoiceNumber'], $regex['invoiceNumber']),
                $this->validateAccount($data['InvoiceAmount'], $regex['invoiceAmount']),
                $this->validateAccount($data['InvoiceDate'], $regex['date'])
            ];
            if (isset($data['ControlID'])) {
                $this->_validate_data[] = $this->validateAccount($data['ControlID'], $regex['alphanum']);
            }
            if (in_array(false, $this->_validate_data)) {
                return false;
            }
        } else {
            //option 0
        }
        return true;
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess validateAccount
     *
     * @param string $inputAccount //The inputAccount
     * @param string $validate     //The validate
     *
     * @return null
     */
    public function validateAccount($inputAccount, $validate)
    {
        if (preg_match($validate, $inputAccount)) {
            return true;
        }
        return false;
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess addArray
     *
     * @param string $array //The array
     *
     * @return null
     */
    function addArray($array)
    {
        $array_return = [];
        foreach ($array as $key => $value) {
            if (!empty($value)) {
                $array_return[] = $value;
            }
        }
        return $array_return;
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess updateMerchantStatusDelete
     *
     * @param int $account_id //The account_id
     * @return null
     */
    public function updateMerchantStatusDelete($account_id)
    {
        $this->load->model($this->_base_model);
        $this->load->model($this->_plugin_manage_model);
        //Account
        $account_data = $this->model_extension_upsmodule_base->getAccountListById($account_id);
        $account_number = '';
        if (!empty($account_data) && isset($account_data["ups_account_number"])) {
            $account_number = $account_data["ups_account_number"];
        }
        //Create Object
        $this->model_extension_upsmodule_pluginmanage->updateMerchantStatusRemoveAccount($account_number);
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess updateMerchantStatus
     *
     * @param int $account_number //The account_number
     * @return null
     */
    public function updateMerchantStatus($account_number)
    {
        $this->load->model($this->_plugin_manage_model);
        $this->model_extension_upsmodule_pluginmanage->transferMerchantInfoByUser($account_number);
    }

    /**
     * ControllerExtensionUpsmoduleAccountsuccess callUpsApi
     *
     * @param string $method  //The method
     * @param string $data    //The data
     * @param string $license //The license
     *
     * @return $response
     */
    public function callUpsApi($method, $data, $license = null)
    {
        //Load model
        $this->load->model($this->_base_model);
        //constants
        $constants = $this->model_extension_upsmodule_base->listConstanst();
        $commonInfo = $this->model_extension_upsmodule_base->getCommonInfo();
        include_once"$constants->link_api_ups";
        //get api
        $Api = new Ups();
        $response = null;
        $id_log = $this->model_extension_upsmodule_base->beforeCallApi($method);
        if ($method == $constants->RegistrationSuccess) {
            $Api->setCommonApiInfo($commonInfo);
            $response = $Api->registrationSuccess($data, $license);
        }
        $data_api = $Api->getInformationAll($constants->full_uri, $constants->request, $constants->response);
        $this->model_extension_upsmodule_base->afterCallApi($id_log, $data_api);
        return $response;
    }
}
