<?php
header('Cache-Control: no-cache, no-store');
ini_set('memory_limit', '1024M');
ini_set('max_execution_time', 900);
ini_set('error_reporting', E_ALL);
include DIR_SYSTEM.'library/excel_point_tool/PHPExcel.php';
class ControllerExtensionExcelExport extends Controller {
	
	private $error = array();
	
	public function __construct($registry){
		 parent::__construct($registry);
		$path = 'exceltool/';
		if (!is_dir(DIR_UPLOAD . $path)) {
			mkdir(DIR_UPLOAD . $path, 0777);
		}
		define('DIR_EXCEL', DIR_UPLOAD . $path);
	}

	public function index(){
		$this->load->language('catalog/product');
		
		$this->load->language('extension/excel_point');

		$this->document->setTitle($this->language->get('heading_title'));
		
		$this->load->model('extension/excel_point');

		$data['heading_title'] = $this->language->get('heading_title');

		$data['text_form'] = !isset($this->request->get['product_id']) ? $this->language->get('text_add') : $this->language->get('text_edit');
		$data['text_default'] = $this->language->get('text_default');
		$data['text_enabled'] = $this->language->get('text_enabled');
		$data['text_disabled'] = $this->language->get('text_disabled');
		$data['text_yes'] = $this->language->get('text_yes');
		$data['text_no'] = $this->language->get('text_no');
		$data['text_missing'] = $this->language->get('text_missing');
		$data['text_email_already'] = $this->language->get('text_email_already');
		$data['text_password'] = $this->language->get('text_password');
		$data['help_email'] = $this->language->get('help_email');
		$data['help_password'] = $this->language->get('help_password');
		
		$data['entry_store'] = $this->language->get('entry_store');
		$data['entry_import'] = $this->language->get('entry_import');
		$data['entry_language'] = $this->language->get('entry_language');
		
		$data['tab_export'] = $this->language->get('tab_export');
		$data['tab_import'] = $this->language->get('tab_import');
		
		$data['select_stock_status'] = $this->language->get('select_stock_status');
		$data['select_manufacture'] = $this->language->get('select_manufacture');
		$data['select_categories'] = $this->language->get('select_categories');
		$data['select_status'] = $this->language->get('select_status');
		$data['export_format'] = $this->language->get('export_format');
		$data['product_image'] = $this->language->get('product_image');
		$data['entry_store'] = $this->language->get('entry_store');
		$data['entry_name'] = $this->language->get('entry_name');
		$data['entry_price'] = $this->language->get('entry_price');
		$data['entry_status'] = $this->language->get('entry_status');
		$data['entry_quantity'] = $this->language->get('entry_quantity');
		$data['entry_model'] = $this->language->get('entry_model');
		$data['entry_language'] = $this->language->get('entry_language');
		$data['entry_stock_status'] = $this->language->get('entry_stock_status');
		$data['entry_limit'] = $this->language->get('entry_limit');
		$data['entry_base_id'] = $this->language->get('entry_base_id');
		$data['entry_base_id'] = $this->language->get('entry_base_id');
		$data['entry_categories'] = $this->language->get('entry_categories');
		$data['entry_manufacturer'] = $this->language->get('entry_manufacturer');
		$data['entry_pricerange'] = $this->language->get('entry_pricerange');
		$data['entry_quantityrange'] = $this->language->get('entry_quantityrange');
		$data['entry_email'] = $this->language->get('entry_email');
		$data['entry_customer_group'] = $this->language->get('entry_customer_group');
		$data['entry_approved'] = $this->language->get('entry_approved');
		$data['entry_ip'] = $this->language->get('entry_ip');
		$data['entry_date_added'] = $this->language->get('entry_date_added');
		$data['entry_return_id'] = $this->language->get('entry_return_id');
		$data['entry_order_id'] = $this->language->get('entry_order_id');
		$data['entry_customer'] = $this->language->get('entry_customer');
		$data['entry_order_status'] = $this->language->get('entry_order_status');
		$data['entry_total'] = $this->language->get('entry_total');
		$data['entry_date_modified'] = $this->language->get('entry_date_modified');
		$data['button_filter'] = $this->language->get('button_filter');
		$data['button_save'] = $this->language->get('button_save');
		$data['button_cancel'] = $this->language->get('button_cancel');
		$data['button_export'] = $this->language->get('button_export');
		
		
		$data['user_token'] = $this->session->data['user_token'];
		
		if(isset($this->error['warning'])){
			$data['error_warning'] = $this->error['warning'];
		}elseif(isset($this->session->data['error_warning'])){
			$data['error_warning'] = $this->session->data['error_warning'];
			unset($this->session->data['error_warning']);
		}else{
			$data['error_warning'] = '';
		}
		
		if(isset($this->session->data['success'])){
			$data['success'] = $this->session->data['success'];
			unset($this->session->data['success']);
		}else{
			$data['success'] = '';
		}
		
		if (isset($this->request->get['filter_name'])) {
			$data['filter_name'] = $this->request->get['filter_name'];
		} else {
			$data['filter_name'] = null;
		}

		if (isset($this->request->get['filter_model'])) {
			$data['filter_model'] = $this->request->get['filter_model'];
		} else {
			$data['filter_model'] = null;
		}

		if (isset($this->request->get['filter_price'])) {
			$data['filter_price'] = $this->request->get['filter_price'];
		} else {
			$data['filter_price'] = null;
		}

		if (isset($this->request->get['filter_quantity'])) {
			$data['filter_quantity'] = $this->request->get['filter_quantity'];
		} else {
			$data['filter_quantity'] = null;
		}

		if (isset($this->request->get['filter_status'])) {
			$data['filter_status'] = $this->request->get['filter_status'];
		} else {
			$data['filter_status'] = null;
		}
		
		if (isset($this->request->get['filter_limit'])) {
			$data['filter_limit'] = $this->request->get['filter_limit'];
		} else {
			$data['filter_limit'] = $this->config->get('config_limit_admin');
		}
		
		$url = '';
		
		$data['breadcrumbs'] = array();

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('text_home'),
			'href' => $this->url->link('common/dashboard', 'user_token=' . $this->session->data['user_token'], 'SSL')
		);

		$data['breadcrumbs'][] = array(
			'text' => $this->language->get('heading_title'),
			'href' => $this->url->link('catalog/product', 'user_token=' . $this->session->data['user_token'] . $url, 'SSL')
		);
		
		$this->load->model('localisation/language');
		$data['languages'] = $this->model_localisation_language->getLanguages();
		
		$this->load->model('setting/store');
		$data['stores'] = $this->model_setting_store->getStores();
		
		$this->load->model('localisation/stock_status');
		$data['stock_statuses'] = $this->model_localisation_stock_status->getStockStatuses();
		
		$this->load->model('catalog/category');
		$data['categories'] = $this->model_catalog_category->getCategories();
		
		$this->load->model('catalog/manufacturer');
		$data['manufacturers'] = $this->model_catalog_manufacturer->getManufacturers();
		
		$data['customer_groups'] = $this->model_extension_excel_point->getCustomerGroups();
		
		$this->load->model('localisation/order_status');
		$data['order_statuses'] = $this->model_localisation_order_status->getOrderStatuses();
		
		$data['maxproduct_id'] = $this->model_extension_excel_point->getmaxproducts();
		$data['miniproduct_id'] = $this->model_extension_excel_point->getminiproducts();
		
		
		$data['minicustomer_id'] = $this->model_extension_excel_point->getminicustomers();
		$data['maxcustomer_id'] = $this->model_extension_excel_point->getmaxcustomers();
		
		$data['miniorder_id'] = $this->model_extension_excel_point->getminiorders();
		$data['maxorder_id'] = $this->model_extension_excel_point->getmaxorders();
		
		if(isset($this->request->get['filter_limit'])){
			$data['filter_limit'] = $this->request->get['filter_limit'];
		}else{
			$data['filter_limit'] = $this->config->get('config_limit_admin');
		}
		
		$data['header'] = $this->load->controller('common/header');
		$data['column_left'] = $this->load->controller('common/column_left');
		$data['footer'] = $this->load->controller('common/footer');

		$this->response->setOutput($this->load->view('extension/excel_point', $data));
	}
	
	public function userexport(){
		$this->load->model('user/user');
		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);
		$objPHPExcel->getActiveSheet()->setTitle("users");
		$i=1;
		$objPHPExcel->getActiveSheet()->setCellValue('A'.$i,'User ID')->getColumnDimension('A')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('B'.$i,'User Group ID')->getColumnDimension('B')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('C'.$i,'Username')->getColumnDimension('C')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('D'.$i,'First Name')->getColumnDimension('D')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('E'.$i,'Last Name')->getColumnDimension('E')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('F'.$i,'E-Mail')->getColumnDimension('F')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('G'.$i,'Image')->getColumnDimension('G')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('H'.$i,'Password')->getColumnDimension('H')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('I'.$i,'Salt')->getColumnDimension('I')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('J'.$i,'Status')->getColumnDimension('J')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('K'.$i,'Date Added')->getColumnDimension('K')->setAutoSize(true);
		
		if(isset($this->request->get['filter_start'])){
			$filter_start = $this->request->get['filter_start'];
		} else {
			$filter_start = 0;
		}

		if (isset($this->request->get['filter_limit'])) {
			$filter_limit = $this->request->get['filter_limit'];
		} else {
			$filter_limit = 0;
		}

		$filter_data = array(
		  'start' => $filter_start,
		  'limit' => $filter_limit
		);
		
		$results = $this->model_user_user->getUsers($filter_data);
		foreach($results as $result){
			$i++;
			$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $result['user_id'])->getColumnDimension('A')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $result['user_group_id'])->getColumnDimension('B')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $result['username'])->getColumnDimension('C')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $result['firstname'])->getColumnDimension('D')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $result['lastname'])->getColumnDimension('E')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, $result['email'])->getColumnDimension('F')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, $result['image'])->getColumnDimension('G')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, $result['password'])->getColumnDimension('H')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, $result['salt'])->getColumnDimension('I')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, $result['status'])->getColumnDimension('J')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, $result['date_added'])->getColumnDimension('K')->setAutoSize(true);
		}
		
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5'); 
		
		$filname ="userslist-".time().'.xls';
		header('Content-Type: application/vnd.ms-excel'); 
		header('Content-Disposition: attachment;filename='.$filname);
		header('Cache-Control: max-age=0'); 
		$objWriter->save('php://output'); 
		exit();
	}
	
	public function couponsexport(){
		$this->load->model('marketing/coupon');
		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);  
		$i=1;
		$objPHPExcel->getActiveSheet()->setCellValue('A'.$i,'Coupon ID')->getColumnDimension('A')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('B'.$i,'Coupon Name')->getColumnDimension('B')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('C'.$i,'Code (code must be unique)')->getColumnDimension('C')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('D'.$i,'Type')->getColumnDimension('D')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('E'.$i,'Discount')->getColumnDimension('E')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('F'.$i,'Total Amount')->getColumnDimension('F')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('G'.$i,'Customer Login')->getColumnDimension('G')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('H'.$i,'Free Shipping')->getColumnDimension('H')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('I'.$i,'Products')->getColumnDimension('I')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('J'.$i,'Category')->getColumnDimension('J')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('K'.$i,'Date Start')->getColumnDimension('K')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('L'.$i,'Date End')->getColumnDimension('L')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('M'.$i,'Uses Per Coupon')->getColumnDimension('M')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('N'.$i,'Uses Per Customer')->getColumnDimension('N')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('O'.$i,'Status')->getColumnDimension('O')->setAutoSize(true);
		
		$data['coupons'] = array();
		
		if (isset($this->request->get['filter_start'])) {
			$filter_start = $this->request->get['filter_start'];
		} else {
			$filter_start = 0;
		}

		if (isset($this->request->get['filter_limit'])) {
			$filter_limit = $this->request->get['filter_limit'];
		} else {
			$filter_limit = 0;
		}

		$filter_data = array(
			'start' => $filter_start,
			'limit' => $filter_limit
		);
		
		$results = $this->model_marketing_coupon->getCoupons($filter_data);
		foreach($results as $result){
		  $i++;
		  $coupon_data = $this->model_marketing_coupon->getCoupon($result['coupon_id']);
		  $products = $this->model_marketing_coupon->getCouponProducts($result['coupon_id']);
		  $categories = $this->model_marketing_coupon->getCouponCategories($result['coupon_id']);
		  $objPHPExcel->getActiveSheet()->setCellValue('A'.$i,$coupon_data['coupon_id'])->getColumnDimension('A')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('B'.$i,$coupon_data['name'])->getColumnDimension('B')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('C'.$i,$coupon_data['code'])->getColumnDimension('C')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('D'.$i,$coupon_data['type'])->getColumnDimension('D')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('E'.$i,$coupon_data['discount'])->getColumnDimension('E')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('F'.$i,$coupon_data['total'])->getColumnDimension('F')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('G'.$i,$coupon_data['logged'])->getColumnDimension('G')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('H'.$i,$coupon_data['shipping'])->getColumnDimension('H')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('I'.$i,(isset($products) ? implode(', ',$products) : ''))->getColumnDimension('I')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('J'.$i,(isset($categories) ? implode(', ',$categories) : ''))->getColumnDimension('J')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('K'.$i,$coupon_data['date_start'])->getColumnDimension('K')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('L'.$i,$coupon_data['date_end'])->getColumnDimension('L')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('M'.$i,$coupon_data['uses_total'])->getColumnDimension('M')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('N'.$i,$coupon_data['uses_customer'])->getColumnDimension('N')->setAutoSize(true);
		  $objPHPExcel->getActiveSheet()->setCellValue('O'.$i,$coupon_data['status'])->getColumnDimension('O')->setAutoSize(true);
		}
		
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5'); 
		
		$filname ="couponlist-".time().'.xls';
		header('Content-Type: application/vnd.ms-excel'); 
		header('Content-Disposition: attachment;filename='.$filname);
		header('Cache-Control: max-age=0'); 
		$objWriter->save('php://output'); 
		exit();
	}
	
	public function affilatesexport(){
		$this->load->model('customer/customer');
		$this->load->language('customer/customer');
		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);  
		$affiliates=array();
			
		if (isset($this->request->get['filter_status'])) {
			$filter_status = $this->request->get['filter_status'];
		} else {
			$filter_status = null;
		}

		/* if (isset($this->request->get['filter_approved'])) {
			$filter_approved = $this->request->get['filter_approved'];
		} else {
			$filter_approved = null;
		} */
		
		if (isset($this->request->get['filter_start'])) {
			$filter_start = $this->request->get['filter_start'];
		} else {
			$filter_start = 0;
		}

		if (isset($this->request->get['filter_limit'])) {
			$filter_limit = $this->request->get['filter_limit'];
		} else {
			$filter_limit = 0;
		}
			
		$filter_data = array(
			'filter_status'            => $filter_status,
			//'filter_approved'          => $filter_approved,
			'start'            		   => $filter_start,
			'limit'           	 	   => $filter_limit
		);
		
		$i=1;
		$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, 'Affiliate ID')->getColumnDimension('A')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, 'First Name')->getColumnDimension('B')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, 'Last Name')->getColumnDimension('C')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, 'E-Mail')->getColumnDimension('D')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, 'Telephone')->getColumnDimension('E')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, 'Fax')->getColumnDimension('F')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, 'Password')->getColumnDimension('G')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, 'Salt')->getColumnDimension('H')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, 'Company')->getColumnDimension('I')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, 'Web Site')->getColumnDimension('J')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, 'Address 1')->getColumnDimension('K')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, 'Address 2')->getColumnDimension('L')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, 'City')->getColumnDimension('M')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, 'Postcode')->getColumnDimension('N')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, 'Country ID')->getColumnDimension('O')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('P'.$i, 'Zone ID')->getColumnDimension('P')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, 'Tracking Code')->getColumnDimension('Q')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('R'.$i, 'Commission')->getColumnDimension('R')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('S'.$i, 'Tax ID')->getColumnDimension('S')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('T'.$i, 'Payment Method')->getColumnDimension('T')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('U'.$i, 'Cheque Payee Name')->getColumnDimension('U')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('V'.$i, 'PayPal Email Account')->getColumnDimension('V')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('W'.$i, 'Bank Name')->getColumnDimension('W')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('X'.$i, 'ABA/BSB number (Branch Number)')->getColumnDimension('X')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('Y'.$i, 'SWIFT Code')->getColumnDimension('Y')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('Z'.$i, 'Account Name')->getColumnDimension('Z')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AA'.$i, 'Account Number')->getColumnDimension('AA')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AB'.$i, 'IP')->getColumnDimension('AB')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AC'.$i, 'status')->getColumnDimension('AC')->setAutoSize(true);
		//$objPHPExcel->getActiveSheet()->setCellValue('AD'.$i, 'Approved')->getColumnDimension('AD')->setAutoSize(true);
			
		$results = $this->model_customer_customer->getAffiliate($filter_data);
		foreach($results as $result){
			$affiliate_info = $this->model_customer_customer->getAffiliate($result['affiliate_id']);
			if($affiliate_info){
				$i++;
				$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $affiliate_info['affiliate_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $affiliate_info['firstname']);
				$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $affiliate_info['lastname']);
				$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $affiliate_info['email']);
				$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $affiliate_info['telephone']);
				$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, $affiliate_info['fax']);
				$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, $affiliate_info['password']);
				$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, $affiliate_info['salt']);
				$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, $affiliate_info['company']);
				$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, $affiliate_info['website']);
				$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, $affiliate_info['address_1']);
				$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, $affiliate_info['address_2']);
				$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, $affiliate_info['city']);
				$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, $affiliate_info['postcode']);
				$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, $affiliate_info['country_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('P'.$i, $affiliate_info['zone_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, $affiliate_info['code']);
				$objPHPExcel->getActiveSheet()->setCellValue('R'.$i, $affiliate_info['commission']);
				$objPHPExcel->getActiveSheet()->setCellValue('S'.$i, $affiliate_info['tax']);
				$objPHPExcel->getActiveSheet()->setCellValue('T'.$i, $affiliate_info['payment']);
				$objPHPExcel->getActiveSheet()->setCellValue('U'.$i, $affiliate_info['cheque']);
				$objPHPExcel->getActiveSheet()->setCellValue('V'.$i, $affiliate_info['paypal']);
				$objPHPExcel->getActiveSheet()->setCellValue('W'.$i, $affiliate_info['bank_name']);
				$objPHPExcel->getActiveSheet()->setCellValue('X'.$i, $affiliate_info['bank_branch_number']);
				$objPHPExcel->getActiveSheet()->setCellValue('Y'.$i, $affiliate_info['bank_swift_code']);
				$objPHPExcel->getActiveSheet()->setCellValue('Z'.$i, $affiliate_info['bank_account_name']);
				$objPHPExcel->getActiveSheet()->setCellValue('AA'.$i, $affiliate_info['bank_account_number']);
				$objPHPExcel->getActiveSheet()->setCellValue('AB'.$i, $affiliate_info['ip']);
				$objPHPExcel->getActiveSheet()->setCellValue('AC'.$i, $affiliate_info['status']);
				//$objPHPExcel->getActiveSheet()->setCellValue('AD'.$i, $affiliate_info['approved']);
			}
		}
			
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5'); 
		
		$filname ="affiliateslist-".time().'.xls';
		header('Content-Type: application/vnd.ms-excel'); 
		header('Content-Disposition: attachment;filename='.$filname); 
		header('Cache-Control: max-age=0'); 
		$objWriter->save('php://output'); 
		exit(); 
	}
	
	public function exportOrder(){
		$this->load->model('sale/order');
		$this->load->language('extension/excel_point');
		$this->load->model('extension/excel_point');
		
		if (isset($this->request->post['filter_to_order_id'])) {
			$filter_to_order_id = $this->request->post['filter_to_order_id'];
		} else {
			$filter_to_order_id = null;
		}
		
		if (isset($this->request->post['filter_from_order_id'])) {
			$filter_from_order_id = $this->request->post['filter_from_order_id'];
		} else {
			$filter_from_order_id = null;
		}
		
		if (isset($this->request->post['filter_to_date_added'])) {
			$filter_to_date_added = $this->request->post['filter_to_date_added'];
		} else {
			$filter_to_date_added = null;
		}
		
		if (isset($this->request->post['filter_from_date_added'])) {
			$filter_from_date_added = $this->request->post['filter_from_date_added'];
		} else {
			$filter_from_date_added = null;
		}
		
		if (isset($this->request->post['filter_to_date_modified'])) {
			$filter_to_date_modified = $this->request->post['filter_to_date_modified'];
		} else {
			$filter_to_date_modified = null;
		}
		
		if (isset($this->request->post['filter_form_date_modified'])) {
			$filter_form_date_modified = $this->request->post['filter_form_date_modified'];
		} else {
			$filter_form_date_modified = null;
		}

		if (isset($this->request->post['filter_order_status'])) {
			$filter_order_status = $this->request->post['filter_order_status'];
		} else {
			$filter_order_status = null;
		}

		if (isset($this->request->post['filter_total'])) {
			$filter_total = $this->request->post['filter_total'];
		} else {
			$filter_total = null;
		}
		
		if (isset($this->request->post['filter_name'])) {
			$filter_name = $this->request->post['filter_name'];
		} else {
			$filter_name = null;
		}
		
		if (isset($this->request->post['filter_customer_group_id'])) {
			$filter_customer_group_id = $this->request->post['filter_customer_group_id'];
		} else {
			$filter_customer_group_id = null;
		}
		
		if (isset($this->request->post['filter_eformat'])) {
			$filter_eformat = $this->request->post['filter_eformat'];
		} else {
			$filter_eformat = null;
		}

		if (isset($this->request->get['sort'])) {
			$sort = $this->request->get['sort'];
		} else {
			$sort = 'o.order_id';
		}

		if (isset($this->request->get['order'])) {
			$order = $this->request->get['order'];
		} else {
			$order = 'DESC';
		}
		
		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}
		
		if (isset($this->request->post['filter_limit'])) {
			$filter_limit = $this->request->post['filter_limit'];
		} else {
			$filter_limit = 0;
		}

		$data['orders'] = array();
		
		$filter_data1 = array(
			'filter_to_date_added'   	=> $filter_to_date_added,
			'filter_from_date_added'    => $filter_from_date_added,
			'filter_to_date_modified'   => $filter_to_date_modified,
			'filter_form_date_modified' => $filter_form_date_modified,
			'filter_to_order_id'   		=> $filter_to_order_id,
			'filter_from_order_id' 		=> $filter_from_order_id,
			'filter_order_status'  		=> $filter_order_status,
			'filter_total'        		=> $filter_total,
			'filter_customer'        	=> $filter_name,
			'filter_customer_group_id'  => $filter_customer_group_id,
			'sort'                		=> $sort,
			'order'                		=> $order,
		);
		
		
		$order_total = $this->model_extension_excel_point->getTotalOrders($filter_data1);
		
		if($filter_limit){
		   $filter_limit = round($order_total/$filter_limit);
		}else{
			 $filter_limit = 0;
		}

		$filter_data = array(
			'filter_to_date_added'   	=> $filter_to_date_added,
			'filter_from_date_added'    => $filter_from_date_added,
			'filter_to_date_modified'   => $filter_to_date_modified,
			'filter_form_date_modified' => $filter_form_date_modified,
			'filter_to_order_id'   		=> $filter_to_order_id,
			'filter_from_order_id' 		=> $filter_from_order_id,
			'filter_order_status'  		=> $filter_order_status,
			'filter_total'        		=> $filter_total,
			'filter_customer'        	=> $filter_name,
			'filter_customer_group_id'  => $filter_customer_group_id,
			'sort'                		=> $sort,
			'order'                		=> $order,
			'start'                		=> ($page - 1) * $filter_limit,
			'limit'           			=> $filter_limit,
		);

		$results = $this->model_extension_excel_point->getOrders($filter_data);
		
		if($filter_eformat == 'xml'){
			$doc = new DOMDocument();
			$doc->formatOutput = true;
			$r = $doc->createElement("Orders");
			$doc->appendChild($r);
			foreach($results as $result){
				$result = $this->model_extension_excel_point->getOrder($result['order_id']);
				if($result){
					$Order = $doc->createElement("Order");
					
					$order_id = $doc->createElement("order_id");
					$order_id->appendChild($doc->createTextNode($result['order_id']));
					$Order->appendChild($order_id);
					
					$invoice_no = $doc->createElement("invoice_no");
					$invoice_no->appendChild($doc->createTextNode($result['invoice_no']));
					$Order->appendChild($invoice_no);
					
					$invoice_prefix = $doc->createElement("invoice_prefix");
					$invoice_prefix->appendChild($doc->createTextNode($result['invoice_prefix']));
					$Order->appendChild($invoice_prefix);
					
					$store_id = $doc->createElement("store_id");
					$store_id->appendChild($doc->createTextNode($result['store_id']));
					$Order->appendChild($store_id);
					
					$store_name = $doc->createElement("store_name");
					$store_name->appendChild($doc->createTextNode($result['store_name']));
					$Order->appendChild($store_name);
					
					$store_url = $doc->createElement("store_url");
					$store_url->appendChild($doc->createTextNode($result['store_url']));
					$Order->appendChild($store_url);
					
					$customer_id = $doc->createElement("customer_id");
					$customer_id->appendChild($doc->createTextNode($result['customer_id']));
					$Order->appendChild($customer_id);
					
					$customer = $doc->createElement("customer");
					$customer->appendChild($doc->createTextNode($result['customer']));
					$Order->appendChild($customer);
					
					$customer_group_id = $doc->createElement("customer_group_id");
					$customer_group_id->appendChild($doc->createTextNode($result['customer_group_id']));
					$Order->appendChild($customer_group_id);
					
					$firstname = $doc->createElement("firstname");
					$firstname->appendChild($doc->createTextNode($result['firstname']));
					$Order->appendChild($firstname);
					
					$lastname = $doc->createElement("lastname");
					$lastname->appendChild($doc->createTextNode($result['lastname']));
					$Order->appendChild($lastname);
					
					$email = $doc->createElement("email");
					$email->appendChild($doc->createTextNode($result['email']));
					$Order->appendChild($email);
					
					$telephone = $doc->createElement("telephone");
					$telephone->appendChild($doc->createTextNode($result['telephone']));
					$Order->appendChild($telephone);
					
					$fax = $doc->createElement("fax");
					$fax->appendChild($doc->createTextNode($result['fax']));
					$Order->appendChild($fax);
					
					$custom_field = $doc->createElement("custom_field");
					$custom_field->appendChild($doc->createTextNode($result['custom_field']));
					$Order->appendChild($custom_field);
					
					$payment_firstname = $doc->createElement("payment_firstname");
					$payment_firstname->appendChild($doc->createTextNode($result['payment_firstname']));
					$Order->appendChild($payment_firstname);
					
					$payment_lastname = $doc->createElement("payment_lastname");
					$payment_lastname->appendChild($doc->createTextNode($result['payment_lastname']));
					$Order->appendChild($payment_lastname);
					
					$payment_company = $doc->createElement("payment_company");
					$payment_company->appendChild($doc->createTextNode($result['payment_company']));
					$Order->appendChild($payment_company);
					
					$payment_address_1 = $doc->createElement("payment_address_1");
					$payment_address_1->appendChild($doc->createTextNode($result['payment_address_1']));
					$Order->appendChild($payment_address_1);
					
					$payment_address_2 = $doc->createElement("payment_address_2");
					$payment_address_2->appendChild($doc->createTextNode($result['payment_address_2']));
					$Order->appendChild($payment_address_2);
					
					$payment_postcode = $doc->createElement("payment_postcode");
					$payment_postcode->appendChild($doc->createTextNode($result['payment_postcode']));
					$Order->appendChild($payment_postcode);
					
					$payment_city = $doc->createElement("payment_city");
					$payment_city->appendChild($doc->createTextNode($result['payment_city']));
					$Order->appendChild($payment_city);
					
					$payment_zone_id = $doc->createElement("payment_zone_id");
					$payment_zone_id->appendChild($doc->createTextNode($result['payment_zone_id']));
					$Order->appendChild($payment_zone_id);
					
					$payment_zone = $doc->createElement("payment_zone");
					$payment_zone->appendChild($doc->createTextNode($result['payment_zone']));
					$Order->appendChild($payment_zone);
					
					$payment_zone_code = $doc->createElement("payment_zone_code");
					$payment_zone_code->appendChild($doc->createTextNode($result['payment_zone_code']));
					$Order->appendChild($payment_zone_code);
					
					$payment_country_id = $doc->createElement("payment_country_id");
					$payment_country_id->appendChild($doc->createTextNode($result['payment_country_id']));
					$Order->appendChild($payment_country_id);
					
					$payment_country = $doc->createElement("payment_country");
					$payment_country->appendChild($doc->createTextNode($result['payment_country']));
					$Order->appendChild($payment_country);
					
					$payment_iso_code_2 = $doc->createElement("payment_iso_code_2");
					$payment_iso_code_2->appendChild($doc->createTextNode($result['payment_iso_code_2']));
					$Order->appendChild($payment_iso_code_2);
					
					$payment_iso_code_3 = $doc->createElement("payment_iso_code_3");
					$payment_iso_code_3->appendChild($doc->createTextNode($result['payment_iso_code_3']));
					$Order->appendChild($payment_iso_code_3);
					
					$payment_address_format = $doc->createElement("payment_address_format");
					$payment_address_format->appendChild($doc->createTextNode($result['payment_address_format']));
					$Order->appendChild($payment_address_format);
					
					$payment_custom_field = $doc->createElement("payment_custom_field");
					$payment_custom_field->appendChild($doc->createTextNode($result['payment_custom_field']));
					$Order->appendChild($payment_custom_field);
					
					$payment_method = $doc->createElement("payment_method");
					$payment_method->appendChild($doc->createTextNode($result['payment_method']));
					$Order->appendChild($payment_method);
					
					$payment_code = $doc->createElement("payment_code");
					$payment_code->appendChild($doc->createTextNode($result['payment_code']));
					$Order->appendChild($payment_code);
					
					$shipping_firstname = $doc->createElement("shipping_firstname");
					$shipping_firstname->appendChild($doc->createTextNode($result['shipping_firstname']));
					$Order->appendChild($shipping_firstname);
					
					$shipping_lastname = $doc->createElement("shipping_lastname");
					$shipping_lastname->appendChild($doc->createTextNode($result['shipping_lastname']));
					$Order->appendChild($shipping_lastname);
					
					$shipping_company = $doc->createElement("shipping_company");
					$shipping_company->appendChild($doc->createTextNode($result['shipping_company']));
					$Order->appendChild($shipping_company);
					
					$shipping_address_1 = $doc->createElement("shipping_address_1");
					$shipping_address_1->appendChild($doc->createTextNode($result['shipping_address_1']));
					$Order->appendChild($shipping_address_1);
					
					$shipping_address_2 = $doc->createElement("shipping_address_2");
					$shipping_address_2->appendChild($doc->createTextNode($result['shipping_address_2']));
					$Order->appendChild($shipping_address_2);
					
					$shipping_postcode = $doc->createElement("shipping_postcode");
					$shipping_postcode->appendChild($doc->createTextNode($result['shipping_postcode']));
					$Order->appendChild($shipping_postcode);
					
					$shipping_city = $doc->createElement("shipping_city");
					$shipping_city->appendChild($doc->createTextNode($result['shipping_city']));
					$Order->appendChild($shipping_city);
					
					$shipping_zone_id = $doc->createElement("shipping_zone_id");
					$shipping_zone_id->appendChild($doc->createTextNode($result['shipping_zone_id']));
					$Order->appendChild($shipping_zone_id);
					
					$shipping_zone = $doc->createElement("shipping_zone");
					$shipping_zone->appendChild($doc->createTextNode($result['shipping_zone']));
					$Order->appendChild($shipping_zone);
					
					$shipping_zone_code = $doc->createElement("shipping_zone_code");
					$shipping_zone_code->appendChild($doc->createTextNode($result['shipping_zone_code']));
					$Order->appendChild($shipping_zone_code);
					
					$shipping_country_id = $doc->createElement("shipping_country_id");
					$shipping_country_id->appendChild($doc->createTextNode($result['shipping_country_id']));
					$Order->appendChild($shipping_country_id);
					
					$shipping_country = $doc->createElement("shipping_country");
					$shipping_country->appendChild($doc->createTextNode($result['shipping_country']));
					$Order->appendChild($shipping_country);
					
					$shipping_iso_code_2 = $doc->createElement("shipping_iso_code_2");
					$shipping_iso_code_2->appendChild($doc->createTextNode($result['shipping_iso_code_2']));
					$Order->appendChild($shipping_iso_code_2);
					
					$shipping_iso_code_3 = $doc->createElement("shipping_iso_code_3");
					$shipping_iso_code_3->appendChild($doc->createTextNode($result['shipping_iso_code_3']));
					$Order->appendChild($shipping_iso_code_3);
					
					$shipping_address_format = $doc->createElement("shipping_address_format");
					$shipping_address_format->appendChild($doc->createTextNode($result['shipping_address_format']));
					$Order->appendChild($shipping_address_format);
					
					$shipping_custom_field = $doc->createElement("shipping_custom_field");
					$shipping_custom_field->appendChild($doc->createTextNode($result['shipping_custom_field']));
					$Order->appendChild($shipping_custom_field);
					
					$shipping_method = $doc->createElement("shipping_method");
					$shipping_method->appendChild($doc->createTextNode($result['shipping_method']));
					$Order->appendChild($shipping_method);
					
					$shipping_code = $doc->createElement("shipping_code");
					$shipping_code->appendChild($doc->createTextNode($result['shipping_code']));
					$Order->appendChild($shipping_code);
					
					$comment = $doc->createElement("comment");
					$comment->appendChild($doc->createTextNode($result['comment']));
					$Order->appendChild($comment);

					$total = $doc->createElement("total");
					$total->appendChild($doc->createTextNode($result['total']));
					$Order->appendChild($total);
					
					$reward = $doc->createElement("reward");
					$reward->appendChild($doc->createTextNode($result['reward']));
					$Order->appendChild($reward);
					
					$order_status_id = $doc->createElement("order_status_id");
					$order_status_id->appendChild($doc->createTextNode($result['order_status_id']));
					$Order->appendChild($order_status_id);
					
					$affiliate_id = $doc->createElement("affiliate_id");
					$affiliate_id->appendChild($doc->createTextNode($result['affiliate_id']));
					$Order->appendChild($affiliate_id);
					
					$affiliate_firstname = $doc->createElement("affiliate_firstname");
					$affiliate_firstname->appendChild($doc->createTextNode($result['affiliate_firstname']));
					$Order->appendChild($affiliate_firstname);
					
					$affiliate_lastname = $doc->createElement("affiliate_lastname");
					$affiliate_lastname->appendChild($doc->createTextNode($result['affiliate_lastname']));
					$Order->appendChild($affiliate_lastname);
					
					$commission = $doc->createElement("commission");
					$commission->appendChild($doc->createTextNode($result['commission']));
					$Order->appendChild($commission);
					
					$language_id = $doc->createElement("language_id");
					$language_id->appendChild($doc->createTextNode($result['language_id']));
					$Order->appendChild($language_id);
					
					$language_code = $doc->createElement("language_code");
					$language_code->appendChild($doc->createTextNode($result['language_code']));
					$Order->appendChild($language_code);
					
					$language_directory = $doc->createElement("language_directory");
					$language_directory->appendChild($doc->createTextNode($result['language_directory']));
					$Order->appendChild($language_directory);
					
					$currency_id = $doc->createElement("currency_id");
					$currency_id->appendChild($doc->createTextNode($result['currency_id']));
					$Order->appendChild($currency_id);
					
					$currency_code = $doc->createElement("currency_code");
					$currency_code->appendChild($doc->createTextNode($result['currency_code']));
					$Order->appendChild($currency_code);
					
					$currency_value = $doc->createElement("currency_value");
					$currency_value->appendChild($doc->createTextNode($result['currency_value']));
					$Order->appendChild($currency_value);
					
					$ip = $doc->createElement("ip");
					$ip->appendChild($doc->createTextNode($result['ip']));
					$Order->appendChild($ip);
					
					$forwarded_ip = $doc->createElement("forwarded_ip");
					$forwarded_ip->appendChild($doc->createTextNode($result['forwarded_ip']));
					$Order->appendChild($forwarded_ip);
					
					$user_agent = $doc->createElement("user_agent");
					$user_agent->appendChild($doc->createTextNode($result['user_agent']));
					$Order->appendChild($user_agent);
					
					$accept_language = $doc->createElement("accept_language");
					$accept_language->appendChild($doc->createTextNode($result['accept_language']));
					$Order->appendChild($accept_language);
					
					$date_added = $doc->createElement("date_added");
					$date_added->appendChild($doc->createTextNode($result['date_added']));
					$Order->appendChild($date_added);
					
					$date_modified = $doc->createElement("date_modified");
					$date_modified->appendChild($doc->createTextNode($result['date_modified']));
					$Order->appendChild($date_modified);
					$order_products  = $this->model_sale_order->getOrderProducts($result['order_id']);
					$products = $doc->createElement("products");
					foreach($order_products as $orderproduct){
						  $product = $doc->createElement("product");
						 
						  $order_product_id = $doc->createElement("order_product_id");
						  $order_product_id->appendChild($doc->createTextNode($orderproduct['order_product_id']));
						  $product->appendChild($order_product_id);

						  $order_id = $doc->createElement("order_id");
						  $order_id->appendChild($doc->createTextNode($orderproduct['order_id']));
						  $product->appendChild($order_id);

						  $product_id = $doc->createElement("product_id");
						  $product_id->appendChild($doc->createTextNode($orderproduct['product_id']));
						  $product->appendChild($product_id);

						  $name = $doc->createElement("name");
						  $name->appendChild($doc->createTextNode($orderproduct['name']));
						  $product->appendChild($name);

						  $model = $doc->createElement("model");
						  $model->appendChild($doc->createTextNode($orderproduct['model']));
						  $product->appendChild($model);
						  
						  $quantity = $doc->createElement("quantity");
						  $quantity->appendChild($doc->createTextNode($orderproduct['quantity']));
						  $product->appendChild($quantity);
						  
						  $price = $doc->createElement("price");
						  $price->appendChild($doc->createTextNode($orderproduct['price']));
						  $product->appendChild($price);
						  
						  $total = $doc->createElement("total");
						  $total->appendChild($doc->createTextNode($orderproduct['total']));
						  $product->appendChild($total);

						  $tax = $doc->createElement("tax");
						  $tax->appendChild($doc->createTextNode($orderproduct['tax']));
						  $product->appendChild($tax);
						  
						  $reward = $doc->createElement("reward");
						  $reward->appendChild($doc->createTextNode($orderproduct['reward']));
						  $product->appendChild($reward);
						
						  $order_options = $doc->createElement("order_product_options");
							$order_product_options = $this->model_sale_order->getOrderOptions($result['order_id'],$orderproduct['order_product_id']);
							foreach($order_product_options as $option){
								$optionx = $doc->createElement("option");
								 
								$order_option_id = $doc->createElement("order_option_id");
								$order_option_id->appendChild($doc->createTextNode($option['order_option_id']));
								$optionx->appendChild($order_option_id);
								
								$order_id = $doc->createElement("order_id");
								$order_id->appendChild($doc->createTextNode($option['order_id']));
								$optionx->appendChild($order_id);
								
								$order_product_id = $doc->createElement("order_product_id");
								$order_product_id->appendChild($doc->createTextNode($option['order_product_id']));
								$optionx->appendChild($order_product_id);
								
								$product_option_id = $doc->createElement("product_option_id");
								$product_option_id->appendChild($doc->createTextNode($option['product_option_id']));
								$optionx->appendChild($product_option_id);
								
								$product_option_value_id = $doc->createElement("product_option_value_id");
								$product_option_value_id->appendChild($doc->createTextNode($option['product_option_value_id']));
								$optionx->appendChild($product_option_value_id);
								
								$name = $doc->createElement("name");
								$name->appendChild($doc->createTextNode($option['name']));
								$optionx->appendChild($name);
								
								$value = $doc->createElement("value");
								$value->appendChild($doc->createTextNode($option['value']));
								$optionx->appendChild($value);
								
								$type = $doc->createElement("type");
								$type->appendChild($doc->createTextNode($option['type']));
								$optionx->appendChild($type);
								$order_options->appendChild($optionx);
							}
						$product->appendChild($order_options);
						$products->appendChild($product);
					}
					$Order->appendChild($products);
					
					//Total
					$totals = $doc->createElement("totals");
					$order_totals  = $this->model_sale_order->getOrderTotals($result['order_id']);
					foreach($order_totals as $total){
						$totalx = $doc->createElement("total");
						
						$order_total_id = $doc->createElement("order_total_id");
						$order_total_id->appendChild($doc->createTextNode($total['order_total_id']));
						$totalx->appendChild($order_total_id);
						
						$order_id = $doc->createElement("order_id");
						$order_id->appendChild($doc->createTextNode($total['order_id']));
						$totalx->appendChild($order_id);
						
						$code = $doc->createElement("code");
						$code->appendChild($doc->createTextNode($total['code']));
						$totalx->appendChild($code);
						
						$title = $doc->createElement("title");
						$title->appendChild($doc->createTextNode($total['title']));
						$totalx->appendChild($title);
						
						$value = $doc->createElement("value");
						$value->appendChild($doc->createTextNode($total['value']));
						$totalx->appendChild($value);
						
						$sort_order = $doc->createElement("sort_order");
						$sort_order->appendChild($doc->createTextNode($total['sort_order']));
						$totalx->appendChild($sort_order);
						
						$totals->appendChild($totalx);
					}
					$Order->appendChild($totals);
					
					
					//history
					$historys = $doc->createElement("historys");
					$order_historys  = $this->model_extension_excel_point->getOrderexportHistories($result['order_id']);
					foreach($order_historys as $history){
						$historyx = $doc->createElement("history");
						
						$order_history_id = $doc->createElement("order_history_id");
						$order_history_id->appendChild($doc->createTextNode($history['order_history_id']));
						$historyx->appendChild($order_history_id);
						
						$order_id = $doc->createElement("order_id");
						$order_id->appendChild($doc->createTextNode($history['order_id']));
						$historyx->appendChild($order_id);
						
						$order_status_id = $doc->createElement("order_status_id");
						$order_status_id->appendChild($doc->createTextNode($history['order_status_id']));
						$historyx->appendChild($order_status_id);
						
						$name = $doc->createElement("name");
						$name->appendChild($doc->createTextNode($history['name']));
						$historyx->appendChild($name);
						
						$notify = $doc->createElement("notify");
						$notify->appendChild($doc->createTextNode($history['notify']));
						$historyx->appendChild($notify);
						
						$comment = $doc->createElement("comment");
						$comment->appendChild($doc->createTextNode($history['comment']));
						$historyx->appendChild($comment);
						
						$date_added = $doc->createElement("date_added");
						$date_added->appendChild($doc->createTextNode($history['date_added']));
						$historyx->appendChild($date_added);
						
						$historys->appendChild($historyx);
					}
					
					$Order->appendChild($historys);
					
					//Voucher
					$Vouchers = $doc->createElement("Vouchers");
					$order_vouchers  = $this->model_sale_order->getOrderVouchers($result['order_id']);
					foreach($order_vouchers as $voucher){
						$voucherx = $doc->createElement("voucher");
						
						$order_voucher_id = $doc->createElement("order_voucher_id");
						$order_voucher_id->appendChild($doc->createTextNode($voucher['order_voucher_id']));
						$voucherx->appendChild($order_voucher_id);
						
						$order_id = $doc->createElement("order_id");
						$order_id->appendChild($doc->createTextNode($voucher['order_id']));
						$voucherx->appendChild($order_id);
						
						$voucher_id = $doc->createElement("voucher_id");
						$voucher_id->appendChild($doc->createTextNode($voucher['voucher_id']));
						$voucherx->appendChild($voucher_id);
						
						$description = $doc->createElement("description");
						$description->appendChild($doc->createTextNode($voucher['description']));
						$voucherx->appendChild($description);
						
						$code = $doc->createElement("code");
						$code->appendChild($doc->createTextNode($voucher['code']));
						$voucherx->appendChild($code);
						
						$from_name = $doc->createElement("from_name");
						$from_name->appendChild($doc->createTextNode($voucher['from_name']));
						$voucherx->appendChild($from_name);
						
						$from_email = $doc->createElement("from_email");
						$from_email->appendChild($doc->createTextNode($voucher['from_email']));
						$voucherx->appendChild($from_email);
						
						$to_name = $doc->createElement("to_name");
						$to_name->appendChild($doc->createTextNode($voucher['to_name']));
						$voucherx->appendChild($to_name);
						
						$to_email = $doc->createElement("to_email");
						$to_email->appendChild($doc->createTextNode($voucher['to_email']));
						$voucherx->appendChild($to_email);
						
						$voucher_theme_id = $doc->createElement("voucher_theme_id");
						$voucher_theme_id->appendChild($doc->createTextNode($voucher['voucher_theme_id']));
						$voucherx->appendChild($voucher_theme_id);
						
						$message = $doc->createElement("message");
						$message->appendChild($doc->createTextNode($voucher['message']));
						$voucherx->appendChild($message);
						
						$amount = $doc->createElement("amount");
						$amount->appendChild($doc->createTextNode($voucher['amount']));
						$voucherx->appendChild($amount);
						
						$Vouchers->appendChild($voucherx);
					}
					$Order->appendChild($Vouchers);
					
					$r->appendChild($Order);
				}
			}
			$mask = time();
			$doc->saveXML();
			$doc->save(DIR_EXCEL."order".$mask.".xml");
		}else{
			
		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);
		
		$i=1;
		
		$objPHPExcel->getActiveSheet()->setTitle("Order");
		
		//Change Cell Format 
		$objPHPExcel->getActiveSheet()->getStyle('BA')->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_GENERAL);
		$objPHPExcel->getActiveSheet()->getStyle('BM')->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_GENERAL);
		$objPHPExcel->getActiveSheet()->getStyle('BG')->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_GENERAL);
		
		
		$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $this->language->get('entry_order_id'))->getColumnDimension('A')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $this->language->get('text_invoice_no'))->getColumnDimension('B')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $this->language->get('text_invoice_prefix'))->getColumnDimension('C')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $this->language->get('entry_store_id'))->getColumnDimension('D')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $this->language->get('entry_store'))->getColumnDimension('E')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, $this->language->get('entry_store_url'))->getColumnDimension('F')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, $this->language->get('entry_customer_id'))->getColumnDimension('G')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, $this->language->get('entry_customer'))->getColumnDimension('H')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, $this->language->get('entry_customergroup_id'))->getColumnDimension('I')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, $this->language->get('entry_firstname'))->getColumnDimension('J')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, $this->language->get('entry_lastname'))->getColumnDimension('K')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, $this->language->get('entry_email'))->getColumnDimension('L')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, $this->language->get('entry_telephone'))->getColumnDimension('M')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, $this->language->get('entry_fax'))->getColumnDimension('N')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, $this->language->get('text_custom_field'))->getColumnDimension('O')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('P'.$i, $this->language->get('text_payment_firstname'))->getColumnDimension('P')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, $this->language->get('text_payment_lastname'))->getColumnDimension('Q')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('R'.$i, $this->language->get('text_payment_company'))->getColumnDimension('R')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('S'.$i, $this->language->get('text_payment_address_1'))->getColumnDimension('S')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('T'.$i, $this->language->get('text_payment_address_2'))->getColumnDimension('T')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('U'.$i, $this->language->get('text_payment_postcode'))->getColumnDimension('U')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('V'.$i, $this->language->get('text_payment_city'))->getColumnDimension('V')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('W'.$i, $this->language->get('text_payment_zone_id'))->getColumnDimension('W')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('X'.$i, $this->language->get('text_payment_zone'))->getColumnDimension('X')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('Y'.$i, $this->language->get('text_payment_zone_code'))->getColumnDimension('Y')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('Z'.$i, $this->language->get('text_payment_country_id'))->getColumnDimension('Z')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AA'.$i,$this->language->get('text_payment_country'))->getColumnDimension('AA')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AB'.$i, $this->language->get('text_payment_iso_code_2'))->getColumnDimension('AB')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AC'.$i, $this->language->get('text_payment_iso_code_3'))->getColumnDimension('AC')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AD'.$i, $this->language->get('text_payment_address_format'))->getColumnDimension('AD')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AE'.$i, $this->language->get('text_payment_custom_field'))->getColumnDimension('AE')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AF'.$i, $this->language->get('text_payment_method'))->getColumnDimension('AF')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AG'.$i, $this->language->get('text_payment_code'))->getColumnDimension('AG')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AH'.$i, $this->language->get('text_shipping_firstname'))->getColumnDimension('AH')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AI'.$i, $this->language->get('text_shipping_lastname'))->getColumnDimension('AI')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AJ'.$i, $this->language->get('text_shipping_company'))->getColumnDimension('AJ')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AK'.$i, $this->language->get('text_shipping_address_1'))->getColumnDimension('AK')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AL'.$i, $this->language->get('text_shipping_address_2'))->getColumnDimension('AL')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AM'.$i, $this->language->get('text_shipping_postcode'))->getColumnDimension('AM')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AN'.$i, $this->language->get('text_shipping_city'))->getColumnDimension('AN')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AO'.$i, $this->language->get('text_shipping_zone_id'))->getColumnDimension('AO')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AP'.$i, $this->language->get('text_shipping_zone'))->getColumnDimension('AP')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AQ'.$i, $this->language->get('text_shipping_zone_code'))->getColumnDimension('AQ')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AR'.$i, $this->language->get('text_shipping_country_id'))->getColumnDimension('AR')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AS'.$i, $this->language->get('text_shipping_country'))->getColumnDimension('AS')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AT'.$i, $this->language->get('text_shipping_iso_code_2'))->getColumnDimension('AT')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AU'.$i, $this->language->get('text_shipping_iso_code_3'))->getColumnDimension('AU')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AV'.$i, $this->language->get('text_shipping_address_format'))->getColumnDimension('AV')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AW'.$i, $this->language->get('text_shipping_custom_field'))->getColumnDimension('AW')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AX'.$i, $this->language->get('text_shipping_method'))->getColumnDimension('AX')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AY'.$i, $this->language->get('text_shipping_code'))->getColumnDimension('AY')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('AZ'.$i, $this->language->get('text_comment'))->getColumnDimension('AZ')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BA'.$i, $this->language->get('text_total'))->getColumnDimension('BA')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BB'.$i, $this->language->get('text_reward'))->getColumnDimension('BB')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BC'.$i, $this->language->get('text_order_status_id'))->getColumnDimension('BC')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BD'.$i, $this->language->get('text_affiliate_id'))->getColumnDimension('BD')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BE'.$i, $this->language->get('text_affiliate_firstname'))->getColumnDimension('BE')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BF'.$i, $this->language->get('text_affiliate_lastname'))->getColumnDimension('BF')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BG'.$i, $this->language->get('text_commission'))->getColumnDimension('BG')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BH'.$i, $this->language->get('text_language_id'))->getColumnDimension('BH')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BI'.$i, $this->language->get('text_language_code'))->getColumnDimension('BI')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BJ'.$i, $this->language->get('text_language_directory'))->getColumnDimension('BJ')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BK'.$i, $this->language->get('text_currency_id'))->getColumnDimension('BK')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BL'.$i, $this->language->get('text_currency_code'))->getColumnDimension('BL')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BM'.$i, $this->language->get('text_currency_value'))->getColumnDimension('BM')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BN'.$i, $this->language->get('text_ip'))->getColumnDimension('BN')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BO'.$i, $this->language->get('text_forwarded_ip'))->getColumnDimension('BO')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BP'.$i, $this->language->get('text_user_agent'))->getColumnDimension('BP')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BQ'.$i, $this->language->get('text_accept_language'))->getColumnDimension('BQ')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BR'.$i, $this->language->get('text_date_added'))->getColumnDimension('BR')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('BS'.$i, $this->language->get('text_date_modified'))->getColumnDimension('BS')->setAutoSize(true);
		
		
		//Order Products
		$u=1;
		$objWorkSheet = $objPHPExcel->createSheet(1);
		$objWorkSheet->setTitle("Order Product");

		$objWorkSheet->setCellValue('A'.$u, 'Order product ID')->getColumnDimension('A')->setAutoSize(true);
		$objWorkSheet->setCellValue('B'.$u, 'Order ID')->getColumnDimension('B')->setAutoSize(true);
		$objWorkSheet->setCellValue('C'.$u, 'Product ID')->getColumnDimension('C')->setAutoSize(true);
		$objWorkSheet->setCellValue('D'.$u, 'Product')->getColumnDimension('D')->setAutoSize(true);
		$objWorkSheet->setCellValue('E'.$u, 'Model')->getColumnDimension('E')->setAutoSize(true);
		$objWorkSheet->setCellValue('F'.$u, 'Quantity')->getColumnDimension('F')->setAutoSize(true);
		$objWorkSheet->setCellValue('G'.$u, 'Price')->getColumnDimension('G')->setAutoSize(true);
		$objWorkSheet->setCellValue('H'.$u, 'Total')->getColumnDimension('H')->setAutoSize(true);
		$objWorkSheet->setCellValue('I'.$u, 'Tax')->getColumnDimension('I')->setAutoSize(true);
		$objWorkSheet->setCellValue('J'.$u, 'Reward')->getColumnDimension('J')->setAutoSize(true);
		
		
		//Order Option
		
		$o=1;
		$objoptionWorkSheet = $objPHPExcel->createSheet(2);
		$objoptionWorkSheet->setTitle("Order Product Option");
		$objoptionWorkSheet->setCellValue('A'.$u, 'Order Option ID')->getColumnDimension('A')->setAutoSize(true);
		$objoptionWorkSheet->setCellValue('B'.$u, 'Order ID')->getColumnDimension('B')->setAutoSize(true);
		$objoptionWorkSheet->setCellValue('C'.$u, 'Order Product ID')->getColumnDimension('C')->setAutoSize(true);
		$objoptionWorkSheet->setCellValue('D'.$u, 'Product Option ID')->getColumnDimension('D')->setAutoSize(true);
		$objoptionWorkSheet->setCellValue('E'.$u, 'Product Option Value ID')->getColumnDimension('E')->setAutoSize(true);
		$objoptionWorkSheet->setCellValue('F'.$u, 'Option Name')->getColumnDimension('F')->setAutoSize(true);
		$objoptionWorkSheet->setCellValue('G'.$u, 'Option Value')->getColumnDimension('G')->setAutoSize(true);
		$objoptionWorkSheet->setCellValue('H'.$u, 'Option Type')->getColumnDimension('H')->setAutoSize(true);
		
		
		///Order Total
		$t=1;
		$objtotalWorkSheet = $objPHPExcel->createSheet(3);
		$objtotalWorkSheet->setTitle("Order Total");
		$objtotalWorkSheet->setCellValue('A'.$t, 'Order Total ID')->getColumnDimension('A')->setAutoSize(true);
		$objtotalWorkSheet->setCellValue('B'.$t, 'Order ID')->getColumnDimension('B')->setAutoSize(true);
		$objtotalWorkSheet->setCellValue('C'.$t, 'Code')->getColumnDimension('C')->setAutoSize(true);
		$objtotalWorkSheet->setCellValue('D'.$t, 'Title')->getColumnDimension('D')->setAutoSize(true);
		$objtotalWorkSheet->setCellValue('E'.$t, 'Value')->getColumnDimension('E')->setAutoSize(true);
		$objtotalWorkSheet->setCellValue('F'.$t, 'Sort order')->getColumnDimension('F')->setAutoSize(true);
		
		//Order History
		$h=1;
		$objhistoryWorkSheet = $objPHPExcel->createSheet(4);
		$objhistoryWorkSheet->setTitle("Order History");
		$objhistoryWorkSheet->setCellValue('A'.$h, 'Order History ID')->getColumnDimension('A')->setAutoSize(true);
		$objhistoryWorkSheet->setCellValue('B'.$h, 'Order ID')->getColumnDimension('B')->setAutoSize(true);
		$objhistoryWorkSheet->setCellValue('C'.$h, 'Order Status ID')->getColumnDimension('C')->setAutoSize(true);
		$objhistoryWorkSheet->setCellValue('D'.$h, 'Order Status')->getColumnDimension('D')->setAutoSize(true);
		$objhistoryWorkSheet->setCellValue('E'.$h, 'Notify')->getColumnDimension('E')->setAutoSize(true);
		$objhistoryWorkSheet->setCellValue('F'.$h, 'Comment')->getColumnDimension('F')->setAutoSize(true);
		$objhistoryWorkSheet->setCellValue('G'.$h, 'Date Added')->getColumnDimension('G')->setAutoSize(true);
		
		//Order Voucher
		$v=1;
		$objVoucherWorkSheet = $objPHPExcel->createSheet(5);
		$objVoucherWorkSheet->setTitle("Order Voucher");
		$objVoucherWorkSheet->setCellValue('A'.$v, 'Order Voucher ID')->getColumnDimension('A')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('B'.$v, 'Order ID')->getColumnDimension('B')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('C'.$v, 'Voucher ID')->getColumnDimension('C')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('D'.$v, 'Description')->getColumnDimension('D')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('E'.$v, 'Code')->getColumnDimension('E')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('F'.$v, 'From Name')->getColumnDimension('F')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('G'.$v, 'From Email')->getColumnDimension('G')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('H'.$v, 'To Name')->getColumnDimension('H')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('I'.$v, 'Voucher Theme ID')->getColumnDimension('I')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('J'.$v, 'Message')->getColumnDimension('J')->setAutoSize(true);
		$objVoucherWorkSheet->setCellValue('K'.$v, 'Amount')->getColumnDimension('K')->setAutoSize(true);
			
		foreach($results as $value){
			$result = $this->model_extension_excel_point->getOrder($value['order_id']);
			$i++;
				$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $result['order_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $result['invoice_no']);
				$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $result['invoice_prefix']);
				$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $result['store_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $result['store_name']);
				$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, $result['store_url']);
				$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, $result['customer_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, $result['customer']);
				$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, $result['customer_group_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, $result['firstname']);
				$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, $result['lastname']);
				$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, $result['email']);
				$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, $result['telephone']);
				$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, $result['fax']);
				$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, $result['custom_field']);
				$objPHPExcel->getActiveSheet()->setCellValue('P'.$i, $result['payment_firstname']);
				$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, $result['payment_lastname']);
				$objPHPExcel->getActiveSheet()->setCellValue('R'.$i, $result['payment_company']);
				$objPHPExcel->getActiveSheet()->setCellValue('S'.$i, $result['payment_address_1']);
				$objPHPExcel->getActiveSheet()->setCellValue('T'.$i, $result['payment_address_2']);
				$objPHPExcel->getActiveSheet()->setCellValue('U'.$i, $result['payment_postcode']);
				$objPHPExcel->getActiveSheet()->setCellValue('V'.$i, $result['payment_city']);
				$objPHPExcel->getActiveSheet()->setCellValue('W'.$i, $result['payment_zone_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('X'.$i, $result['payment_zone']);
				$objPHPExcel->getActiveSheet()->setCellValue('Y'.$i, $result['payment_zone_code']);
				$objPHPExcel->getActiveSheet()->setCellValue('Z'.$i, $result['payment_country_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('AA'.$i, $result['payment_country']);
				$objPHPExcel->getActiveSheet()->setCellValue('AB'.$i, $result['payment_iso_code_2']);
				$objPHPExcel->getActiveSheet()->setCellValue('AC'.$i, $result['payment_iso_code_3']);
				$objPHPExcel->getActiveSheet()->setCellValue('AD'.$i, $result['payment_address_format']);
				$objPHPExcel->getActiveSheet()->setCellValue('AE'.$i, $result['payment_custom_field']);
				$objPHPExcel->getActiveSheet()->setCellValue('AF'.$i, $result['payment_method']);
				$objPHPExcel->getActiveSheet()->setCellValue('AG'.$i, $result['payment_code']);
				
				$objPHPExcel->getActiveSheet()->setCellValue('AH'.$i, $result['shipping_firstname']);
				$objPHPExcel->getActiveSheet()->setCellValue('AI'.$i, $result['shipping_lastname']);
				$objPHPExcel->getActiveSheet()->setCellValue('AJ'.$i, $result['shipping_company']);
				$objPHPExcel->getActiveSheet()->setCellValue('AK'.$i, $result['shipping_address_1']);
				$objPHPExcel->getActiveSheet()->setCellValue('AL'.$i, $result['shipping_address_2']);
				$objPHPExcel->getActiveSheet()->setCellValue('AM'.$i, $result['shipping_postcode']);
				$objPHPExcel->getActiveSheet()->setCellValue('AN'.$i, $result['shipping_city']);
				$objPHPExcel->getActiveSheet()->setCellValue('AO'.$i, $result['shipping_zone_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('AP'.$i, $result['shipping_zone']);
				
				$objPHPExcel->getActiveSheet()->setCellValue('AQ'.$i, $result['shipping_zone_code']);
				$objPHPExcel->getActiveSheet()->setCellValue('AR'.$i, $result['shipping_country_id']);
				
				$objPHPExcel->getActiveSheet()->setCellValue('AS'.$i, $result['shipping_country']);
				$objPHPExcel->getActiveSheet()->setCellValue('AT'.$i, $result['shipping_iso_code_2']);
				$objPHPExcel->getActiveSheet()->setCellValue('AU'.$i, $result['shipping_iso_code_3']);
				$objPHPExcel->getActiveSheet()->setCellValue('AV'.$i, $result['shipping_address_format']);
				
				$objPHPExcel->getActiveSheet()->setCellValue('AW'.$i, $result['shipping_custom_field']);
				
				$objPHPExcel->getActiveSheet()->setCellValue('AX'.$i, $result['shipping_method']);
				
				$objPHPExcel->getActiveSheet()->setCellValue('AY'.$i, $result['shipping_code']);
				$objPHPExcel->getActiveSheet()->setCellValue('AZ'.$i, $result['comment']);
				$objPHPExcel->getActiveSheet()->setCellValue('BA'.$i, sprintf("%0.2f", $result['total']));
				
				
				$objPHPExcel->getActiveSheet()->setCellValue('BB'.$i, $result['reward']);
				$objPHPExcel->getActiveSheet()->setCellValue('BC'.$i, $result['order_status_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('BD'.$i, $result['affiliate_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('BE'.$i, $result['affiliate_firstname']);
				$objPHPExcel->getActiveSheet()->setCellValue('BF'.$i, $result['affiliate_lastname']);
				$objPHPExcel->getActiveSheet()->setCellValue('BG'.$i, $result['commission']);
				$objPHPExcel->getActiveSheet()->setCellValue('BH'.$i, $result['language_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('BI'.$i, $result['language_code']);
				$objPHPExcel->getActiveSheet()->setCellValue('BJ'.$i, $result['language_directory']);
				$objPHPExcel->getActiveSheet()->setCellValue('BK'.$i, $result['currency_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('BL'.$i, $result['currency_code']);
				$objPHPExcel->getActiveSheet()->setCellValue('BM'.$i, $result['currency_value']);
				$objPHPExcel->getActiveSheet()->setCellValue('BN'.$i, $result['ip']);
				$objPHPExcel->getActiveSheet()->setCellValue('BO'.$i, $result['forwarded_ip']);
				$objPHPExcel->getActiveSheet()->setCellValue('BP'.$i, $result['user_agent']);
				$objPHPExcel->getActiveSheet()->setCellValue('BQ'.$i, $result['accept_language']);
				$objPHPExcel->getActiveSheet()->setCellValue('BR'.$i, $result['date_added']);
				$objPHPExcel->getActiveSheet()->setCellValue('BS'.$i, $result['date_modified']);
			
				$order_products  = $this->model_sale_order->getOrderProducts($result['order_id']);
				foreach($order_products as $orderproduct){
					$u++;
					$objWorkSheet->setCellValue('A'.$u, $orderproduct['order_product_id']);
					$objWorkSheet->setCellValue('B'.$u, $orderproduct['order_id']);
					$objWorkSheet->setCellValue('C'.$u, $orderproduct['product_id']);
					$objWorkSheet->setCellValue('D'.$u, $orderproduct['name']);
					$objWorkSheet->setCellValue('E'.$u, $orderproduct['model']);
					$objWorkSheet->setCellValue('F'.$u, $orderproduct['quantity']);
					$objWorkSheet->setCellValue('G'.$u, $orderproduct['price']);
					$objWorkSheet->setCellValue('H'.$u, $orderproduct['total']);
					$objWorkSheet->setCellValue('I'.$u, $orderproduct['tax']);
					$objWorkSheet->setCellValue('J'.$u, $orderproduct['reward']);
					
					$order_product_options = $this->model_sale_order->getOrderOptions($result['order_id'],$orderproduct['order_product_id']);
					foreach($order_product_options as $option){
						$o++;
						$objoptionWorkSheet->setCellValue('A'.$o, $option['order_option_id']);
						$objoptionWorkSheet->setCellValue('B'.$o, $option['order_id']);
						$objoptionWorkSheet->setCellValue('C'.$o, $option['order_product_id']);
						$objoptionWorkSheet->setCellValue('D'.$o, $option['product_option_id']);
						$objoptionWorkSheet->setCellValue('E'.$o, $option['product_option_value_id']);
						$objoptionWorkSheet->setCellValue('F'.$o, $option['name']);
						$objoptionWorkSheet->setCellValue('G'.$o, $option['value']);
						$objoptionWorkSheet->setCellValue('H'.$o, $option['type']);
					}
				}
			
				//totals
				$order_totals  = $this->model_sale_order->getOrderTotals($result['order_id']);
				foreach($order_totals as $total){
					$t++;
					$objtotalWorkSheet->setCellValue('A'.$t, $total['order_total_id']);
					$objtotalWorkSheet->setCellValue('B'.$t, $total['order_id']);
					$objtotalWorkSheet->setCellValue('C'.$t, $total['code']);
					$objtotalWorkSheet->setCellValue('D'.$t, $total['title']);
					$objtotalWorkSheet->setCellValue('E'.$t, sprintf("%0.2f", $total['value']));
					$objtotalWorkSheet->setCellValue('F'.$t, $total['sort_order']);
				}
			
				//history
				$order_historys  = $this->model_extension_excel_point->getOrderexportHistories($result['order_id']);
				foreach($order_historys as $history){
					$h++;
					$objhistoryWorkSheet->setCellValue('A'.$h, $history['order_history_id']);
					$objhistoryWorkSheet->setCellValue('B'.$h, $history['order_id']);
					$objhistoryWorkSheet->setCellValue('C'.$h, $history['order_status_id']);
					$objhistoryWorkSheet->setCellValue('D'.$h, $history['name']);
					$objhistoryWorkSheet->setCellValue('E'.$h, $history['notify']);
					$objhistoryWorkSheet->setCellValue('F'.$h, $history['comment']);
					$objhistoryWorkSheet->setCellValue('G'.$h, $history['date_added']);
				}
			
				//Voucher
				$order_vouchers  = $this->model_sale_order->getOrderVouchers($result['order_id']);
				foreach($order_vouchers as $voucher){
					$v++;
					$objVoucherWorkSheet->setCellValue('A'.$v, $voucher['order_voucher_id']);
					$objVoucherWorkSheet->setCellValue('B'.$v, $voucher['order_id']);
					$objVoucherWorkSheet->setCellValue('C'.$v, $voucher['voucher_id']);
					$objVoucherWorkSheet->setCellValue('D'.$v, $voucher['description']);
					$objVoucherWorkSheet->setCellValue('E'.$v, $voucher['code']);
					$objVoucherWorkSheet->setCellValue('F'.$v, $voucher['from_name']);
					$objVoucherWorkSheet->setCellValue('G'.$v, $voucher['from_email']);
					$objVoucherWorkSheet->setCellValue('H'.$v, $voucher['to_name']);
					$objVoucherWorkSheet->setCellValue('I'.$v, $voucher['to_email']);
					$objVoucherWorkSheet->setCellValue('J'.$v, $voucher['voucher_theme_id']);
					$objVoucherWorkSheet->setCellValue('K'.$v, $voucher['message']);
					$objVoucherWorkSheet->setCellValue('L'.$v, $voucher['amount']);
				}
			}
			
			$mask = time();
			if($filter_eformat == 'xls'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
				$filename = 'order'.$mask.'.xls';
			}elseif($filter_eformat == 'xlsx'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
				$filename = 'order'.$mask.'.xlsx';
			}
			$objWriter->save(DIR_EXCEL . $filename);
		}
		
		if($filter_limit){
			$start = ($page - 1) * $filter_limit;
			$end = $start + $filter_limit;
			if($end < $order_total){
				$json['next'] = str_replace('&amp;', '&', $this->url->link('extension/excel_export/exportOrder', 'user_token=' . $this->session->data['user_token'] . '&page=' . ($page + 1), true));
			} else {
				$json['next'] = '';
				$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filter_eformat='.$filter_eformat.'&filename=order&mask='.$mask.'&batches='.true);
			}
		}else{
			$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filename=order&filter_eformat='.$filter_eformat.'&mask='.$mask.'&batches='.false);
		}
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function exportCustomerGroup(){
		$this->load->model('extension/excel_point');
		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);
		
		$i=1;
		$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, 'Customer Group ID')->getColumnDimension('A')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, 'Customer Group Name')->getColumnDimension('B')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, 'Description')->getColumnDimension('C')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, 'Approve New Customers')->getColumnDimension('D')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, 'Sort Order')->getColumnDimension('E')->setAutoSize(true);
		
		if(!empty($this->request->get['filter_language'])){
			$language_id = $this->request->get['filter_language'];
		}else{
			$language_id = $this->config->get('config_language_id');
		}
	
		if(!empty($this->request->get['filter_start'])) {
			$filter_start = $this->request->get['filter_start'];
		} else {
			$filter_start = 0;
		}
		
		if(!empty($this->request->get['filter_limit'])) {
			$filter_limit = $this->request->get['filter_limit'];
		} else {
			$filter_limit = 0;
		}
		
		$filter_data=array(
			'language_id'		=> $language_id,
			'start'           	=> $filter_start,
			'limit'           	=> $filter_limit,
		);
		
		$results = $this->model_extension_excel_point->getCustomerGroupsdata($filter_data);
		foreach($results as $result){
			$i++;
			$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $result['customer_group_id'])->getColumnDimension('A')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $result['name'])->getColumnDimension('B')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $result['description'])->getColumnDimension('C')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $result['approval'])->getColumnDimension('D')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $result['sort_order'])->getColumnDimension('E')->setAutoSize(true);
		}
		
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5'); 
		
		$filename = 'customerGroupList'.time().'.xls';
		header('Content-Type: application/vnd.ms-excel'); 
		header('Content-Disposition: attachment;filename='.$filename);
		header('Cache-Control: max-age=0'); 

		$objWriter->save('php://output'); 
		exit(); 
	}
	
	public function exportCustomer(){
		$this->load->model('extension/excel_point');
		$this->load->model('setting/store');
		
		$customers=array();
		if(isset($this->request->post['customer_name'])) {
			$filter_name = $this->request->post['customer_name'];
		}else{
			$filter_name = null;
		}

		if(isset($this->request->post['filter_email'])) {
			$filter_email = $this->request->post['filter_email'];
		}else{
			$filter_email = null;
		}

		if(isset($this->request->post['filter_customer_group_id'])) {
			$filter_customer_group_id = $this->request->post['filter_customer_group_id'];
		}else{
			$filter_customer_group_id = null;
		}

		if(isset($this->request->post['filter_status'])) {
			$filter_status = $this->request->post['filter_status'];
		}else{
			$filter_status = null;
		}

		/* if (isset($this->request->post['filter_approved'])) {
			$filter_approved = $this->request->post['filter_approved'];
		} else {
			$filter_approved = null;
		} */
		
		if (isset($this->request->post['filter_to_date'])) {
			$filter_to_date = $this->request->post['filter_to_date'];
		} else {
			$filter_to_date = null;
		}
		
		if (isset($this->request->post['filter_from_date'])) {
			$filter_from_date = $this->request->post['filter_from_date'];
		} else {
			$filter_from_date = null;
		}
		
	
		
		if (!empty($this->request->post['filter_idstart'])) {
			$filter_idstart = $this->request->post['filter_idstart'];
		} else {
			$filter_idstart = 0;
		}
		
		if (!empty($this->request->post['filter_idend'])) {
			$filter_idend = $this->request->post['filter_idend'];
		} else {
			$filter_idend = '';
		}
		
		if (isset($this->request->post['filter_eformat'])) {
			$filter_eformat = $this->request->post['filter_eformat'];
		} else {
			$filter_eformat = null;
		}
		
		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}
		
		if (isset($this->request->post['filter_limit'])) {
			$filter_limit = $this->request->post['filter_limit'];
		} else {
			$filter_limit = 0;
		}
		
		$filter_data1 = array(
			'filter_name'              => $filter_name,
			'filter_email'             => $filter_email,
			'filter_customer_group_id' => $filter_customer_group_id,
			'filter_status'            => $filter_status,
			//'filter_approved'          => $filter_approved,
			'filter_to_date'       	   => $filter_to_date,
			'filter_from_date'         => $filter_from_date,
			'filter_idstart'   		   => $filter_idstart,
			'filter_idend'   		   => $filter_idend,
			'filter_eformat' 		   => $filter_eformat
		);
		
		
		$customer_total = $this->model_extension_excel_point->getTotalCustomers($filter_data1);
		
		if($filter_limit){
		   $filter_limit = round($customer_total/$filter_limit);
		}else{
			 $filter_limit = 0;
		}
		
		$filter_data = array(
			'filter_name'              => $filter_name,
			'filter_email'             => $filter_email,
			'filter_customer_group_id' => $filter_customer_group_id,
			'filter_status'            => $filter_status,
			//'filter_approved'          => $filter_approved,
			'filter_to_date'       	   => $filter_to_date,
			'filter_from_date'         => $filter_from_date,
			'filter_idstart'   		   => $filter_idstart,
			'filter_idend'   		   => $filter_idend,
			'start'                    => ($page - 1) * $filter_limit,
			'limit'           		   => $filter_limit,
			'filter_eformat' 		   => $filter_eformat
		);
			
		$results = $this->model_extension_excel_point->getCustomers($filter_data);
		foreach($results as $result){
			$address_info = $this->model_extension_excel_point->getAddress($result['address_id']);
			$customers[]=array(
				'customer_id'		=> $result['customer_id'],
				'firstname' 		=> $result['firstname'],
				'lastname' 			=> $result['lastname'],
				'email' 			=> $result['email'],
				'password' 			=> $result['password'],
				'salt' 				=> $result['salt'],
				'telephone' 		=> $result['telephone'],
				'fax' 				=> $result['fax'],
				'customer_group_id' => $result['customer_group_id'],
				'company'     		=> $address_info['company'],
				'address_1'   		=> $address_info['address_1'],
				'address_2'   		=> $address_info['address_2'],
				'postcode'    		=> $address_info['postcode'],
				'city'  	  		=> $address_info['city'],
				'zone_id'  	  		=> $address_info['zone_id'],
				'country_id'     	=> $address_info['country_id'],
				//'approved' 	 		=> $result['approved'],
				'newsletter'        => $result['newsletter'],
				'status' 			=> $result['status'],
			);
		}
		
		if($filter_eformat == 'xml'){
			$doc = new DOMDocument();
			$doc->formatOutput = true;
			$r = $doc->createElement("Customers");
			$doc->appendChild($r);
			
			foreach($customers as $customer){
				$Information = $doc->createElement("Customer");
				
				$CUSTOMERID = $doc->createElement("customer_id");
				$CUSTOMERID->appendChild($doc->createTextNode($customer['customer_id']));
				$Information->appendChild($CUSTOMERID);
				
				$FIRSTNAME = $doc->createElement("firstname");
				$FIRSTNAME->appendChild($doc->createTextNode($customer['firstname']));
				$Information->appendChild($FIRSTNAME);
				
				$LASTNAME = $doc->createElement("lastname");
				$LASTNAME->appendChild($doc->createTextNode($customer['lastname']));
				$Information->appendChild($LASTNAME);

				$EMAIL = $doc->createElement("email");
				$EMAIL->appendChild($doc->createTextNode($customer['email']));
				$Information->appendChild($EMAIL);
				
				$PASSWORD = $doc->createElement("password");
				$PASSWORD->appendChild($doc->createTextNode($customer['password']));
				$Information->appendChild($PASSWORD);
				
				$SALT = $doc->createElement("salt");
				$SALT->appendChild($doc->createTextNode($customer['salt']));
				$Information->appendChild($SALT);
				
				$TELEPHONE = $doc->createElement("telephone");
				$TELEPHONE->appendChild($doc->createTextNode($customer['telephone']));
				$Information->appendChild($TELEPHONE);
				
				$FAX = $doc->createElement("fax");
				$FAX->appendChild($doc->createTextNode($customer['fax']));
				$Information->appendChild($FAX);
				
				$CUSTOMER_GROUP_ID = $doc->createElement("customer_group_id");
				$CUSTOMER_GROUP_ID->appendChild($doc->createTextNode($customer['customer_group_id']));
				$Information->appendChild($CUSTOMER_GROUP_ID);
				
				$COMPANY = $doc->createElement("company");
				$COMPANY->appendChild($doc->createTextNode($customer['company']));
				$Information->appendChild($COMPANY);
				
				$ADDRESS_1 = $doc->createElement("address_1");
				$ADDRESS_1->appendChild($doc->createTextNode($customer['address_1']));
				$Information->appendChild($ADDRESS_1);
				
				$ADDRESS_2 = $doc->createElement("address_2");
				$ADDRESS_2->appendChild($doc->createTextNode($customer['address_2']));
				$Information->appendChild($ADDRESS_2);
				
				$POSTCODE = $doc->createElement("postcode");
				$POSTCODE->appendChild($doc->createTextNode($customer['postcode']));
				$Information->appendChild($POSTCODE);
				
				$CITY = $doc->createElement("city");
				$CITY->appendChild($doc->createTextNode($customer['city']));
				$Information->appendChild($CITY);
				
				$ZONE_ID = $doc->createElement("zone_id");
				$ZONE_ID->appendChild($doc->createTextNode($customer['zone_id']));
				$Information->appendChild($ZONE_ID);
				
				$COUNTRY_ID = $doc->createElement("country_id");
				$COUNTRY_ID->appendChild($doc->createTextNode($customer['country_id']));
				$Information->appendChild($COUNTRY_ID);
				
				/* $APPROVED = $doc->createElement("approved");
				$APPROVED->appendChild($doc->createTextNode($customer['approved']));
				$Information->appendChild($APPROVED); */
				
				$NEWSLETTER = $doc->createElement("newsletter");
				$NEWSLETTER->appendChild($doc->createTextNode($customer['newsletter']));
				$Information->appendChild($NEWSLETTER);
				
				$STATUS = $doc->createElement("status");
				$STATUS->appendChild($doc->createTextNode($customer['status']));
				$Information->appendChild($STATUS);
				
				$Rewardsx = $doc->createElement("Rewards");
				$rewards = $this->model_extension_excel_point->getRewards($customer['customer_id']);
				if($rewards){
					foreach($rewards as $reward){
					  $Rewardx = $doc->createElement("Reward");
					  $rcustomer_id = $doc->createElement("customer_id");
					  $rcustomer_id->appendChild($doc->createTextNode($reward['customer_id']));
					  $Rewardx->appendChild($rcustomer_id);
					  
					  $order_id = $doc->createElement("order_id");
					  $order_id->appendChild($doc->createTextNode($reward['order_id']));
					  $Rewardx->appendChild($order_id);

					  $description = $doc->createElement("description");
					  $description->appendChild($doc->createTextNode($reward['description']));
					  $Rewardx->appendChild($description);
					  
					  $points = $doc->createElement("points");
					  $points->appendChild($doc->createTextNode($reward['points']));
					  $Rewardx->appendChild($points);
					  
					  $date_added = $doc->createElement("date_added");
					  $date_added->appendChild($doc->createTextNode($reward['date_added']));
					  $Rewardx->appendChild($date_added);
					  $Rewardsx->appendChild($Rewardx);
					}
				}
				$Information->appendChild($Rewardsx);
				
				$Transactionsx = $doc->createElement("Transactions");
				$transactions = $this->model_extension_excel_point->getTransactions($customer['customer_id']);
				foreach($transactions as $transaction){
					  $Transactionx = $doc->createElement("Transaction");
					  $rcustomer_id = $doc->createElement("customer_id");
					  $rcustomer_id->appendChild($doc->createTextNode($transaction['customer_id']));
					  $Transactionx->appendChild($rcustomer_id);
					  
					  $order_id = $doc->createElement("order_id");
					  $order_id->appendChild($doc->createTextNode($transaction['order_id']));
					  $Transactionx->appendChild($order_id);

					  $description = $doc->createElement("description");
					  $description->appendChild($doc->createTextNode($transaction['description']));
					  $Transactionx->appendChild($description);
					  
					  $amount = $doc->createElement("amount");
					  $amount->appendChild($doc->createTextNode($transaction['amount']));
					  $Transactionx->appendChild($amount);
					  
					  $date_added = $doc->createElement("date_added");
					  $date_added->appendChild($doc->createTextNode($transaction['date_added']));
					  $Transactionx->appendChild($date_added);
					  $Transactionsx->appendChild($Transactionx);
				}
				$Information->appendChild($Transactionsx);
				
				
				$r->appendChild($Information);
			}
			$mask = time();
			$doc->saveXML();
			$doc->save(DIR_EXCEL."customer".$mask.".xml");
		}else{
			$objPHPExcel = new PHPExcel();
			$objPHPExcel->setActiveSheetIndex(0);  
			$objPHPExcel->getActiveSheet()->setTitle("Customers");
			$i=1;
			$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, 'Customer ID')->getColumnDimension('A')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, 'First Name')->getColumnDimension('B')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, 'Last Name')->getColumnDimension('C')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, 'E-Mail')->getColumnDimension('D')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, 'Password')->getColumnDimension('E')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, 'Salt')->getColumnDimension('F')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, 'TelePhone')->getColumnDimension('G')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, 'Fax')->getColumnDimension('H')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, 'Customer Group ID')->getColumnDimension('I')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, 'Company')->getColumnDimension('J')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, 'Address 1')->getColumnDimension('K')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, 'Address 2')->getColumnDimension('L')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, 'Postal Code')->getColumnDimension('M')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, 'City')->getColumnDimension('N')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, 'Zone ID')->getColumnDimension('O')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('P'.$i, 'Country ID')->getColumnDimension('P')->setAutoSize(true);
			//$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, 'Approved')->getColumnDimension('Q')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, 'Newsletter')->getColumnDimension('R')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('R'.$i, 'Status')->getColumnDimension('S')->setAutoSize(true);
			
			$rw=1;
			$rewardWorkSheet = $objPHPExcel->createSheet(2);
			$rewardWorkSheet->setTitle("Rewards");
			$rewardWorkSheet->setCellValue('A'.$rw, 'Customer ID')->getColumnDimension('A')->setAutoSize(true);
			$rewardWorkSheet->setCellValue('B'.$rw, 'Order ID')->getColumnDimension('B')->setAutoSize(true);
			$rewardWorkSheet->setCellValue('C'.$rw, 'Description')->getColumnDimension('C')->setAutoSize(true);
			$rewardWorkSheet->setCellValue('D'.$rw, 'Points')->getColumnDimension('D')->setAutoSize(true);
			$rewardWorkSheet->setCellValue('E'.$rw, 'Date Added')->getColumnDimension('E')->setAutoSize(true);
			
			$transac=1;
			$transactionWorkSheet = $objPHPExcel->createSheet(3);
			$transactionWorkSheet->setTitle("Transactions");
			$transactionWorkSheet->setCellValue('A'.$transac, 'Customer ID')->getColumnDimension('A')->setAutoSize(true);
			$transactionWorkSheet->setCellValue('B'.$transac, 'Order ID')->getColumnDimension('B')->setAutoSize(true);
			$transactionWorkSheet->setCellValue('C'.$transac, 'Description')->getColumnDimension('C')->setAutoSize(true);
			$transactionWorkSheet->setCellValue('D'.$transac, 'Amount')->getColumnDimension('D')->setAutoSize(true);
			$transactionWorkSheet->setCellValue('E'.$transac, 'Date Added')->getColumnDimension('E')->setAutoSize(true);
			
			foreach($customers as $customer){
				$i++;
				$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $customer['customer_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $customer['firstname']);
				$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $customer['lastname']);
				$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $customer['email']);
				$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $customer['password']);
				$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, $customer['salt']);
				$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, $customer['telephone']);
				$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, $customer['fax']);
				$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, $customer['customer_group_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, $customer['company']);
				$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, $customer['address_1']);
				$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, $customer['address_2']);
				$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, $customer['postcode']);
				$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, $customer['city']);
				$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, $customer['zone_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('P'.$i, $customer['country_id']);
				//$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, $customer['approved']);
				$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, $customer['newsletter']);
				$objPHPExcel->getActiveSheet()->setCellValue('R'.$i, $customer['status']);
				 
				$rewards = $this->model_extension_excel_point->getRewards($customer['customer_id']);
				foreach($rewards as $reward){
				  $rw++;
				  $rewardWorkSheet->setCellValue('A'.$rw, $reward['customer_id']);
				  $rewardWorkSheet->setCellValue('B'.$rw, $reward['order_id']);
				  $rewardWorkSheet->setCellValue('C'.$rw, $reward['description']);
				  $rewardWorkSheet->setCellValue('D'.$rw, $reward['points']);
				  $rewardWorkSheet->setCellValue('E'.$rw, $reward['date_added']);
				}
				$transactions = $this->model_extension_excel_point->getTransactions($customer['customer_id']);
				foreach($transactions as $transaction){
				  $transac++;
				  $transactionWorkSheet->setCellValue('A'.$transac, $transaction['customer_id']);
				  $transactionWorkSheet->setCellValue('B'.$transac, $transaction['order_id']);
				  $transactionWorkSheet->setCellValue('C'.$transac, $transaction['description']);
				  $transactionWorkSheet->setCellValue('D'.$transac, $transaction['amount']);
				  $transactionWorkSheet->setCellValue('E'.$transac, $transaction['date_added']);
				}
			}
			
			$mask = time();
			if($filter_eformat == 'xls'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
				$filename = 'customer'.$mask.'.xls';
			}elseif($filter_eformat == 'xlsx'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
				$filename = 'customer'.$mask.'.xlsx';
			}
			$objWriter->save(DIR_EXCEL . $filename);
		}
		
		if($filter_limit){
			$start = ($page - 1) * $filter_limit;
			$end = $start + $filter_limit;
			if($end < $customer_total){
				$json['next'] = str_replace('&amp;', '&', $this->url->link('extension/excel_export/exportCustomer', 'user_token=' . $this->session->data['user_token'] . '&page=' . ($page + 1), true));
			} else {
				$json['next'] = '';
				$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filter_eformat='.$filter_eformat.'&filename=customer&mask='.$mask.'&batches='.true);
			}
		}else{
			$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filename=customer&filter_eformat='.$filter_eformat.'&mask='.$mask.'&batches='.false);
		}
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function exportManufacture(){
		$this->load->model('extension/excel_point');
		$this->load->model('setting/store');
		
		if (isset($this->request->post['filter_status'])) {
			$filter_status = $this->request->post['filter_status'];
		} else {
			$filter_status = null;
		}
		
		if(!empty($this->request->post['filter_language_id'])){
			$language_id = $this->request->post['filter_language_id'];
		}else{
			$language_id = $this->config->get('config_language_id');
		}
		
		if (isset($this->request->post['filter_store'])) {
			$filter_store = $this->request->post['filter_store'];
		} else {
			$filter_store = null;
		}
		
		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}
		
		if (!empty($this->request->post['filter_limit'])) {
			$filter_limit = $this->request->post['filter_limit'];
		} else {
			$filter_limit = 0;
		}
		
		
		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);
		$objPHPExcel->getActiveSheet()->setTitle("Manufacturer");
		$i=1;
		$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, 'Manufacturer ID')->getColumnDimension('A')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, 'Stores')->getColumnDimension('C')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, 'Manufacturer Name')->getColumnDimension('D')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, 'SEO Keyword')->getColumnDimension('E')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, 'Image')->getColumnDimension('F')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, 'Sort Order')->getColumnDimension('G')->setAutoSize(true);
		
		
		$filter_data1=array(
			'filter_status'   		=> $filter_status,
		    'filter_store'			=> $filter_store,
		);
		
		$manufacturer_total = $this->model_extension_excel_point->getTotalManufacturerdata($filter_data1);
		
		if($filter_limit){
		   $filter_limit = round($manufacturer_total/$filter_limit);
		}
		
		$filter_data=array(
			'filter_status'   		=> $filter_status,
		    'filter_store'			=> $filter_store,
		    'start'                 => ($page - 1) * $filter_limit,
			'limit'           		=> $filter_limit,
		);
		
		$results = $this->model_extension_excel_point->getManufacturerdata($filter_data);
		
		foreach($results as $result){
			$i++;
			$keyword = $this->model_extension_excel_point->getManufacturerKeyword($result['manufacturer_id'],$language_id,$filter_store);
			$storeinfo = $this->model_setting_store->getStore($result['store_id']);
			if($storeinfo){
				$store = $storeinfo['name'];
			}else{
				$store = 'default';
			}
			$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $result['manufacturer_id'])->getColumnDimension('A')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $store)->getColumnDimension('C')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $result['name'])->getColumnDimension('D')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $keyword)->getColumnDimension('E')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $result['image'])->getColumnDimension('F')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, $result['sort_order'])->getColumnDimension('G')->setAutoSize(true);
		}
		$filter_eformat = 'xls';
		
		if($filter_eformat == 'xls'){
			$mask = time();
			$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
			$filename = 'manufacturer'.$mask.'.xls';
		}
			
		$objWriter->save(DIR_EXCEL . $filename);
		
		if($filter_limit){
			$start = ($page - 1) * $filter_limit;
			$end = $start + $filter_limit;
			if($end < $manufacturer_total){
				$json['next'] = str_replace('&amp;', '&', $this->url->link('extension/excel_export/exportManufacture', 'user_token=' . $this->session->data['user_token'] . '&page=' . ($page + 1), true));
			} else {
				$json['next'] = '';
				$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filter_eformat='.$filter_eformat.'&filename=manufacturer&mask='.$mask.'&batches='.true);
			}
		}else{
			$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filename=manufacturer&filter_eformat='.$filter_eformat.'&mask='.$mask.'&batches='.false);
		}
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	
	///categories start
	public function exportCategories(){
		$this->load->model('extension/excel_point');
		$this->load->model('setting/store');
		
		if(!empty($this->request->post['filter_language_id'])){
			$language_id = $this->request->post['filter_language_id'];
		}else{
			$language_id = $this->config->get('config_language_id');
		}
		
		if (isset($this->request->post['filter_status'])) {
			$filter_status = $this->request->post['filter_status'];
		} else {
			$filter_status = null;
		}
		
		if (isset($this->request->post['filter_categories'])) {
			$filter_categories = $this->request->post['filter_categories'];
		} else {
			$filter_categories = null;
		}
		
		
		if (isset($this->request->post['filter_store'])) {
			$filter_store = $this->request->post['filter_store'];
		} else {
			$filter_store = null;
		}
		
		if(!empty($this->request->post['filter_pimage'])){
			$filter_pimage = $this->request->post['filter_pimage'];
		}else{
			$filter_pimage = 0;
		}
		
		if(!empty($this->request->post['filter_eformat'])){
			$filter_eformat = $this->request->post['filter_eformat'];
		}else{
			$filter_eformat = 'xls';
		}
		
		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}
		
		if (!empty($this->request->post['filter_limit'])) {
			$filter_limit = $this->request->post['filter_limit'];
		} else {
			$filter_limit = 0;
		}
		
		
		$filter_data1=array(
			'filter_status'   		=> $filter_status,
		    'filter_language_id'	=> $language_id,
		    'filter_store'			=> $filter_store,
		    'filter_categories'		=> $filter_categories,
		);
		
		$categories_total = $this->model_extension_excel_point->getTotalCategories($filter_data1);
		
		if($filter_limit){
			 $filter_limit =round($categories_total/$filter_limit);
		}
		
		$filter_data=array(
			'filter_status'   		=> $filter_status,
		    'filter_language_id'	=> $language_id,
		    'filter_store'			=> $filter_store,
		    'filter_categories'		=> $filter_categories,
		    'start'                	=> ($page - 1) * $filter_limit,
		    'limit'           		=> $filter_limit,
		);
		
		$results = $this->model_extension_excel_point->getCategories($filter_data);
		if($filter_eformat == 'xml'){
			$doc = new DOMDocument();
			$doc->formatOutput = true;
			$r = $doc->createElement("CATEGORIES");
			$doc->appendChild( $r );
			$i=0;
			foreach($results as $result){
				$i++;
				
				$keyword = $this->model_extension_excel_point->getCategoriesKeyword($result['category_id'],$language_id,$filter_store);
				
				$CATEGORY = $doc->createElement("CATEGORY");
				$CATEGORY_ID = $doc->createElement("category_id");
				$CATEGORY_ID->appendChild($doc->createTextNode($result['category_id']));
				$CATEGORY->appendChild($CATEGORY_ID);
				
				$language = $doc->createElement("Language");
				$language->appendChild($doc->createTextNode($result['language']));
				$CATEGORY->appendChild($language);
				
				$storeinfo = $this->model_setting_store->getStore($result['store_id']);
				if($storeinfo){
					$store = $storeinfo['name'];
				}else{
					$store = 'default';
				}
				
				$c_store = $doc->createElement("store");
				$c_store->appendChild($doc->createTextNode($store));
				$CATEGORY->appendChild($c_store);
				
				$CATEGORY_NAME = $doc->createElement("category_name");
				$CATEGORY_NAME->appendChild($doc->createTextNode($result['name']));
				$CATEGORY->appendChild($CATEGORY_NAME);
				
				$CATEGORY_description = $doc->createElement("description");
				$CATEGORY_description->appendChild($doc->createTextNode($result['description']));
				$CATEGORY->appendChild($CATEGORY_description);
				
				$CATEGORY_meta_title = $doc->createElement("meta_title");
				$CATEGORY_meta_title->appendChild($doc->createTextNode($result['meta_title']));
				$CATEGORY->appendChild($CATEGORY_meta_title);
				
				$meta_description = $doc->createElement("meta_description");
				$meta_description->appendChild($doc->createTextNode($result['meta_description']));
				$CATEGORY->appendChild($meta_description);
				
				$CATEGORY_meta_keyword = $doc->createElement("meta_keyword");
				$CATEGORY_meta_keyword->appendChild($doc->createTextNode($result['meta_keyword']));
				$CATEGORY->appendChild($CATEGORY_meta_keyword);
				
				$CATEGORY_parent_id = $doc->createElement("parent_id");
				$CATEGORY_parent_id->appendChild($doc->createTextNode($result['parent_id']));
				$CATEGORY->appendChild($CATEGORY_parent_id);
				
				$CATEGORY_keyword = $doc->createElement("keyword");
				$CATEGORY_keyword->appendChild($doc->createTextNode($keyword));
				$CATEGORY->appendChild($CATEGORY_keyword);
				
				
				if($filter_pimage == 'yes'){
					if($result['image'] != '' || file_exists(HTTP_CATALOG.'image/'.$result['image'])){
						$main_image = $doc->createElement("Image");
						$main_image->appendChild($doc->createTextNode(HTTP_CATALOG.'image/'.$result['image']));
						$CATEGORY->appendChild($main_image);
					}
					else{
						$main_image = $doc->createElement("Image");
						$main_image->appendChild($doc->createTextNode(''));
						$CATEGORY->appendChild($main_image);
					}
				}else{
					$main_image = $doc->createElement("Image");
					$main_image->appendChild($doc->createTextNode($result['image']));
					$CATEGORY->appendChild($main_image);
				}
				
				$CATEGORY_top = $doc->createElement("Top");
				$CATEGORY_top->appendChild($doc->createTextNode($result['top']));
				$CATEGORY->appendChild($CATEGORY_top);
				
				$CATEGORY_column = $doc->createElement("Column");
				$CATEGORY_column->appendChild($doc->createTextNode($result['column']));
				$CATEGORY->appendChild($CATEGORY_column);
				
				$category_sort_order = $doc->createElement("Sort_Order");
				$category_sort_order->appendChild($doc->createTextNode($result['sort_order']));
				$CATEGORY->appendChild($category_sort_order);
				
				$category_status = $doc->createElement("Status");
				$category_status->appendChild($doc->createTextNode($result['status']));
				$CATEGORY->appendChild($category_status);
				
				$r->appendChild($CATEGORY);
			}
			$mask = time();
			$doc->saveXML();
			$doc->save(DIR_EXCEL."categories".$mask.".xml");
		}else{
			$objPHPExcel = new PHPExcel();
			$objPHPExcel->setActiveSheetIndex(0);
			$objPHPExcel->getActiveSheet()->setTitle("Category");
			$objPHPExcel->getActiveSheet()->getStyle('S')->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_GENERAL);
			$objPHPExcel->getActiveSheet()
			->getStyle('A1:BB1')
			->applyFromArray(
				array(
					'fill' => array(
						'type' => PHPExcel_Style_Fill::FILL_SOLID,
						'color' => array('rgb' => 'd9d9d9')
					)
				)
			);
			$i=1;
			$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, 'Category ID')->getColumnDimension('A')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, 'Language')->getColumnDimension('B')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, 'Store')->getColumnDimension('C')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, 'Name')->getColumnDimension('D')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, 'Description')->getColumnDimension('E')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, 'Meta Title')->getColumnDimension('F')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, 'Meta Description')->getColumnDimension('G')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, 'Meta Keyword')->getColumnDimension('H')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, 'Parent ID')->getColumnDimension('I')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, 'SEO Keyword')->getColumnDimension('J')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, 'Image')->getColumnDimension('K')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, 'Top')->getColumnDimension('L')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, 'Columns')->getColumnDimension('M')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, 'Sort Order')->getColumnDimension('N')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, 'Status')->getColumnDimension('O')->setAutoSize(true);
			
			foreach($results as $result){
				$i++;
				$storeinfo = $this->model_setting_store->getStore($result['store_id']);
				if($storeinfo){
					$store = $storeinfo['name'];
				}else{
					$store = 'default';
				}
				
				$keyword = $this->model_extension_excel_point->getCategoriesKeyword($result['category_id'],$language_id,$filter_store);
				
				$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $result['category_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $result['language']);
				$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $store);
				$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $result['name']);
				$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $result['description']);
				$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, $result['meta_title']);
				$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, $result['meta_description']);
				$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, $result['meta_keyword']);
				$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, $result['parent_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, $keyword);
				
				$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, $result['image']);
				
				if($filter_pimage == 'yes'){
				   if($result['image'] != '' || file_exists(HTTP_CATALOG.'image/'.$result['image'])){
						$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, HTTP_CATALOG.'image/'.$result['image']);
					}else{
						$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, '');
					}
				}else{
					$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, $result['image']);
				}
				
				$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, $result['top']);
				$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, $result['column']);
				$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, $result['sort_order']);
				$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, $result['status']);
			}
			$mask = time();
			if($filter_eformat == 'csv'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'CSV');
				$filename = 'categories'.$mask.'.csv';
			}elseif($filter_eformat == 'xls'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
				$filename = 'categories'.$mask.'.xls';
			}elseif($filter_eformat == 'xlsx'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
				$filename = 'categories'.$mask.'.xlsx';
			}
			
			$objWriter->save(DIR_EXCEL . $filename);
		}
		
		
		if($filter_limit){
		    $start = ($page - 1) * $filter_limit;
			$end = $start + $filter_limit;
			
			if($end < $categories_total){
				$json['next'] = str_replace('&amp;', '&', $this->url->link('extension/excel_export/exportCategories', 'user_token=' . $this->session->data['user_token'] . '&page=' . ($page + 1), true));
			} else {
				$json['next'] = '';
				$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filter_eformat='.$filter_eformat.'&mask='.$mask.'&filename=categories&batches='.true);
			}
		}else{
			$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filter_eformat='.$filter_eformat.'&mask='.$mask.'&filename=categories&batches='.false);
		}
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	//categories End
	
	public function exportproductsoptions(){
		
		$this->load->model('extension/excel_point');
		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);
		$i=1;
		$objPHPExcel->getActiveSheet()->setCellValue('A'.$i,'Option ID')->getColumnDimension('A')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('B'.$i,'Option type')->getColumnDimension('B')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('C'.$i,'Language ID')->getColumnDimension('C')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('D'.$i,'Option Name')->getColumnDimension('D')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('E'.$i,'Sort Order')->getColumnDimension('E')->setAutoSize(true);
		
		if(isset($this->request->get['filter_start'])){
			$filter_start = $this->request->get['filter_start'];
		} else {
			$filter_start = 0;
		}

		if (isset($this->request->get['filter_limit'])) {
			$filter_limit = $this->request->get['filter_limit'];
		} else {
			$filter_limit = 0;
		}

		if (isset($this->request->get['filter_language_id'])) {
			$filter_language_id = $this->request->get['filter_language_id'];
		} else {
			$filter_language_id = '';
		}

		$filter_data = array(
		  'start' 			   => $filter_start,
		  'limit' 			   => $filter_limit,
		  'filter_language_id' => $filter_language_id
		);
		
		$results = $this->model_extension_excel_point->getProductOption($filter_data);
		foreach($results as $result){
			$i++;
			$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $result['option_id'])->getColumnDimension('A')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $result['type'])->getColumnDimension('B')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $result['language_id'])->getColumnDimension('C')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $result['name'])->getColumnDimension('D')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $result['sort_order'])->getColumnDimension('E')->setAutoSize(true);
		}
		
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5'); 
		
		$filname ="productsoptions-".time().'.xls';
		header('Content-Type: application/vnd.ms-excel'); 
		header('Content-Disposition: attachment;filename='.$filname);
		header('Cache-Control: max-age=0'); 
		$objWriter->save('php://output'); 
		exit();
		
		
	}
	
	public function exportproducts(){
		if(!empty($this->request->post['filter_language_id'])){
			$language_id = $this->request->post['filter_language_id'];
		}else{
			$language_id = $this->config->post('config_language_id');
		}
		
		if (isset($this->request->post['filter_name'])) {
			$filter_name = $this->request->post['filter_name'];
		} else {
			$filter_name = null;
		}

		if (isset($this->request->post['filter_model'])) {
			$filter_model = $this->request->post['filter_model'];
		} else {
			$filter_model = null;
		}

		if (isset($this->request->post['filter_price_to'])) {
			$filter_price_to = $this->request->post['filter_price_to'];
		} else {
			$filter_price_to = null;
		}
		
		if (isset($this->request->post['filter_price_form'])) {
			$filter_price_form = $this->request->post['filter_price_form'];
		} else {
			$filter_price_form = null;
		}

		if (isset($this->request->post['filter_quantity_to'])) {
			$filter_quantity_to = $this->request->post['filter_quantity_to'];
		} else {
			$filter_quantity_to = null;
		}
		
		if (isset($this->request->post['filter_quantity_form'])) {
			$filter_quantity_form = $this->request->post['filter_quantity_form'];
		} else {
			$filter_quantity_form = null;
		}

		if (isset($this->request->post['filter_status'])) {
			$filter_status = $this->request->post['filter_status'];
		} else {
			$filter_status = null;
		}
		
		if (isset($this->request->post['filter_store'])) {
			$filter_store = $this->request->post['filter_store'];
		} else {
			$filter_store = null;
		}
		
		if (isset($this->request->post['filter_stock_status'])) {
			$filter_stock_status = $this->request->post['filter_stock_status'];
		} else {
			$filter_stock_status = null;
		}
		
		if (isset($this->request->post['filter_categories'])) {
			$filter_categories = $this->request->post['filter_categories'];
		} else {
			$filter_categories = null;
		}
		
		if (isset($this->request->post['filter_manufacturer'])) {
			$filter_manufacturer = $this->request->post['filter_manufacturer'];
		} else {
			$filter_manufacturer = null;
		}
		
		if (!empty($this->request->post['filter_idstart'])) {
			$filter_idstart = $this->request->post['filter_idstart'];
		} else {
			$filter_idstart = 0;
		}
		
		if (!empty($this->request->post['filter_idend'])) {
			$filter_idend = $this->request->post['filter_idend'];
		} else {
			$filter_idend = '';
		}
		
		if (isset($this->request->post['filter_pimage'])){
			$filter_pimage = $this->request->post['filter_pimage'];
		} else {
			$filter_pimage = null;
		}
		
		if (isset($this->request->post['filter_eformat'])){
			$filter_eformat = $this->request->post['filter_eformat'];
		} else {
			$filter_eformat = 'xls';
		}
		
		if (isset($this->request->get['page'])) {
			$page = $this->request->get['page'];
		} else {
			$page = 1;
		}
		
		if (!empty($this->request->post['filter_limit'])) {
			$filter_limit = $this->request->post['filter_limit'];
		} else {
			$filter_limit = 0;
		}
		
		
		$this->load->model('catalog/product');
		$this->load->model('extension/excel_point');
		$this->load->model('setting/store');
		
		$filter_data1=array(
			'filter_name'	  				=> $filter_name,
			'filter_model'	  			=> $filter_model,
			'filter_price_to'	  		=> $filter_price_to,
			'filter_price_form'	  	=> $filter_price_form,
			'filter_quantity_to' 		=> $filter_quantity_to,
			'filter_quantity_form' 	=> $filter_quantity_form,
			'filter_status'   			=> $filter_status,
			'filter_language_id'		=> $language_id,
			'filter_store'					=> $filter_store,
			'filter_categories'			=> $filter_categories,
			'filter_manufacturer'		=> $filter_manufacturer,
			'filter_stock_status'   => $filter_stock_status,
			'filter_idstart'   			=> $filter_idstart,
			'filter_idend'   				=> $filter_idend,
			'filter_eformat'   			=> $filter_eformat,
			'filter_pimage'   			=> $filter_pimage,
		);
		
		$product_total = $this->model_extension_excel_point->getTotalProducts($filter_data1);
		
		if($filter_limit){
		   $filter_limit = $product_total/$filter_limit;
		}
		
		$filter_data=array(
			'filter_name'	  			=> $filter_name,
			'filter_model'	  			=> $filter_model,
			'filter_price_to'	  		=> $filter_price_to,
			'filter_price_form'	  		=> $filter_price_form,
			'filter_quantity_to' 		=> $filter_quantity_to,
			'filter_quantity_form' 		=> $filter_quantity_form,
			'filter_status'   			=> $filter_status,
			'filter_language_id'		=> $language_id,
			'filter_store'				=> $filter_store,
			'filter_categories'			=> $filter_categories,
			'filter_manufacturer'		=> $filter_manufacturer,
			'start'                 	=> ($page - 1) * $filter_limit,
			'limit'           			=> $filter_limit,
			'filter_stock_status'   	=> $filter_stock_status,
			'filter_idstart'   			=> $filter_idstart,
			'filter_idend'   			=> $filter_idend,
			'filter_eformat'   			=> $filter_eformat,
			'filter_pimage'   			=> $filter_pimage,
		);
		
		
		$products = $this->model_extension_excel_point->getProducts($filter_data);
		if($filter_eformat == 'xml'){
			$doc = new DOMDocument();
			$doc->formatOutput = true;
			$r = $doc->createElement("PRODUCTS");
			$doc->appendChild( $r );
			$i=0;
			foreach($products as $product){
				$storeinfo = $this->model_setting_store->getStore($product['store_id']);
				if($storeinfo){
					$store = $storeinfo['name'];
				}else{
					$store = 'default';
				}
				$i++;
				$PRODUCT = $doc->createElement("Product");
				
				$PRODUCTID = $doc->createElement("product_id");
				$PRODUCTID->appendChild($doc->createTextNode($product['product_id']));
				$PRODUCT->appendChild($PRODUCTID);
				
				$language = $doc->createElement("Language");
				$language->appendChild($doc->createTextNode($product['language']));
				$PRODUCT->appendChild($language);
				
				$p_store = $doc->createElement("Store");
				$p_store->appendChild($doc->createTextNode($store));
				$PRODUCT->appendChild($p_store);
				
				$name = $doc->createElement("Name");
				$name->appendChild($doc->createTextNode(html_entity_decode($product['name'])));
				$PRODUCT->appendChild($name);
				
				$model = $doc->createElement("Model");
				$model->appendChild($doc->createTextNode($product['model']));
				$PRODUCT->appendChild($model);
				
				$p_description = $doc->createElement("Description");
				$p_description->appendChild($doc->createTextNode(html_entity_decode($product['description'])));
				$PRODUCT->appendChild($p_description);
				
				$meta_title = $doc->createElement("MetaTitle");
				$meta_title->appendChild($doc->createTextNode($product['meta_title']));
				$PRODUCT->appendChild($meta_title);
				
				$meta_description = $doc->createElement("MetaDescription");
				$meta_description->appendChild($doc->createTextNode($product['meta_description']));
				$PRODUCT->appendChild($meta_description);
				
				$meta_keyword = $doc->createElement("MetaKeyword");
				$meta_keyword->appendChild($doc->createTextNode($product['meta_keyword']));
				$PRODUCT->appendChild($meta_keyword);
				
				$tag = $doc->createElement("Tag");
				$tag->appendChild($doc->createTextNode($product['tag']));
				$PRODUCT->appendChild($tag);
				
				if($filter_pimage == 'yes'){
					if($product['image'] != '' || file_exists(HTTP_CATALOG.'image/'.$product['image'])){
						$main_image = $doc->createElement("Image");
						$main_image->appendChild($doc->createTextNode(HTTP_CATALOG.'image/'.$product['image']));
						$PRODUCT->appendChild($main_image);
					}
					else{
						$main_image = $doc->createElement("Image");
						$main_image->appendChild($doc->createTextNode(''));
						$PRODUCT->appendChild($main_image);
					}
				}else{
					$main_image = $doc->createElement("Image");
					$main_image->appendChild($doc->createTextNode($product['image']));
					$PRODUCT->appendChild($main_image);
				}
				
				$sku = $doc->createElement("SKU");
				$sku->appendChild($doc->createTextNode($product['sku']));
				$PRODUCT->appendChild($sku);
				
				$upc = $doc->createElement("UPC");
				$upc->appendChild($doc->createTextNode($product['upc']));
				$PRODUCT->appendChild($upc);
				
				$ean = $doc->createElement("EAN");
				$ean->appendChild($doc->createTextNode($product['ean']));
				$PRODUCT->appendChild($ean);
				
				$jan = $doc->createElement("JAN");
				$jan->appendChild($doc->createTextNode($product['jan']));
				$PRODUCT->appendChild($jan);
				
				$isbn = $doc->createElement("ISBN");
				$isbn->appendChild($doc->createTextNode($product['isbn']));
				$PRODUCT->appendChild($isbn);
				
				$mpn = $doc->createElement("MPN");
				$mpn->appendChild($doc->createTextNode($product['mpn']));
				$PRODUCT->appendChild($mpn);
				
				$location = $doc->createElement("Location");
				$location->appendChild($doc->createTextNode($product['location']));
				$PRODUCT->appendChild($location);
				
				$price = $doc->createElement("Price");
				$price->appendChild($doc->createTextNode($product['price']));
				$PRODUCT->appendChild($price);
				
				$tax_class_id = $doc->createElement("Tax_Class_ID");
				$tax_class_id->appendChild($doc->createTextNode($product['tax_class_id']));
				$PRODUCT->appendChild($tax_class_id);
				
				$quantity = $doc->createElement("Quantity");
				$quantity->appendChild($doc->createTextNode($product['quantity']));
				$PRODUCT->appendChild($quantity);
				
				$minimum = $doc->createElement("Minimum");
				$minimum->appendChild($doc->createTextNode($product['minimum']));
				$PRODUCT->appendChild($minimum);
				
				$subtract = $doc->createElement("Subtract");
				$subtract->appendChild($doc->createTextNode($product['subtract']));
				$PRODUCT->appendChild($subtract);
				
				$stock_status_id = $doc->createElement("Stock_Status_ID");
				$stock_status_id->appendChild($doc->createTextNode($product['stock_status_id']));
				$PRODUCT->appendChild($stock_status_id);
				
				$shipping = $doc->createElement("Shipping");
				$shipping->appendChild($doc->createTextNode($product['shipping']));
				$PRODUCT->appendChild($shipping);
				
				$SEO = $doc->createElement("SEO");
				$SEO->appendChild($doc->createTextNode($this->model_extension_excel_point->getKeyword($product['product_id'],$language_id,$filter_store)));
				$PRODUCT->appendChild($SEO);
				
				$date_available = $doc->createElement("Date_Available");
				$date_available->appendChild($doc->createTextNode($product['date_available']));
				$PRODUCT->appendChild($date_available);
				
				$length = $doc->createElement("length");
				$length->appendChild($doc->createTextNode($product['length']));
				$PRODUCT->appendChild($length);
				
				$length_class_id = $doc->createElement("Length_Class_ID");
				$length_class_id->appendChild($doc->createTextNode($product['length_class_id']));
				$PRODUCT->appendChild($length_class_id);
				
				$width = $doc->createElement("Width");
				$width->appendChild($doc->createTextNode($product['width']));
				$PRODUCT->appendChild($width);
				
				$height = $doc->createElement("Height");
				$height->appendChild($doc->createTextNode($product['height']));
				$PRODUCT->appendChild($height);
				
				$weight = $doc->createElement("Weight");
				$weight->appendChild($doc->createTextNode($product['weight']));
				$PRODUCT->appendChild($weight);
				
				$weight_class_id = $doc->createElement("Weight_Class_ID");
				$weight_class_id->appendChild($doc->createTextNode($product['weight_class_id']));
				$PRODUCT->appendChild($weight_class_id);
				
				$status = $doc->createElement("Status");
				$status->appendChild($doc->createTextNode($product['status']));
				$PRODUCT->appendChild($status);
				
				$sort_order = $doc->createElement("Sort_Order");
				$sort_order->appendChild($doc->createTextNode($product['sort_order']));
				$PRODUCT->appendChild($sort_order);
				
				$manufacturer_id = $doc->createElement("Manufacturer_ID");
				$manufacturer_id->appendChild($doc->createTextNode($product['manufacturer_id']));
				$PRODUCT->appendChild($manufacturer_id);
				
				
				$categories = $this->model_catalog_product->getProductCategories($product['product_id']);
				
				$categories_ids = $doc->createElement("Categories_IDs");
				$categories_ids->appendChild($doc->createTextNode(!empty($categories) ? implode(',',$categories) : ''));
				$PRODUCT->appendChild($categories_ids);
				
				// Filter Data
				$filterdata=array();
				$filteres = $this->model_catalog_product->getProductFilters($product['product_id']);
				foreach($filteres as $filter_id){
					$filter_info = $this->model_catalog_filter->getFilter($filter_id);
					if($filter_info){
						$filterdata[] = html_entity_decode(($filter_info['group'] ? $filter_info['group']. ' :: ' . $filter_info['name'] : $filter_info['name']));
					}
				}
				$pfilter = $doc->createElement("Filter_Data");
				$pfilter->appendChild($doc->createTextNode(implode(', ',$filterdata)));
				$PRODUCT->appendChild($pfilter);
				
				$downloads = $this->model_catalog_product->getProductDownloads($product['product_id']);
				$pdownloads = $doc->createElement("Downloads");
				$pdownloads->appendChild($doc->createTextNode(implode(', ',$downloads)));
				$PRODUCT->appendChild($pdownloads);
				
				$realated = $this->model_catalog_product->getProductRelated($product['product_id']);
				$prealated = $doc->createElement("Related");
				$prealated->appendChild($doc->createTextNode(implode(', ',$realated)));
				$PRODUCT->appendChild($prealated);
				
				//GetAttribute
				$this->load->model('catalog/attribute');
				$this->load->model('catalog/attribute_group');
				$attributes = $this->model_extension_excel_point->getProductAttributes($product['product_id'],$language_id);
				$pattributes = $doc->createElement("Attribute");
				$pattributes->appendChild($doc->createTextNode(implode(', ',$attributes)));
				$PRODUCT->appendChild($pattributes);
				
				//options
				$productoptions = $this->model_extension_excel_point->getProductOptions($product['product_id'],$language_id);
				$pproductoptions = $doc->createElement("Options");
				$pproductoptions->appendChild($doc->createTextNode(implode(';', $productoptions)));
				$PRODUCT->appendChild($pproductoptions);
				
				//options required
				$productoptionrequired = $this->model_extension_excel_point->getProductOptionsrequired($product['product_id'],$language_id);
				$pproductoptionrequired = $doc->createElement("Option_Required");
				$pproductoptionrequired->appendChild($doc->createTextNode(implode('; ',$productoptionrequired)));
				$PRODUCT->appendChild($pproductoptionrequired);
				
				///getDiscount
				$discounts=array();
				$productdiscounts = $this->model_catalog_product->getProductDiscounts($product['product_id']);
				foreach($productdiscounts as $pdiscount){
					$discounts[]= $pdiscount['customer_group_id'].'::'.$pdiscount['quantity'].'::'.$pdiscount['priority'].'::'.$pdiscount['price'].'::'.$pdiscount['date_start'].'::'.$pdiscount['date_end'];
				}
				$pproductdiscounts = $doc->createElement("Discounts");
				$pproductdiscounts->appendChild($doc->createTextNode(implode(', ',$discounts)));
				$PRODUCT->appendChild($pproductdiscounts);
				
				//GetSpecial
				$specials=array();
				$productspecials = $this->model_catalog_product->getProductSpecials($product['product_id']);
				foreach($productspecials as $pspecial){
					$specials[]= $pspecial['customer_group_id'].'::'.$pspecial['priority'].'::'.$pspecial['price'].'::'.$pspecial['date_start'].'::'.$pspecial['date_end'];
				}
				$pproductspecials = $doc->createElement("Specials");
				$pproductspecials->appendChild($doc->createTextNode(implode(', ',$specials)));
				$PRODUCT->appendChild($pproductspecials);
				
				//GET Images
				$images=array();
				$productimages = $this->model_catalog_product->getProductImages($product['product_id']);
				foreach($productimages as $pimage){
					
					if($filter_pimage == 'yes'){
						if($pimage['image'] != '' || file_exists(HTTP_CATALOG.'image/'.$pimage['image'])){
							$images[]= HTTP_CATALOG.'image/'.$pimage['image'];
						}else{
							$images= array();
						}
					}else{
						$images[]= $pimage['image'];
					}
				}
				$pimages = $doc->createElement("SubImages");
				$pimages->appendChild($doc->createTextNode(implode(';',$images)));
				$PRODUCT->appendChild($pimages);
				
				$points = $doc->createElement("Points");
				$points->appendChild($doc->createTextNode($product['points']));
				$PRODUCT->appendChild($points);
				
				$viewed = $doc->createElement("Viewed");
				$viewed->appendChild($doc->createTextNode($product['viewed']));
				$PRODUCT->appendChild($viewed);
				
				$r->appendChild($PRODUCT);
			}
			$mask = time();
			$doc->saveXML();
			$doc->save(DIR_EXCEL."product".$mask.".xml");
		}else{
			$objPHPExcel = new PHPExcel();
			$objPHPExcel->setActiveSheetIndex(0);
			$objPHPExcel->getActiveSheet()->setTitle("Product");
			$objPHPExcel->getActiveSheet()->getStyle('S')->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_GENERAL);
			$objPHPExcel->getActiveSheet()
			->getStyle('A1:BB1')
			->applyFromArray(
				array(
					'fill' => array(
						'type' => PHPExcel_Style_Fill::FILL_SOLID,
						'color' => array('rgb' => 'd9d9d9')
					)
				)
			);
			
			$objPHPExcel->setActiveSheetIndex(0);
			$objPHPExcel->getActiveSheet()->setTitle("Product");
			$objPHPExcel->getActiveSheet()->getStyle('S')->getNumberFormat()->setFormatCode(PHPExcel_Style_NumberFormat::FORMAT_GENERAL);
			$i=1;
			$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, 'Product ID')->getColumnDimension('A')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, 'Language')->getColumnDimension('B')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, 'Store')->getColumnDimension('C')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, 'Name')->getColumnDimension('D')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, 'Model')->getColumnDimension('E')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, 'Description')->getColumnDimension('F')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, 'Meta Title')->getColumnDimension('G')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, 'Meta Description')->getColumnDimension('H')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, 'Meta Keyword')->getColumnDimension('I')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, 'Tag')->getColumnDimension('J')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, 'Main Image')->getColumnDimension('K')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, 'SKU')->getColumnDimension('L')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, 'UPC')->getColumnDimension('M')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, 'EAN')->getColumnDimension('N')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, 'JAN')->getColumnDimension('O')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('P'.$i, 'ISBN')->getColumnDimension('P')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, 'MPN')->getColumnDimension('Q')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('R'.$i, 'Location')->getColumnDimension('R')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('S'.$i, 'Price')->getColumnDimension('S')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('T'.$i, 'Tax Class ID')->getColumnDimension('T')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('U'.$i, 'Quantity')->getColumnDimension('U')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('V'.$i, 'Minimum Quantity')->getColumnDimension('V')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('W'.$i, 'Subtract Stock')->getColumnDimension('W')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('X'.$i, 'Stock Status ID')->getColumnDimension('X')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('Y'.$i, 'Shipping')->getColumnDimension('Y')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('Z'.$i, 'SEO')->getColumnDimension('Z')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AA'.$i, 'Date Available')->getColumnDimension('AA')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AB'.$i, 'Length')->getColumnDimension('AB')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AC'.$i, 'Length Class ID')->getColumnDimension('AC')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AD'.$i, 'Width')->getColumnDimension('AD')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AE'.$i, 'Height')->getColumnDimension('AE')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AF'.$i, 'Weight')->getColumnDimension('AF')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AG'.$i, 'Weight Class ID')->getColumnDimension('AG')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AH'.$i, 'Status')->getColumnDimension('AH')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AI'.$i, 'Sort Order')->getColumnDimension('AI')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AJ'.$i, 'Manufacturer ID')->getColumnDimension('AJ')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AK'.$i, 'Category ids')->getColumnDimension('AK')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AL'.$i, 'Filters (Filter Group :: filter Value)')->getColumnDimension('AL')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AM'.$i, 'Download')->getColumnDimension('AM')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AN'.$i, 'Related Products')->getColumnDimension('AN')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AO'.$i, 'Attributes (attribute_Group::attribute::text)')->getColumnDimension('AO')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AP'.$i, 'Options (Option::Type::optionvalue~qty~subtract~priceprefix~price~pointsprefix~points~weightprefix~weight)')->getColumnDimension('AV')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AQ'.$i, 'Options Required (optionname:optiontype=0,optionname:optiontype=1)')->getColumnDimension('AQ')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AR'.$i, 'Discount (Customer Group id::Quantity::Priority::Price::startdate::Enddate)')->getColumnDimension('AR')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AS'.$i, 'Special (Customer_group_id::Priority::Price::startdate::Enddate)')->getColumnDimension('AS')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AT'.$i, 'Sub Images (image1;image2)')->getColumnDimension('AT')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AU'.$i, 'Reward Points')->getColumnDimension('AU')->setAutoSize(true);
			$objPHPExcel->getActiveSheet()->setCellValue('AV'.$i, 'Viewed')->getColumnDimension('AV')->setAutoSize(true);
			
			
			foreach($products as $product){
				$storeinfo = $this->model_setting_store->getStore($product['store_id']);
				if($storeinfo){
					$store = $storeinfo['name'];
				}else{
					$store = 'default';
				}
			
				$i++;
				$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $product['product_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $product['language']);
				$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $store);
				$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, html_entity_decode($product['name']));
				$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $product['model']);
				$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, html_entity_decode($product['description']));
				$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, $product['meta_title']);
				$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, $product['meta_description']);
				$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, $product['meta_keyword']);
				$objPHPExcel->getActiveSheet()->setCellValue('J'.$i, $product['tag']);
				if($filter_pimage == 'yes'){
					if($product['image'] != '' || file_exists(HTTP_CATALOG.'image/'.$product['image'])){
					  $objPHPExcel->getActiveSheet()->setCellValue('K'.$i, HTTP_CATALOG.'image/'.$product['image']);
					}else{
					 $objPHPExcel->getActiveSheet()->setCellValue('K'.$i, '');
					}
				}else{
					$objPHPExcel->getActiveSheet()->setCellValue('K'.$i, $product['image']);
				}
				$objPHPExcel->getActiveSheet()->setCellValue('L'.$i, $product['sku']);
				$objPHPExcel->getActiveSheet()->setCellValue('M'.$i, $product['upc']);
				$objPHPExcel->getActiveSheet()->setCellValue('N'.$i, $product['ean']);
				$objPHPExcel->getActiveSheet()->setCellValue('O'.$i, $product['jan']);
				$objPHPExcel->getActiveSheet()->setCellValue('P'.$i, $product['isbn']);
				$objPHPExcel->getActiveSheet()->setCellValue('Q'.$i, $product['mpn']);
				$objPHPExcel->getActiveSheet()->setCellValue('R'.$i, $product['location']);
				$objPHPExcel->getActiveSheet()->setCellValue('S'.$i, $product['price']);
				$objPHPExcel->getActiveSheet()->setCellValue('T'.$i, $product['tax_class_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('U'.$i, $product['quantity']);
				$objPHPExcel->getActiveSheet()->setCellValue('V'.$i, $product['minimum']);
				$objPHPExcel->getActiveSheet()->setCellValue('W'.$i, $product['subtract']);
				$objPHPExcel->getActiveSheet()->setCellValue('X'.$i, $product['stock_status_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('Y'.$i, $product['shipping']);
				$objPHPExcel->getActiveSheet()->setCellValue('Z'.$i, $this->model_extension_excel_point->getKeyword($product['product_id'],$language_id,$filter_store));
				$objPHPExcel->getActiveSheet()->setCellValue('AA'.$i,  $product['date_available']);
				$objPHPExcel->getActiveSheet()->setCellValue('AB'.$i, $product['length']);
				$objPHPExcel->getActiveSheet()->setCellValue('AC'.$i, $product['length_class_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('AD'.$i, $product['width']);
				$objPHPExcel->getActiveSheet()->setCellValue('AE'.$i, $product['height']);
				$objPHPExcel->getActiveSheet()->setCellValue('AF'.$i, $product['weight']);
				$objPHPExcel->getActiveSheet()->setCellValue('AG'.$i, $product['weight_class_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('AH'.$i, $product['status']);
				$objPHPExcel->getActiveSheet()->setCellValue('AI'.$i, $product['sort_order']);
				$objPHPExcel->getActiveSheet()->setCellValue('AJ'.$i, $product['manufacturer_id']);
				$categories = $this->model_catalog_product->getProductCategories($product['product_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('AK'.$i, (!empty($categories) ? implode(',',$categories) : ''));
				$filterdata=array();
				$this->load->model('catalog/filter');
				$filteres = $this->model_catalog_product->getProductFilters($product['product_id']);
				foreach($filteres as $filter_id){
					$filter_info = $this->model_catalog_filter->getFilter($filter_id);
					if($filter_info){
						$filterdata[] = html_entity_decode(($filter_info['group'] ? $filter_info['group']. ' :: ' . $filter_info['name'] : $filter_info['name']));
					}
				}
				$objPHPExcel->getActiveSheet()->setCellValue('AL'.$i, implode(', ',$filterdata));
				$downloads = $this->model_catalog_product->getProductDownloads($product['product_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('AM'.$i, implode(', ',$downloads));
				$realated = $this->model_catalog_product->getProductRelated($product['product_id']);
				$objPHPExcel->getActiveSheet()->setCellValue('AN'.$i, implode(', ',$realated));
				
				//GetAttribute
				$this->load->model('catalog/attribute');
				$this->load->model('catalog/attribute_group');
				$attributes = $this->model_extension_excel_point->getProductAttributes($product['product_id'],$language_id);
				$objPHPExcel->getActiveSheet()->setCellValue('AO'.$i, implode(', ',$attributes));
				
				//options
				$productoptions = $this->model_extension_excel_point->getProductOptions($product['product_id'],$language_id);
				$objPHPExcel->getActiveSheet()->setCellValue('AP'.$i, implode('; ',$productoptions));
				
				//options required
				$productoptionrequired = $this->model_extension_excel_point->getProductOptionsrequired($product['product_id'],$language_id);
				$objPHPExcel->getActiveSheet()->setCellValue('AQ'.$i, implode('; ',$productoptionrequired));
				
				///getDiscount
				$discounts=array();
				$productdiscounts = $this->model_catalog_product->getProductDiscounts($product['product_id']);
				foreach($productdiscounts as $pdiscount){
					$discounts[]= $pdiscount['customer_group_id'].'::'.$pdiscount['quantity'].'::'.$pdiscount['priority'].'::'.$pdiscount['price'].'::'.$pdiscount['date_start'].'::'.$pdiscount['date_end'];
				}
				$objPHPExcel->getActiveSheet()->setCellValue('AR'.$i, implode(', ',$discounts));
				
				//GetSpecial
				$specials=array();
				$productspecials = $this->model_catalog_product->getProductSpecials($product['product_id']);
				foreach($productspecials as $pspecial){
					$specials[]= $pspecial['customer_group_id'].'::'.$pspecial['priority'].'::'.$pspecial['price'].'::'.$pspecial['date_start'].'::'.$pspecial['date_end'];
				}
				$objPHPExcel->getActiveSheet()->setCellValue('AS'.$i, implode(', ',$specials));
				
				//GET Images
				$images=array();
				$productimages = $this->model_catalog_product->getProductImages($product['product_id']);
				foreach($productimages as $pimage){
					if($filter_pimage == 'yes'){
						if($pimage['image'] != '' || file_exists(HTTP_CATALOG.'image/'.$pimage['image'])){
							$images[]= HTTP_CATALOG.'image/'.$pimage['image'];
						}else{
							$images= array();
						}
					}else{
						$images[]= $pimage['image'];
					}
				}

				$objPHPExcel->getActiveSheet()->setCellValue('AT'.$i, implode(';',$images));
				$objPHPExcel->getActiveSheet()->setCellValue('AU'.$i, $product['points']);
				$objPHPExcel->getActiveSheet()->setCellValue('AV'.$i, $product['viewed']);
			}
			$mask = time();
			if($filter_eformat == 'csv'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'CSV');
				$filename = 'product'.$mask.'.csv';
			}elseif($filter_eformat == 'xls'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
				$filename = 'product'.$mask.'.xls';
			}elseif($filter_eformat == 'xlsx'){
				$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
				$filename = 'product'.$mask.'.xlsx';
			}
			$objWriter->save(DIR_EXCEL . $filename);
		}
		
		if($filter_limit){
			$start = ($page - 1) * $filter_limit;
			$end = $start + $filter_limit;
			if($end < $product_total){
				$json['next'] = str_replace('&amp;', '&', $this->url->link('extension/excel_export/exportproducts', 'user_token=' . $this->session->data['user_token'] . '&page=' . ($page + 1), true));
			} else {
				$json['next'] = '';
				$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filter_eformat='.$filter_eformat.'&filename=product&mask='.$mask.'&batches='.true);
			}
		}else{
			$json['download'] = $this->url->link('extension/excel_export/downloadfile','user_token='.$this->session->data['user_token'].'&filename=product&filter_eformat='.$filter_eformat.'&mask='.$mask.'&batches='.false);
		}
		print_r(json_encode(str_replace('&amp;','&',$json))); 
	}
	
	public function exportproductreview(){
		
		if (isset($this->request->get['filter_start'])) {
			$filter_start = $this->request->get['filter_start'];
		} else {
			$filter_start = 0;
		}
		
		if (isset($this->request->get['filter_status'])) {
			$filter_status = $this->request->get['filter_status'];
		} else {
			$filter_status = null;
		}
		
		if (!empty($this->request->get['filter_limit'])) {
			$filter_limit = $this->request->get['filter_limit'];
		} else {
			$filter_limit = 0;
		}
			
		$filter_data=array(
			'filter_status'   		=> $filter_status,
		    'limit'           		=> $filter_limit,
		    'start'           		=> $filter_start,
		);
		
		$this->load->model('extension/excel_point');
		$objPHPExcel = new PHPExcel();
		$objPHPExcel->setActiveSheetIndex(0);
		$objPHPExcel->getActiveSheet()->setTitle("Product Reviews");
		$i=1;
		$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, 'Review ID')->getColumnDimension('A')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, 'Product ID')->getColumnDimension('B')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, 'Customer ID')->getColumnDimension('C')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, 'Author')->getColumnDimension('D')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, 'Text')->getColumnDimension('E')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, 'Rating')->getColumnDimension('F')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, 'Status')->getColumnDimension('G')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, 'Date Added')->getColumnDimension('H')->setAutoSize(true);
		$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, 'Date Modified')->getColumnDimension('I')->setAutoSize(true);
		
		$productreview = $this->model_extension_excel_point->getproductreview($filter_data);
		foreach($productreview as $productreviews){
			
			$i++;
			$objPHPExcel->getActiveSheet()->setCellValue('A'.$i, $productreviews['review_id']);
			$objPHPExcel->getActiveSheet()->setCellValue('B'.$i, $productreviews['product_id']);
			$objPHPExcel->getActiveSheet()->setCellValue('C'.$i, $productreviews['customer_id']);
			$objPHPExcel->getActiveSheet()->setCellValue('D'.$i, $productreviews['author']);
			$objPHPExcel->getActiveSheet()->setCellValue('E'.$i, $productreviews['text']);
			$objPHPExcel->getActiveSheet()->setCellValue('F'.$i, $productreviews['rating']);
			$objPHPExcel->getActiveSheet()->setCellValue('G'.$i, $productreviews['status']);
			$objPHPExcel->getActiveSheet()->setCellValue('H'.$i, $productreviews['date_added']);
			$objPHPExcel->getActiveSheet()->setCellValue('I'.$i, $productreviews['date_modified']);
		}
		
		
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
		$filename = 'ProductReview'.time().'.xls';
		header('Content-Type: application/vnd.ms-excel'); 
		header('Content-Disposition: attachment;filename='.$filename); 
		header('Cache-Control: max-age=0'); 
		$objWriter->save('php://output'); 
		exit();
	}
	
	
	/*NEW 06-02-2017*/
	public function create_zip($files = array(),$destination = '',$overwrite = false){
		//if the zip file already exists and overwrite is false, return false
		if(file_exists($destination) && !$overwrite) { return false; }
	
		//vars
		$valid_files = array();
		//if files were passed in...
		if(is_array($files)) {
			//cycle through each file
			foreach($files as $file) {
				//make sure the file exists
				if(file_exists($file)) {
					$valid_files[] = $file;
				}
			}
		}
		
		
		//if we have good files...
		if(count($valid_files)) {
			//create the archive
			$zip = new ZipArchive();
			if($zip->open($destination,$overwrite ? ZIPARCHIVE::OVERWRITE : ZIPARCHIVE::CREATE) !== true) {
				return false;
			}
			//add the files
			foreach($valid_files as $file){
				$zip->addFile($file,basename($file));
			}
			
			$zip->close();
			
			header('Content-Type: application/zip'); 
			header('Content-Disposition: attachment;filename='.basename($destination)); 
			header('Cache-Control: max-age=0'); 
			header('Expires: 0');
			header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
			header('Pragma: public');
			header('Content-Length: ' . filesize($destination));
			readfile($destination, 'rb');
			unlink($destination);
			foreach($valid_files as $file){
				unlink($file);
			}
			exit();
		}
		else
		{
			return false;
		}
	}
	
	public function downloadfile(){
		if($this->request->get['batches']){
			$nom_zip = DIR_EXCEL.$this->request->get['filename'].'-'.time().'.zip';
			$files = array_diff(scandir(DIR_EXCEL), array('..', '.'));
			foreach($files as $file){
			 $tmpfile[]=DIR_EXCEL.$file;
			}
			
			$this->create_zip($tmpfile,$nom_zip);
		}else{
			$file = DIR_EXCEL . $this->request->get['filename'].$this->request->get['mask'].'.'.$this->request->get['filter_eformat'];
			if(is_file($file)){
				if($this->request->get['filter_eformat'] != 'xml'){
						header('Content-Type: application/vnd.ms-excel'); 
						header('Content-Disposition: attachment;filename='.basename($file)); 
						header('Cache-Control: max-age=0'); 
						header('Expires: 0');
						header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
						header('Pragma: public');
						header('Content-Length: ' . filesize($file));
						readfile($file, 'rb');
						unlink($file);
						exit();
				}else{
					header('Content-type: text/xml');
					header('Content-Disposition: attachment; filename='.basename($file));
					header('Expires: 0');
					header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
					header('Pragma: public');
					header('Content-Length: ' . filesize($file));
					readfile($file, 'rb');
					unlink($file);
					exit();
				}
			}
		}
	}
	
	
	protected function validatecustomerForm(){
		if (!$this->user->hasPermission('modify', 'extension/customer_import')) {
			$this->error['warning'] = $this->language->get('error_permission');
		}
		
		if(empty($this->request->post['emailaction'])){
			$this->error['warning'] = $this->language->get('error_email_action');	
		}
		
		if(empty($this->request->post['password_format'])){
			$this->error['warning'] = $this->language->get('error_password_format');
		}
		
		return !$this->error;
	}
}