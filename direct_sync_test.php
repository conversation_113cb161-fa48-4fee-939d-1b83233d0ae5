<?php
/**
 * Direct sync test - bypasses OpenCart web initialization
 * This directly calls the sync functionality like the cron script does
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set content type
header('Content-Type: text/html; charset=utf-8');

echo "<h2>Direct Sync Test</h2>";
echo "<pre>";

function testLog($message) {
    $timestamp = date('Y-m-d H:i:s');
    echo "[$timestamp] $message<br>\n";
    flush();
}

try {
    testLog("=== Starting Direct Sync Test ===");
    
    // Define VERSION constant
    if (!defined('VERSION')) {
        define('VERSION', '*******');
    }
    
    // Get script directory
    $script_dir = dirname(__FILE__);
    
    // Include admin config
    if (!is_file($script_dir . '/admin/config.php')) {
        throw new Exception("Could not find admin/config.php");
    }
    
    require_once($script_dir . '/admin/config.php');
    testLog("Config loaded");
    
    // Include startup
    require_once(DIR_SYSTEM . 'startup.php');
    testLog("Startup loaded");
    
    // Initialize registry manually (like the cron script does)
    $registry = new Registry();
    
    // Config
    $config = new Config();
    $config->load('default');
    $config->load('admin');
    $registry->set('config', $config);
    testLog("Config initialized");
    
    // Log
    $log = new Log($config->get('error_filename') ?: 'error.log');
    $registry->set('log', $log);
    
    // Set timezone
    if ($config->get('date_timezone')) {
        date_default_timezone_set($config->get('date_timezone'));
    } else {
        date_default_timezone_set('UTC');
    }
    
    // Database
    $db = new DB(
        $config->get('db_engine') ?: DB_DRIVER,
        $config->get('db_hostname') ?: DB_HOSTNAME,
        $config->get('db_username') ?: DB_USERNAME,
        $config->get('db_password') ?: DB_PASSWORD,
        $config->get('db_database') ?: DB_DATABASE,
        $config->get('db_port') ?: DB_PORT
    );
    $registry->set('db', $db);
    testLog("Database connected");
    
    // Sync PHP and DB time zones
    $db->query("SET time_zone = '" . $db->escape(date('P')) . "'");
    
    // Session (minimal setup)
    $session = new Session($config->get('session_engine') ?: 'file', $registry);
    $registry->set('session', $session);
    
    // Cache
    $cache = new Cache($config->get('cache_engine') ?: 'file', $config->get('cache_expire') ?: 3600);
    $registry->set('cache', $cache);
    
    // Event
    $event = new Event($registry);
    $registry->set('event', $event);
    
    // Loader
    $loader = new Loader($registry);
    $registry->set('load', $loader);
    
    testLog("All components initialized");
    
    // Check module status
    $module_status_query = $db->query("SELECT value FROM " . DB_PREFIX . "setting WHERE `key` = 'module_opc_advqb_status'");
    if ($module_status_query->num_rows > 0) {
        $module_status = (bool)$module_status_query->row['value'];
        testLog("Module status: " . ($module_status ? "enabled" : "disabled"));
    } else {
        testLog("Module status not found in database");
        $module_status = false;
    }
    
    // Check linked products
    $linked_products_query = $db->query("SELECT COUNT(*) as count FROM " . DB_PREFIX . "advqb_product");
    $linked_count = $linked_products_query->row['count'];
    testLog("Found $linked_count linked products");
    
    if ($linked_count == 0) {
        testLog("WARNING: No linked products found. Sync may not work.");
    }
    
    // Initialize AdvQB library
    $registry->set('advqb', new Advqb($registry));
    $advqb = $registry->get('advqb');
    testLog("AdvQB library loaded");
    
    // Check if sync method exists
    if (!method_exists($advqb, 'syncQuantityFromAdvQB')) {
        throw new Exception("syncQuantityFromAdvQB method not found");
    }
    testLog("syncQuantityFromAdvQB method found");
    
    // Perform the sync
    testLog("Starting quantity sync...");
    $count = $advqb->syncQuantityFromAdvQB();
    testLog("Sync completed. Products updated: $count");
    
    if ($count > 0) {
        testLog("SUCCESS: $count products were synced successfully!");
    } else {
        testLog("INFO: No products were updated. This could mean:");
        testLog("  - No quantity changes were detected");
        testLog("  - QuickBooks authentication issues");
        testLog("  - Network connectivity issues");
    }
    
    testLog("=== Direct Sync Test Completed ===");
    
} catch (Exception $e) {
    testLog("ERROR: " . $e->getMessage());
    testLog("File: " . $e->getFile() . " Line: " . $e->getLine());
} catch (Error $e) {
    testLog("FATAL ERROR: " . $e->getMessage());
    testLog("File: " . $e->getFile() . " Line: " . $e->getLine());
}

echo "</pre>";
echo "<p><strong>Test completed. Check the output above for results.</strong></p>";
echo "<p>If you see 'SUCCESS' or 'INFO: No products were updated', the sync functionality is working correctly.</p>";
?>
